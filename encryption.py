
import base64
import hashlib
from Crypto import Random
from Crypto.Cipher import AES
import string, random

BS = 16
pad = lambda s: s + (BS - len(s) % BS) * chr(BS - len(s) % BS).encode()
unpad = lambda s: s[:-ord(s[len(s)-1:])]

def iv():
    return chr(0) * 16

class AES256(object):
    def __init__(self, key):
        self.key = key
        self.key = hashlib.sha256(key.encode()).digest()
    
    def key_generator(self):
        # generate key for AES-256 encryption
        return ''.join(random.choice('abcdefghijklmnopqrstuvwxyz123456789ABCDEFGHIJKLKMPQRSTUVXYZ') for _ in range(32))

    def encrypt(self, message):
        message = message.encode()
        raw = pad(message)
        cipher = AES.new(self.key, AES.MODE_CBC, iv())
        enc = cipher.encrypt(raw)
        return base64.b64encode(enc).decode('utf-8')

    def decrypt(self, enc):
        enc = base64.b64decode(enc)
        cipher = AES.new(self.key, AES.MODE_CBC, iv())
        dec = cipher.decrypt(enc)
        return unpad(dec).decode('utf-8')

class Encryption():
    def encode(value):
        string_bytes = value.encode("ascii")
        base64_bytes = base64.b64encode(string_bytes)
        base64_string = base64_bytes.decode("ascii")
        return base64_string

    def decode(value):
        base64_bytes = value.encode("ascii")
        string_bytes = base64.b64decode(base64_bytes)
        string = string_bytes.decode("ascii")
        return string


## how to use:
#
# key = AESCipher('').key_generator()

# message = 'Test English...'

# enc = AES256(key).encrypt(message)
# dec = AES256(key).decrypt(enc)

# print(key)
# print(enc)
# print(dec)
