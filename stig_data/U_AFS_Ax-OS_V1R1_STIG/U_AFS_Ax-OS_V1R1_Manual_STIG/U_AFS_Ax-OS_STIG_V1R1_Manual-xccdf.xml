<?xml version="1.0" encoding="utf-8"?><?xml-stylesheet type='text/xsl' href='STIG_unclass.xsl'?><Benchmark xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:cpe="http://cpe.mitre.org/language/2.0" xmlns:xhtml="http://www.w3.org/1999/xhtml" xmlns:dsig="http://www.w3.org/2000/09/xmldsig#" xsi:schemaLocation="http://checklists.nist.gov/xccdf/1.1 http://nvd.nist.gov/schema/xccdf-1.1.4.xsd http://cpe.mitre.org/dictionary/2.0 http://cpe.mitre.org/files/cpe-dictionary_2.1.xsd" id="AFS_Ax-OS_STIG" xml:lang="en" xmlns="http://checklists.nist.gov/xccdf/1.1"><status date="2025-07-22">accepted</status><title>Axonius Federal Systems Ax-OS Security Technical Implementation Guide</title><description>This Security Technical Implementation Guide is published as a tool to improve the security of Department of Defense (DOD) information systems. The requirements are derived from the National Institute of Standards and Technology (NIST) 800-53 and related documents. Comments or proposed revisions to this document should be sent via email to the following address: <EMAIL>.</description><notice id="terms-of-use" xml:lang="en"></notice><front-matter xml:lang="en"></front-matter><rear-matter xml:lang="en"></rear-matter><reference href="https://cyber.mil"><dc:publisher>DISA</dc:publisher><dc:source>STIG.DOD.MIL</dc:source></reference><plain-text id="release-info">Release: 1 Benchmark Date: 14 Jul 2025</plain-text><plain-text id="generator">3.5.1</plain-text><plain-text id="conventionsVersion">1.10.0</plain-text><version>1</version><Profile id="MAC-1_Classified"><title>I - Mission Critical Classified</title><description>&lt;ProfileDescription&gt;&lt;/ProfileDescription&gt;</description><select idref="V-276001" selected="true" /><select idref="V-276002" selected="true" /><select idref="V-276003" selected="true" /><select idref="V-276004" selected="true" /><select idref="V-276005" selected="true" /><select idref="V-276006" selected="true" /><select idref="V-276007" selected="true" /><select idref="V-276008" selected="true" /><select idref="V-276009" selected="true" /><select idref="V-276010" selected="true" /><select idref="V-276011" selected="true" /><select idref="V-276012" selected="true" /><select idref="V-276013" selected="true" /><select idref="V-276014" selected="true" /><select idref="V-276015" selected="true" /><select idref="V-276016" selected="true" /></Profile><Profile id="MAC-1_Public"><title>I - Mission Critical Public</title><description>&lt;ProfileDescription&gt;&lt;/ProfileDescription&gt;</description><select idref="V-276001" selected="true" /><select idref="V-276002" selected="true" /><select idref="V-276003" selected="true" /><select idref="V-276004" selected="true" /><select idref="V-276005" selected="true" /><select idref="V-276006" selected="true" /><select idref="V-276007" selected="true" /><select idref="V-276008" selected="true" /><select idref="V-276009" selected="true" /><select idref="V-276010" selected="true" /><select idref="V-276011" selected="true" /><select idref="V-276012" selected="true" /><select idref="V-276013" selected="true" /><select idref="V-276014" selected="true" /><select idref="V-276015" selected="true" /><select idref="V-276016" selected="true" /></Profile><Profile id="MAC-1_Sensitive"><title>I - Mission Critical Sensitive</title><description>&lt;ProfileDescription&gt;&lt;/ProfileDescription&gt;</description><select idref="V-276001" selected="true" /><select idref="V-276002" selected="true" /><select idref="V-276003" selected="true" /><select idref="V-276004" selected="true" /><select idref="V-276005" selected="true" /><select idref="V-276006" selected="true" /><select idref="V-276007" selected="true" /><select idref="V-276008" selected="true" /><select idref="V-276009" selected="true" /><select idref="V-276010" selected="true" /><select idref="V-276011" selected="true" /><select idref="V-276012" selected="true" /><select idref="V-276013" selected="true" /><select idref="V-276014" selected="true" /><select idref="V-276015" selected="true" /><select idref="V-276016" selected="true" /></Profile><Profile id="MAC-2_Classified"><title>II - Mission Support Classified</title><description>&lt;ProfileDescription&gt;&lt;/ProfileDescription&gt;</description><select idref="V-276001" selected="true" /><select idref="V-276002" selected="true" /><select idref="V-276003" selected="true" /><select idref="V-276004" selected="true" /><select idref="V-276005" selected="true" /><select idref="V-276006" selected="true" /><select idref="V-276007" selected="true" /><select idref="V-276008" selected="true" /><select idref="V-276009" selected="true" /><select idref="V-276010" selected="true" /><select idref="V-276011" selected="true" /><select idref="V-276012" selected="true" /><select idref="V-276013" selected="true" /><select idref="V-276014" selected="true" /><select idref="V-276015" selected="true" /><select idref="V-276016" selected="true" /></Profile><Profile id="MAC-2_Public"><title>II - Mission Support Public</title><description>&lt;ProfileDescription&gt;&lt;/ProfileDescription&gt;</description><select idref="V-276001" selected="true" /><select idref="V-276002" selected="true" /><select idref="V-276003" selected="true" /><select idref="V-276004" selected="true" /><select idref="V-276005" selected="true" /><select idref="V-276006" selected="true" /><select idref="V-276007" selected="true" /><select idref="V-276008" selected="true" /><select idref="V-276009" selected="true" /><select idref="V-276010" selected="true" /><select idref="V-276011" selected="true" /><select idref="V-276012" selected="true" /><select idref="V-276013" selected="true" /><select idref="V-276014" selected="true" /><select idref="V-276015" selected="true" /><select idref="V-276016" selected="true" /></Profile><Profile id="MAC-2_Sensitive"><title>II - Mission Support Sensitive</title><description>&lt;ProfileDescription&gt;&lt;/ProfileDescription&gt;</description><select idref="V-276001" selected="true" /><select idref="V-276002" selected="true" /><select idref="V-276003" selected="true" /><select idref="V-276004" selected="true" /><select idref="V-276005" selected="true" /><select idref="V-276006" selected="true" /><select idref="V-276007" selected="true" /><select idref="V-276008" selected="true" /><select idref="V-276009" selected="true" /><select idref="V-276010" selected="true" /><select idref="V-276011" selected="true" /><select idref="V-276012" selected="true" /><select idref="V-276013" selected="true" /><select idref="V-276014" selected="true" /><select idref="V-276015" selected="true" /><select idref="V-276016" selected="true" /></Profile><Profile id="MAC-3_Classified"><title>III - Administrative Classified</title><description>&lt;ProfileDescription&gt;&lt;/ProfileDescription&gt;</description><select idref="V-276001" selected="true" /><select idref="V-276002" selected="true" /><select idref="V-276003" selected="true" /><select idref="V-276004" selected="true" /><select idref="V-276005" selected="true" /><select idref="V-276006" selected="true" /><select idref="V-276007" selected="true" /><select idref="V-276008" selected="true" /><select idref="V-276009" selected="true" /><select idref="V-276010" selected="true" /><select idref="V-276011" selected="true" /><select idref="V-276012" selected="true" /><select idref="V-276013" selected="true" /><select idref="V-276014" selected="true" /><select idref="V-276015" selected="true" /><select idref="V-276016" selected="true" /></Profile><Profile id="MAC-3_Public"><title>III - Administrative Public</title><description>&lt;ProfileDescription&gt;&lt;/ProfileDescription&gt;</description><select idref="V-276001" selected="true" /><select idref="V-276002" selected="true" /><select idref="V-276003" selected="true" /><select idref="V-276004" selected="true" /><select idref="V-276005" selected="true" /><select idref="V-276006" selected="true" /><select idref="V-276007" selected="true" /><select idref="V-276008" selected="true" /><select idref="V-276009" selected="true" /><select idref="V-276010" selected="true" /><select idref="V-276011" selected="true" /><select idref="V-276012" selected="true" /><select idref="V-276013" selected="true" /><select idref="V-276014" selected="true" /><select idref="V-276015" selected="true" /><select idref="V-276016" selected="true" /></Profile><Profile id="MAC-3_Sensitive"><title>III - Administrative Sensitive</title><description>&lt;ProfileDescription&gt;&lt;/ProfileDescription&gt;</description><select idref="V-276001" selected="true" /><select idref="V-276002" selected="true" /><select idref="V-276003" selected="true" /><select idref="V-276004" selected="true" /><select idref="V-276005" selected="true" /><select idref="V-276006" selected="true" /><select idref="V-276007" selected="true" /><select idref="V-276008" selected="true" /><select idref="V-276009" selected="true" /><select idref="V-276010" selected="true" /><select idref="V-276011" selected="true" /><select idref="V-276012" selected="true" /><select idref="V-276013" selected="true" /><select idref="V-276014" selected="true" /><select idref="V-276015" selected="true" /><select idref="V-276016" selected="true" /></Profile><Group id="V-276001"><title>SRG-APP-000001</title><description>&lt;GroupDescription&gt;&lt;/GroupDescription&gt;</description><Rule id="SV-276001r1122653_rule" weight="10.0" severity="medium"><version>AXOS-00-000005</version><title>Ax-OS must limit the number of concurrent sessions to 10 for all accounts and/or account types.</title><description>&lt;VulnDiscussion&gt;Operating system management includes the ability to control the number of users and user sessions that utilize an operating system. Limiting the number of allowed users and sessions per user is helpful in reducing the risks related to denial-of-service (DoS) attacks.

Satisfies: SRG-APP-000001, SRG-APP-000246, SRG-APP-000247, SRG-APP-000435&lt;/VulnDiscussion&gt;&lt;FalsePositives&gt;&lt;/FalsePositives&gt;&lt;FalseNegatives&gt;&lt;/FalseNegatives&gt;&lt;Documentable&gt;false&lt;/Documentable&gt;&lt;Mitigations&gt;&lt;/Mitigations&gt;&lt;SeverityOverrideGuidance&gt;&lt;/SeverityOverrideGuidance&gt;&lt;PotentialImpacts&gt;&lt;/PotentialImpacts&gt;&lt;ThirdPartyTools&gt;&lt;/ThirdPartyTools&gt;&lt;MitigationControl&gt;&lt;/MitigationControl&gt;&lt;Responsibility&gt;&lt;/Responsibility&gt;&lt;IAControls&gt;&lt;/IAControls&gt;</description><reference><dc:title>DPMS Target Axonius Federal Systems Ax-OS</dc:title><dc:publisher>DISA</dc:publisher><dc:type>DPMS Target</dc:type><dc:subject>Axonius Federal Systems Ax-OS</dc:subject><dc:identifier>5710</dc:identifier></reference><ident system="http://cyber.mil/cci">CCI-000054</ident><ident system="http://cyber.mil/cci">CCI-001094</ident><ident system="http://cyber.mil/cci">CCI-001095</ident><ident system="http://cyber.mil/cci">CCI-002385</ident><fixtext fixref="F-80044r1122652_fix">From the Axonius Toolbox (accessed via SSH) Main Actions Menu, select the following options:

Compliance Actions &gt;&gt; Advanced Compliance Actions &gt;&gt; Maximum Concurrent Logins &gt;&gt; Enable</fixtext><fix id="F-80044r1122652_fix" /><check system="C-80139r1122651_chk"><check-content-ref href="AFS_Ax-OS_STIG.xml" name="M" /><check-content>From the Axonius Toolbox (accessed via Secure Shell [SSH]) Main Actions Menu, select the following options:

Compliance Actions &gt;&gt; Advanced Compliance Actions &gt;&gt; Maximum Concurrent Logins

If "Current Status: Disable" is shown, this is a finding.</check-content></check></Rule></Group><Group id="V-276002"><title>SRG-APP-000003</title><description>&lt;GroupDescription&gt;&lt;/GroupDescription&gt;</description><Rule id="SV-276002r1122656_rule" weight="10.0" severity="medium"><version>AXOS-00-000010</version><title>Ax-OS must automatically terminate a graphical user interface (GUI) user session after 15 minutes.</title><description>&lt;VulnDiscussion&gt;An attacker can take advantage of user sessions that are left open, thus bypassing the user authentication process.

To thwart the vulnerability of open and unused user sessions, the application server must be configured to close the sessions when a configured condition or trigger event is met.

Session termination ends all processes associated with a user's logical session except those specifically created by the user (i.e., session owner) to continue after the session is terminated.

Conditions or trigger events requiring automatic session termination can include, for example, periods of user inactivity, targeted responses to certain types of incidents, and time-of-day restrictions on information system use.

Satisfies: SRG-APP-000003, SRG-APP-000190, SRG-APP-000295&lt;/VulnDiscussion&gt;&lt;FalsePositives&gt;&lt;/FalsePositives&gt;&lt;FalseNegatives&gt;&lt;/FalseNegatives&gt;&lt;Documentable&gt;false&lt;/Documentable&gt;&lt;Mitigations&gt;&lt;/Mitigations&gt;&lt;SeverityOverrideGuidance&gt;&lt;/SeverityOverrideGuidance&gt;&lt;PotentialImpacts&gt;&lt;/PotentialImpacts&gt;&lt;ThirdPartyTools&gt;&lt;/ThirdPartyTools&gt;&lt;MitigationControl&gt;&lt;/MitigationControl&gt;&lt;Responsibility&gt;&lt;/Responsibility&gt;&lt;IAControls&gt;&lt;/IAControls&gt;</description><reference><dc:title>DPMS Target Axonius Federal Systems Ax-OS</dc:title><dc:publisher>DISA</dc:publisher><dc:type>DPMS Target</dc:type><dc:subject>Axonius Federal Systems Ax-OS</dc:subject><dc:identifier>5710</dc:identifier></reference><ident system="http://cyber.mil/cci">CCI-000057</ident><ident system="http://cyber.mil/cci">CCI-001133</ident><ident system="http://cyber.mil/cci">CCI-002361</ident><fixtext fixref="F-80045r1122655_fix">Select the gear icon (System Settings) &gt;&gt; Privacy and Security &gt;&gt; Session.

Under the Session Menu, enable the "Enable session timeout" slide bar.

Set "Session idle timeout (minutes)" to "15".

Click "Save".</fixtext><fix id="F-80045r1122655_fix" /><check system="C-80140r1122654_chk"><check-content-ref href="AFS_Ax-OS_STIG.xml" name="M" /><check-content>Select the gear icon (System Settings) &gt;&gt; Privacy and Security &gt;&gt; Session.

Under the Session Menu, verify the "Enable session timeout" slide bar is enabled.

Verify "Session idle timeout (minutes)" is set to "15".

If "Session idle timeout (minutes)" is not set to 15 minutes or less, this is a finding.</check-content></check></Rule></Group><Group id="V-276003"><title>SRG-APP-000003</title><description>&lt;GroupDescription&gt;&lt;/GroupDescription&gt;</description><Rule id="SV-276003r1122659_rule" weight="10.0" severity="medium"><version>AXOS-00-000015</version><title>Ax-OS must automatically terminate a Secure Shell (SSH) user session after 15 minutes.</title><description>&lt;VulnDiscussion&gt;An attacker can take advantage of user sessions that are left open, thus bypassing the user authentication process.

To thwart the vulnerability of open and unused user sessions, the application server must be configured to close the sessions when a configured condition or trigger event is met.

Session termination ends all processes associated with a user's logical session except those specifically created by the user (i.e., session owner) to continue after the session is terminated.

Conditions or trigger events requiring automatic session termination can include, for example, periods of user inactivity, targeted responses to certain types of incidents, and time-of-day restrictions on information system use.&lt;/VulnDiscussion&gt;&lt;FalsePositives&gt;&lt;/FalsePositives&gt;&lt;FalseNegatives&gt;&lt;/FalseNegatives&gt;&lt;Documentable&gt;false&lt;/Documentable&gt;&lt;Mitigations&gt;&lt;/Mitigations&gt;&lt;SeverityOverrideGuidance&gt;&lt;/SeverityOverrideGuidance&gt;&lt;PotentialImpacts&gt;&lt;/PotentialImpacts&gt;&lt;ThirdPartyTools&gt;&lt;/ThirdPartyTools&gt;&lt;MitigationControl&gt;&lt;/MitigationControl&gt;&lt;Responsibility&gt;&lt;/Responsibility&gt;&lt;IAControls&gt;&lt;/IAControls&gt;</description><reference><dc:title>DPMS Target Axonius Federal Systems Ax-OS</dc:title><dc:publisher>DISA</dc:publisher><dc:type>DPMS Target</dc:type><dc:subject>Axonius Federal Systems Ax-OS</dc:subject><dc:identifier>5710</dc:identifier></reference><ident system="http://cyber.mil/cci">CCI-000057</ident><fixtext fixref="F-80046r1122658_fix">From the Axonius Toolbox (accessed via SSH) Main Actions Menu, select the following options:

Compliance Actions &gt;&gt; Advanced Compliance Actions &gt;&gt; Idle session timeout

Enable "Idle session timeout".</fixtext><fix id="F-80046r1122658_fix" /><check system="C-80141r1122657_chk"><check-content-ref href="AFS_Ax-OS_STIG.xml" name="M" /><check-content>From the Axonius Toolbox (accessed via SSH) Main Actions Menu, select the following options:

Compliance Actions &gt;&gt; Advanced Compliance Actions &gt;&gt; Idle session timeout

If "Idle session timeout" is not enabled, this is a finding.</check-content></check></Rule></Group><Group id="V-276004"><title>SRG-APP-000014</title><description>&lt;GroupDescription&gt;&lt;/GroupDescription&gt;</description><Rule id="SV-276004r1122662_rule" weight="10.0" severity="high"><version>AXOS-00-000020</version><title>Ax-OS must implement DOD-approved encryption to protect the confidentiality of remote access sessions.</title><description>&lt;VulnDiscussion&gt;Without confidentiality protection mechanisms, unauthorized individuals may gain access to sensitive information via a remote access session.

Remote access is access to DOD nonpublic information systems by an authorized user (or information system) communicating through an external, nonorganization-controlled network. Remote access methods include, for example, dial-up, broadband, and wireless.

Encryption provides a means to secure the remote connection to prevent unauthorized access to data traversing the remote access connection, thereby providing a degree of confidentiality. The encryption strength of the mechanism is selected based on the security categorization of the information.&lt;/VulnDiscussion&gt;&lt;FalsePositives&gt;&lt;/FalsePositives&gt;&lt;FalseNegatives&gt;&lt;/FalseNegatives&gt;&lt;Documentable&gt;false&lt;/Documentable&gt;&lt;Mitigations&gt;&lt;/Mitigations&gt;&lt;SeverityOverrideGuidance&gt;&lt;/SeverityOverrideGuidance&gt;&lt;PotentialImpacts&gt;&lt;/PotentialImpacts&gt;&lt;ThirdPartyTools&gt;&lt;/ThirdPartyTools&gt;&lt;MitigationControl&gt;&lt;/MitigationControl&gt;&lt;Responsibility&gt;&lt;/Responsibility&gt;&lt;IAControls&gt;&lt;/IAControls&gt;</description><reference><dc:title>DPMS Target Axonius Federal Systems Ax-OS</dc:title><dc:publisher>DISA</dc:publisher><dc:type>DPMS Target</dc:type><dc:subject>Axonius Federal Systems Ax-OS</dc:subject><dc:identifier>5710</dc:identifier></reference><ident system="http://cyber.mil/cci">CCI-000068</ident><fixtext fixref="F-80047r1122661_fix">From the Axonius Toolbox (accessed via SSH) Main Actions Menu, select the following options:

System Actions &gt;&gt; Advanced System Actions &gt;&gt; Enable FIPS Mode 

If "Disable FIPS Mode" is displayed, no action is required.</fixtext><fix id="F-80047r1122661_fix" /><check system="C-80142r1122660_chk"><check-content-ref href="AFS_Ax-OS_STIG.xml" name="M" /><check-content>From the Axonius Toolbox (accessed via Secure Shell [SSH]) Main Actions Menu, select the following options:

System Actions &gt;&gt; Advanced System Actions

If "Enable FIPS Mode" is present, this is a finding.</check-content></check></Rule></Group><Group id="V-276005"><title>SRG-APP-000033</title><description>&lt;GroupDescription&gt;&lt;/GroupDescription&gt;</description><Rule id="SV-276005r1122665_rule" weight="10.0" severity="medium"><version>AXOS-00-000025</version><title>Ax-OS must enforce approved authorizations for logical access to information and system resources in accordance with applicable access control policies.</title><description>&lt;VulnDiscussion&gt;Strong access controls are critical to securing the application server. The application server must employ access control policies (e.g., identity-based, role-based, and attribute-based policies) and access enforcement mechanisms (e.g., access control lists, access control matrices, and cryptography) to control access between users (or processes acting on behalf of users) and objects (e.g., applications, files, records, processes, and application domains) in the application server.

Without stringent logical access and authorization controls, an adversary may have the ability, with little effort, to compromise the application server and associated supporting infrastructure.

Satisfies: SRG-APP-000033, SRG-APP-000158, SRG-APP-000211, SRG-APP-000233, SRG-APP-000340, SRG-APP-000342, SRG-APP-000328, SRG-APP-000380, SRG-APP-000386, SRG-APP-000472, SRG-APP-000473, SRG-APP-000715, SRG-APP-000720, SRG-APP-000725, SRG-APP-000730, SRG-APP-000735&lt;/VulnDiscussion&gt;&lt;FalsePositives&gt;&lt;/FalsePositives&gt;&lt;FalseNegatives&gt;&lt;/FalseNegatives&gt;&lt;Documentable&gt;false&lt;/Documentable&gt;&lt;Mitigations&gt;&lt;/Mitigations&gt;&lt;SeverityOverrideGuidance&gt;&lt;/SeverityOverrideGuidance&gt;&lt;PotentialImpacts&gt;&lt;/PotentialImpacts&gt;&lt;ThirdPartyTools&gt;&lt;/ThirdPartyTools&gt;&lt;MitigationControl&gt;&lt;/MitigationControl&gt;&lt;Responsibility&gt;&lt;/Responsibility&gt;&lt;IAControls&gt;&lt;/IAControls&gt;</description><reference><dc:title>DPMS Target Axonius Federal Systems Ax-OS</dc:title><dc:publisher>DISA</dc:publisher><dc:type>DPMS Target</dc:type><dc:subject>Axonius Federal Systems Ax-OS</dc:subject><dc:identifier>5710</dc:identifier></reference><ident system="http://cyber.mil/cci">CCI-000213</ident><ident system="http://cyber.mil/cci">CCI-000778</ident><ident system="http://cyber.mil/cci">CCI-001082</ident><ident system="http://cyber.mil/cci">CCI-001084</ident><ident system="http://cyber.mil/cci">CCI-002235</ident><ident system="http://cyber.mil/cci">CCI-002233</ident><ident system="http://cyber.mil/cci">CCI-002165</ident><ident system="http://cyber.mil/cci">CCI-001813</ident><ident system="http://cyber.mil/cci">CCI-001774</ident><ident system="http://cyber.mil/cci">CCI-002696</ident><ident system="http://cyber.mil/cci">CCI-002699</ident><ident system="http://cyber.mil/cci">CCI-003638</ident><ident system="http://cyber.mil/cci">CCI-003639</ident><ident system="http://cyber.mil/cci">CCI-003640</ident><ident system="http://cyber.mil/cci">CCI-003641</ident><ident system="http://cyber.mil/cci">CCI-003642</ident><fixtext fixref="F-80048r1122664_fix">Role-Based Access Control hierarchy is to be defined by the AO. Separation of duties must be configured.

Select the gear icon (System Settings) &gt;&gt; Access Management &gt;&gt; LDAP &amp; SAML.

Depending on the multifactor type configured, under LDAP or SAML, locate "User Assignment Settings".

Assign two or more roles as defined by the AO and tie them to an LDAP/SAML user or group.</fixtext><fix id="F-80048r1122664_fix" /><check system="C-80143r1122663_chk"><check-content-ref href="AFS_Ax-OS_STIG.xml" name="M" /><check-content>Role-Based Access Control hierarchy is to be defined by the authorizing official (AO). Separation of duties must be configured.

Select the gear icon (System Settings) &gt;&gt; Access Management &gt;&gt; LDAP &amp; SAML.

Depending on the multifactor type configured, under LDAP or SAML, locate "User Assignment Settings".

If only one assigned role exists, this is a finding.</check-content></check></Rule></Group><Group id="V-276006"><title>SRG-APP-000070</title><description>&lt;GroupDescription&gt;&lt;/GroupDescription&gt;</description><Rule id="SV-276006r1122668_rule" weight="10.0" severity="medium"><version>AXOS-00-000030</version><title>Ax-OS must display the Standard Mandatory DOD Notice and Consent Banner before granting access to Ax-OS.</title><description>&lt;VulnDiscussion&gt;Display of a standardized and approved use notification before granting access to the publicly accessible application ensures privacy and security notification verbiage used is consistent with applicable federal laws, Executive Orders, directives, policies, regulations, standards, and guidance.

System use notifications are required only for access via logon interfaces with human users and are not required when such human interfaces do not exist.

The banner must be formatted in accordance with DTM-08-060. Use the following verbiage for desktops, laptops, and other devices accommodating banners of 1300 characters:

"You are accessing a U.S. Government (USG) Information System (IS) that is provided for USG-authorized use only.

By using this IS (which includes any device attached to this IS), you consent to the following conditions:

-The USG routinely intercepts and monitors communications on this IS for purposes including, but not limited to, penetration testing, COMSEC monitoring, network operations and defense, personnel misconduct (PM), law enforcement (LE), and counterintelligence (CI) investigations.

-At any time, the USG may inspect and seize data stored on this IS.

-Communications using, or data stored on, this IS are not private, are subject to routine monitoring, interception, and search, and may be disclosed or used for any USG-authorized purpose.

-This IS includes security measures (e.g., authentication and access controls) to protect USG interests--not for your personal benefit or privacy.

-Notwithstanding the above, using this IS does not constitute consent to PM, LE or CI investigative searching or monitoring of the content of privileged communications, or work product, related to personal representation or services by attorneys, psychotherapists, or clergy, and their assistants. Such communications and work product are private and confidential. See User Agreement for details."
 
Use the following verbiage for operating systems that have severe limitations on the number of characters that can be displayed in the banner:

"I've read &amp; consent to terms in IS user agreem't."

Satisfies: SRG-APP-000070, SRG-APP-000068&lt;/VulnDiscussion&gt;&lt;FalsePositives&gt;&lt;/FalsePositives&gt;&lt;FalseNegatives&gt;&lt;/FalseNegatives&gt;&lt;Documentable&gt;false&lt;/Documentable&gt;&lt;Mitigations&gt;&lt;/Mitigations&gt;&lt;SeverityOverrideGuidance&gt;&lt;/SeverityOverrideGuidance&gt;&lt;PotentialImpacts&gt;&lt;/PotentialImpacts&gt;&lt;ThirdPartyTools&gt;&lt;/ThirdPartyTools&gt;&lt;MitigationControl&gt;&lt;/MitigationControl&gt;&lt;Responsibility&gt;&lt;/Responsibility&gt;&lt;IAControls&gt;&lt;/IAControls&gt;</description><reference><dc:title>DPMS Target Axonius Federal Systems Ax-OS</dc:title><dc:publisher>DISA</dc:publisher><dc:type>DPMS Target</dc:type><dc:subject>Axonius Federal Systems Ax-OS</dc:subject><dc:identifier>5710</dc:identifier></reference><ident system="http://cyber.mil/cci">CCI-001384</ident><ident system="http://cyber.mil/cci">CCI-001385</ident><ident system="http://cyber.mil/cci">CCI-001386</ident><ident system="http://cyber.mil/cci">CCI-001387</ident><ident system="http://cyber.mil/cci">CCI-001388</ident><ident system="http://cyber.mil/cci">CCI-000048</ident><fixtext fixref="F-80049r1122667_fix">Select the gear icon (System Settings) &gt;&gt; GUI &gt;&gt; Login.

Under Login Page Settings &gt;&gt; Custom message (up to 3000 characters), enter the Standard Mandatory DOD Notice and Consent Banner text.

Click "Save".</fixtext><fix id="F-80049r1122667_fix" /><check system="C-80144r1122666_chk"><check-content-ref href="AFS_Ax-OS_STIG.xml" name="M" /><check-content>Select the gear icon (System Settings) &gt;&gt; GUI &gt;&gt; Login.

Under Login Page Settings &gt;&gt; Custom message (up to 3000 characters), verify the Standard Mandatory DOD Notice and Consent Banner is displayed. 

If the banner is not displayed, this is a finding.</check-content></check></Rule></Group><Group id="V-276007"><title>SRG-APP-000070</title><description>&lt;GroupDescription&gt;&lt;/GroupDescription&gt;</description><Rule id="SV-276007r1122671_rule" weight="10.0" severity="medium"><version>AXOS-00-000035</version><title>Ax-OS must display the Standard Mandatory DOD Notice and Consent Banner before granting access to the Toolbox.</title><description>&lt;VulnDiscussion&gt;Display of a standardized and approved use notification before granting access to the publicly accessible application ensures privacy and security notification verbiage used is consistent with applicable federal laws, Executive Orders, directives, policies, regulations, standards, and guidance.

System use notifications are required only for access via logon interfaces with human users and are not required when such human interfaces do not exist.

The banner must be formatted in accordance with DTM-08-060. Use the following verbiage for desktops, laptops, and other devices accommodating banners of 1300 characters:

"You are accessing a U.S. Government (USG) Information System (IS) that is provided for USG-authorized use only.

By using this IS (which includes any device attached to this IS), you consent to the following conditions:

-The USG routinely intercepts and monitors communications on this IS for purposes including, but not limited to, penetration testing, COMSEC monitoring, network operations and defense, personnel misconduct (PM), law enforcement (LE), and counterintelligence (CI) investigations.

-At any time, the USG may inspect and seize data stored on this IS.

-Communications using, or data stored on, this IS are not private, are subject to routine monitoring, interception, and search, and may be disclosed or used for any USG-authorized purpose.

-This IS includes security measures (e.g., authentication and access controls) to protect USG interests--not for your personal benefit or privacy.

-Notwithstanding the above, using this IS does not constitute consent to PM, LE or CI investigative searching or monitoring of the content of privileged communications, or work product, related to personal representation or services by attorneys, psychotherapists, or clergy, and their assistants. Such communications and work product are private and confidential. See User Agreement for details."
 
Use the following verbiage for operating systems that have severe limitations on the number of characters that can be displayed in the banner:

"I've read &amp; consent to terms in IS user agreem't."&lt;/VulnDiscussion&gt;&lt;FalsePositives&gt;&lt;/FalsePositives&gt;&lt;FalseNegatives&gt;&lt;/FalseNegatives&gt;&lt;Documentable&gt;false&lt;/Documentable&gt;&lt;Mitigations&gt;&lt;/Mitigations&gt;&lt;SeverityOverrideGuidance&gt;&lt;/SeverityOverrideGuidance&gt;&lt;PotentialImpacts&gt;&lt;/PotentialImpacts&gt;&lt;ThirdPartyTools&gt;&lt;/ThirdPartyTools&gt;&lt;MitigationControl&gt;&lt;/MitigationControl&gt;&lt;Responsibility&gt;&lt;/Responsibility&gt;&lt;IAControls&gt;&lt;/IAControls&gt;</description><reference><dc:title>DPMS Target Axonius Federal Systems Ax-OS</dc:title><dc:publisher>DISA</dc:publisher><dc:type>DPMS Target</dc:type><dc:subject>Axonius Federal Systems Ax-OS</dc:subject><dc:identifier>5710</dc:identifier></reference><ident system="http://cyber.mil/cci">CCI-001384</ident><ident system="http://cyber.mil/cci">CCI-001385</ident><ident system="http://cyber.mil/cci">CCI-001386</ident><ident system="http://cyber.mil/cci">CCI-001387</ident><ident system="http://cyber.mil/cci">CCI-001388</ident><fixtext fixref="F-80050r1122670_fix">From the Axonius Toolbox (accessed via SSH) Main Actions Menu, select the following options:

Compliance Actions &gt;&gt; Advanced Compliance Actions &gt;&gt; Update SSH Banner Text

Enter the Standard Mandatory DOD Notice and Consent Banner text.</fixtext><fix id="F-80050r1122670_fix" /><check system="C-80145r1122669_chk"><check-content-ref href="AFS_Ax-OS_STIG.xml" name="M" /><check-content>Access the Axonius Toolbox via Secure Shell (SSH) and verify the Standard Mandatory DOD Notice and Consent Banner is displayed. 

If the banner is not displayed, this is a finding.</check-content></check></Rule></Group><Group id="V-276008"><title>SRG-APP-000141</title><description>&lt;GroupDescription&gt;&lt;/GroupDescription&gt;</description><Rule id="SV-276008r1122674_rule" weight="10.0" severity="medium"><version>AXOS-00-000040</version><title>Ax-OS password manager must be disabled.</title><description>&lt;VulnDiscussion&gt;It is detrimental for applications to provide, or install by default, functionality exceeding requirements or mission objectives. These unnecessary capabilities or services are often overlooked and therefore may remain unsecured. They increase the risk to the platform by providing additional attack vectors.

Applications are capable of providing a wide variety of functions and services. Some of the functions and services provided by default may not be necessary to support essential organizational operations (e.g., key missions, functions). 

Examples of nonessential capabilities include, but are not limited to, advertising software or browser plug-ins not related to requirements or providing a wide array of functionality that is not required for every mission but cannot be disabled.&lt;/VulnDiscussion&gt;&lt;FalsePositives&gt;&lt;/FalsePositives&gt;&lt;FalseNegatives&gt;&lt;/FalseNegatives&gt;&lt;Documentable&gt;false&lt;/Documentable&gt;&lt;Mitigations&gt;&lt;/Mitigations&gt;&lt;SeverityOverrideGuidance&gt;&lt;/SeverityOverrideGuidance&gt;&lt;PotentialImpacts&gt;&lt;/PotentialImpacts&gt;&lt;ThirdPartyTools&gt;&lt;/ThirdPartyTools&gt;&lt;MitigationControl&gt;&lt;/MitigationControl&gt;&lt;Responsibility&gt;&lt;/Responsibility&gt;&lt;IAControls&gt;&lt;/IAControls&gt;</description><reference><dc:title>DPMS Target Axonius Federal Systems Ax-OS</dc:title><dc:publisher>DISA</dc:publisher><dc:type>DPMS Target</dc:type><dc:subject>Axonius Federal Systems Ax-OS</dc:subject><dc:identifier>5710</dc:identifier></reference><ident system="http://cyber.mil/cci">CCI-000381</ident><fixtext fixref="F-80051r1122673_fix">Select the gear icon (System Settings) &gt;&gt; Access Management &gt;&gt; External Password Managers.

Disable the "Use Password Manager" slide bar.</fixtext><fix id="F-80051r1122673_fix" /><check system="C-80146r1122672_chk"><check-content-ref href="AFS_Ax-OS_STIG.xml" name="M" /><check-content>Select the gear icon (System Settings) &gt;&gt; Access Management &gt;&gt; External Password Managers.

If the "Use Password Manager" slide bar is enabled, this is a finding.</check-content></check></Rule></Group><Group id="V-276009"><title>SRG-APP-000149</title><description>&lt;GroupDescription&gt;&lt;/GroupDescription&gt;</description><Rule id="SV-276009r1122677_rule" weight="10.0" severity="high"><version>AXOS-00-000045</version><title>Ax-OS must use multifactor authentication for network access to the customer account.</title><description>&lt;VulnDiscussion&gt;Without the use of multifactor authentication, the ease of access to privileged functions is greatly increased. 

Multifactor authentication requires using two or more factors to achieve authentication. 

Factors include: 
(i) something a user knows (e.g., password/PIN); 
(ii) something a user has (e.g., cryptographic identification device, token); or 
(iii) something a user is (e.g., biometric). 

A privileged account is defined as an information system account with authorizations of a privileged user. 

Network access is defined as access to an information system by a user (or a process acting on behalf of a user) communicating through a network (e.g., local area network, wide area network, or the internet).&lt;/VulnDiscussion&gt;&lt;FalsePositives&gt;&lt;/FalsePositives&gt;&lt;FalseNegatives&gt;&lt;/FalseNegatives&gt;&lt;Documentable&gt;false&lt;/Documentable&gt;&lt;Mitigations&gt;&lt;/Mitigations&gt;&lt;SeverityOverrideGuidance&gt;&lt;/SeverityOverrideGuidance&gt;&lt;PotentialImpacts&gt;&lt;/PotentialImpacts&gt;&lt;ThirdPartyTools&gt;&lt;/ThirdPartyTools&gt;&lt;MitigationControl&gt;&lt;/MitigationControl&gt;&lt;Responsibility&gt;&lt;/Responsibility&gt;&lt;IAControls&gt;&lt;/IAControls&gt;</description><reference><dc:title>DPMS Target Axonius Federal Systems Ax-OS</dc:title><dc:publisher>DISA</dc:publisher><dc:type>DPMS Target</dc:type><dc:subject>Axonius Federal Systems Ax-OS</dc:subject><dc:identifier>5710</dc:identifier></reference><ident system="http://cyber.mil/cci">CCI-000765</ident><fixtext fixref="F-80052r1122676_fix">From the Axonius Toolbox (accessed via SSH) Main Actions Menu, select the following options:

System Actions &gt;&gt; Update customer account SSH key

Follow the on-screen prompts to configure key-based authentication.</fixtext><fix id="F-80052r1122676_fix" /><check system="C-80147r1122675_chk"><check-content-ref href="AFS_Ax-OS_STIG.xml" name="M" /><check-content>Have the system administrator (SA) demonstrate accessing the Axonius Toolbox (accessed via Secure Shell [SSH]).

Verify the SA is using a password-protected SSH key to log in to the system. 

If the SA is not using a password-protected SSH key to log in to the system, this is a finding.</check-content></check></Rule></Group><Group id="V-276010"><title>SRG-APP-000149</title><description>&lt;GroupDescription&gt;&lt;/GroupDescription&gt;</description><Rule id="SV-276010r1122680_rule" weight="10.0" severity="high"><version>AXOS-00-000050</version><title>Ax-OS must use multifactor authentication for network access to the files account.</title><description>&lt;VulnDiscussion&gt;Without the use of multifactor authentication, the ease of access to privileged functions is greatly increased. 

Multifactor authentication requires using two or more factors to achieve authentication. 

Factors include: 
(i) something a user knows (e.g., password/PIN); 
(ii) something a user has (e.g., cryptographic identification device, token); or 
(iii) something a user is (e.g., biometric). 

A privileged account is defined as an information system account with authorizations of a privileged user. 

Network access is defined as access to an information system by a user (or a process acting on behalf of a user) communicating through a network (e.g., local area network, wide area network, or the internet).&lt;/VulnDiscussion&gt;&lt;FalsePositives&gt;&lt;/FalsePositives&gt;&lt;FalseNegatives&gt;&lt;/FalseNegatives&gt;&lt;Documentable&gt;false&lt;/Documentable&gt;&lt;Mitigations&gt;&lt;/Mitigations&gt;&lt;SeverityOverrideGuidance&gt;&lt;/SeverityOverrideGuidance&gt;&lt;PotentialImpacts&gt;&lt;/PotentialImpacts&gt;&lt;ThirdPartyTools&gt;&lt;/ThirdPartyTools&gt;&lt;MitigationControl&gt;&lt;/MitigationControl&gt;&lt;Responsibility&gt;&lt;/Responsibility&gt;&lt;IAControls&gt;&lt;/IAControls&gt;</description><reference><dc:title>DPMS Target Axonius Federal Systems Ax-OS</dc:title><dc:publisher>DISA</dc:publisher><dc:type>DPMS Target</dc:type><dc:subject>Axonius Federal Systems Ax-OS</dc:subject><dc:identifier>5710</dc:identifier></reference><ident system="http://cyber.mil/cci">CCI-000765</ident><fixtext fixref="F-80053r1122679_fix">From the Axonius Toolbox (accessed via SSH) Main Actions Menu, select the following options:

System Actions &gt;&gt; Update files account SSH key

Follow the on-screen prompts to configure key-based authentication.</fixtext><fix id="F-80053r1122679_fix" /><check system="C-80148r1122678_chk"><check-content-ref href="AFS_Ax-OS_STIG.xml" name="M" /><check-content>Have the system administrator (SA) demonstrate logging in to the Axonius host via Secure File Transfer Protocol (SFTP).

Verify the SA is using a password-protected Secure Shell (SSH) key to log in to the system. 

If the SA is not using a password-protected SSH key to log in to the system, this is a finding.</check-content></check></Rule></Group><Group id="V-276011"><title>SRG-APP-000150</title><description>&lt;GroupDescription&gt;&lt;/GroupDescription&gt;</description><Rule id="SV-276011r1123259_rule" weight="10.0" severity="high"><version>AXOS-00-000055</version><title>Ax-OS must use multifactor authentication for network access to nonprivileged accounts.</title><description>&lt;VulnDiscussion&gt;To ensure accountability and prevent unauthenticated access, nonprivileged users must utilize multifactor authentication to prevent potential misuse and compromise of the system. 

Multifactor authentication uses two or more factors to achieve authentication. 

Factors include:
(i) Something you know (e.g., password/PIN); 
(ii) Something you have (e.g., cryptographic identification device, token); or 
(iii) Something you are (e.g., biometric). 

A nonprivileged account is any information system account with authorizations of a nonprivileged user. 

Network access is any access to an application by a user (or process acting on behalf of a user) that is obtained through a network connection.

Applications that integrate with the DOD Active Directory and use the DOD Common Access Card (CAC) are examples of compliant multifactor authentication solutions.&lt;/VulnDiscussion&gt;&lt;FalsePositives&gt;&lt;/FalsePositives&gt;&lt;FalseNegatives&gt;&lt;/FalseNegatives&gt;&lt;Documentable&gt;false&lt;/Documentable&gt;&lt;Mitigations&gt;&lt;/Mitigations&gt;&lt;SeverityOverrideGuidance&gt;&lt;/SeverityOverrideGuidance&gt;&lt;PotentialImpacts&gt;&lt;/PotentialImpacts&gt;&lt;ThirdPartyTools&gt;&lt;/ThirdPartyTools&gt;&lt;MitigationControl&gt;&lt;/MitigationControl&gt;&lt;Responsibility&gt;&lt;/Responsibility&gt;&lt;IAControls&gt;&lt;/IAControls&gt;</description><reference><dc:title>DPMS Target Axonius Federal Systems Ax-OS</dc:title><dc:publisher>DISA</dc:publisher><dc:type>DPMS Target</dc:type><dc:subject>Axonius Federal Systems Ax-OS</dc:subject><dc:identifier>5710</dc:identifier></reference><ident system="http://cyber.mil/cci">CCI-000766</ident><fixtext fixref="F-80054r1123238_fix">Select the gear icon (System Settings) &gt;&gt; Access Management &gt;&gt; LDAP &amp; SAML.

Under LDAP &amp; SAML, enable either the slide bar for "Allow LDAP Logins" or the slide bar for "Allow SAML Logins".

Configure the remaining fields for the environment.</fixtext><fix id="F-80054r1123238_fix" /><check system="C-80149r1122681_chk"><check-content-ref href="AFS_Ax-OS_STIG.xml" name="M" /><check-content>Select the gear icon (System Settings) &gt;&gt; Access Management &gt;&gt; LDAP &amp; SAML.

Under LDAP &amp; SAML, if the slide bar for "Allow LDAP Logins" or "Allow SAML Logins" is not selected, this is a finding.

If the LDAP or SAML configuration does not point to an authentication source approved by the authorizing official (AO), this is a finding.</check-content></check></Rule></Group><Group id="V-276012"><title>SRG-APP-000150</title><description>&lt;GroupDescription&gt;&lt;/GroupDescription&gt;</description><Rule id="SV-276012r1122686_rule" weight="10.0" severity="high"><version>AXOS-00-000060</version><title>Ax-OS must have no local accounts for the user interface.</title><description>&lt;VulnDiscussion&gt;To ensure accountability and prevent unauthenticated access, nonprivileged users must utilize multifactor authentication to prevent potential misuse and compromise of the system. 

Multifactor authentication uses two or more factors to achieve authentication. 

Factors include:
(i) Something you know (e.g., password/PIN); 
(ii) Something you have (e.g., cryptographic identification device, token); or 
(iii) Something you are (e.g., biometric). 

A nonprivileged account is any information system account with authorizations of a nonprivileged user. 

Network access is any access to an application by a user (or process acting on behalf of a user) that is obtained through a network connection.

Applications that integrate with the DOD Active Directory and use the DOD Common Access Card (CAC) are examples of compliant multifactor authentication solutions.

Satisfies: SRG-APP-000150, SRG-APP-000023, SRG-APP-000024, SRG-APP-000025, SRG-APP-000065, SRG-APP-000148, SRG-APP-000153, SRG-APP-000154, SRG-APP-000155, SRG-APP-000156, SRG-APP-000157, SRG-APP-000163, SRG-APP-000175, SRG-APP-000176, SRG-APP-000177, SRG-APP-000178, SRG-APP-000180, SRG-APP-000183, SRG-APP-000318, SRG-APP-000345, SRG-APP-000389, SRG-APP-000391, SRG-APP-000392, SRG-APP-000394, SRG-APP-000395, SRG-APP-000400, SRG-APP-000401, SRG-APP-000402, SRG-APP-000403, SRG-APP-000404, SRG-APP-000405, SRG-APP-000410, SRG-APP-000427, SRG-APP-000580, SRG-APP-000700, SRG-APP-000705, SRG-APP-000710, SRG-APP-000740, SRG-APP-000815, SRG-APP-000820, SRG-APP-000825, SRG-APP-000830, SRG-APP-000835, SRG-APP-000840, SRG-APP-000845, SRG-APP-000850, SRG-APP-000855, SRG-APP-000860, SRG-APP-000865, SRG-APP-000870, SRG-APP-000875, SRG-APP-000880, SRG-APP-000885, SRG-APP-000890&lt;/VulnDiscussion&gt;&lt;FalsePositives&gt;&lt;/FalsePositives&gt;&lt;FalseNegatives&gt;&lt;/FalseNegatives&gt;&lt;Documentable&gt;false&lt;/Documentable&gt;&lt;Mitigations&gt;&lt;/Mitigations&gt;&lt;SeverityOverrideGuidance&gt;&lt;/SeverityOverrideGuidance&gt;&lt;PotentialImpacts&gt;&lt;/PotentialImpacts&gt;&lt;ThirdPartyTools&gt;&lt;/ThirdPartyTools&gt;&lt;MitigationControl&gt;&lt;/MitigationControl&gt;&lt;Responsibility&gt;&lt;/Responsibility&gt;&lt;IAControls&gt;&lt;/IAControls&gt;</description><reference><dc:title>DPMS Target Axonius Federal Systems Ax-OS</dc:title><dc:publisher>DISA</dc:publisher><dc:type>DPMS Target</dc:type><dc:subject>Axonius Federal Systems Ax-OS</dc:subject><dc:identifier>5710</dc:identifier></reference><ident system="http://cyber.mil/cci">CCI-000766</ident><ident system="http://cyber.mil/cci">CCI-000015</ident><ident system="http://cyber.mil/cci">CCI-000016</ident><ident system="http://cyber.mil/cci">CCI-000017</ident><ident system="http://cyber.mil/cci">CCI-000044</ident><ident system="http://cyber.mil/cci">CCI-000764</ident><ident system="http://cyber.mil/cci">CCI-004045</ident><ident system="http://cyber.mil/cci">CCI-004046</ident><ident system="http://cyber.mil/cci">CCI-001941</ident><ident system="http://cyber.mil/cci">CCI-003627</ident><ident system="http://cyber.mil/cci">CCI-000185</ident><ident system="http://cyber.mil/cci">CCI-000186</ident><ident system="http://cyber.mil/cci">CCI-000187</ident><ident system="http://cyber.mil/cci">CCI-000206</ident><ident system="http://cyber.mil/cci">CCI-000804</ident><ident system="http://cyber.mil/cci">CCI-000884</ident><ident system="http://cyber.mil/cci">CCI-002145</ident><ident system="http://cyber.mil/cci">CCI-002238</ident><ident system="http://cyber.mil/cci">CCI-002038</ident><ident system="http://cyber.mil/cci">CCI-001953</ident><ident system="http://cyber.mil/cci">CCI-001954</ident><ident system="http://cyber.mil/cci">CCI-001958</ident><ident system="http://cyber.mil/cci">CCI-001967</ident><ident system="http://cyber.mil/cci">CCI-002007</ident><ident system="http://cyber.mil/cci">CCI-004068</ident><ident system="http://cyber.mil/cci">CCI-002009</ident><ident system="http://cyber.mil/cci">CCI-002010</ident><ident system="http://cyber.mil/cci">CCI-004083</ident><ident system="http://cyber.mil/cci">CCI-004085</ident><ident system="http://cyber.mil/cci">CCI-001632</ident><ident system="http://cyber.mil/cci">CCI-002470</ident><ident system="http://cyber.mil/cci">CCI-003628</ident><ident system="http://cyber.mil/cci">CCI-003629</ident><ident system="http://cyber.mil/cci">CCI-003747</ident><ident system="http://cyber.mil/cci">CCI-004047</ident><ident system="http://cyber.mil/cci">CCI-004058</ident><ident system="http://cyber.mil/cci">CCI-004059</ident><ident system="http://cyber.mil/cci">CCI-004060</ident><ident system="http://cyber.mil/cci">CCI-004061</ident><ident system="http://cyber.mil/cci">CCI-004062</ident><ident system="http://cyber.mil/cci">CCI-004063</ident><ident system="http://cyber.mil/cci">CCI-004064</ident><ident system="http://cyber.mil/cci">CCI-004065</ident><ident system="http://cyber.mil/cci">CCI-004066</ident><ident system="http://cyber.mil/cci">CCI-004192</ident><ident system="http://cyber.mil/cci">CCI-004901</ident><ident system="http://cyber.mil/cci">CCI-004902</ident><fixtext fixref="F-80055r1122685_fix">Role-Based Access Control hierarchy is to be defined by the AO. Separation of duties must be configured.

Select the gear icon (System Settings) &gt;&gt; User and Role Management &gt;&gt; Users.

After Lightweight Directory Access Protocol (LDAP)/Single Sign-On (SSO) has been configured, remove all local users.</fixtext><fix id="F-80055r1122685_fix" /><check system="C-80150r1122684_chk"><check-content-ref href="AFS_Ax-OS_STIG.xml" name="M" /><check-content>Role-Based Access Control hierarchy is to be defined by the authorizing official (AO). Separation of duties must be configured.

Select the gear icon (System Settings) &gt;&gt; User and Role Management &gt;&gt; Users.

In the list of users, verify the list is empty.

If the list is not empty, this is a finding.</check-content></check></Rule></Group><Group id="V-276013"><title>SRG-APP-000219</title><description>&lt;GroupDescription&gt;&lt;/GroupDescription&gt;</description><Rule id="SV-276013r1122689_rule" weight="10.0" severity="high"><version>AXOS-00-000065</version><title>Ax-OS must protect the authenticity of communications sessions.</title><description>&lt;VulnDiscussion&gt;Authenticity protection provides protection against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.

Application communication sessions are protected using transport encryption protocols such as Transport Layer Security (TLS). TLS provides web applications with a means to authenticate user sessions and encrypt application traffic. Session authentication can be single (one way) or mutual (two way) in nature. Single authentication authenticates the server for the client, whereas mutual authentication provides a means for the client and server to authenticate each other. 

This requirement applies to applications that use communications sessions. This includes, but is not limited to, web-based applications and service-oriented architectures (SOAs). 

This requirement addresses communications protection at the application session versus the network packet. It also establishes grounds for confidence at both ends of communications sessions in relation to the ongoing identities of other parties and validity of information transmitted. 

Depending on the required degree of confidentiality and integrity, web services/SOA will require the use of TL) mutual authentication (two-way/bidirectional).

Satisfies: SRG-APP-000219, SRG-APP-000910&lt;/VulnDiscussion&gt;&lt;FalsePositives&gt;&lt;/FalsePositives&gt;&lt;FalseNegatives&gt;&lt;/FalseNegatives&gt;&lt;Documentable&gt;false&lt;/Documentable&gt;&lt;Mitigations&gt;&lt;/Mitigations&gt;&lt;SeverityOverrideGuidance&gt;&lt;/SeverityOverrideGuidance&gt;&lt;PotentialImpacts&gt;&lt;/PotentialImpacts&gt;&lt;ThirdPartyTools&gt;&lt;/ThirdPartyTools&gt;&lt;MitigationControl&gt;&lt;/MitigationControl&gt;&lt;Responsibility&gt;&lt;/Responsibility&gt;&lt;IAControls&gt;&lt;/IAControls&gt;</description><reference><dc:title>DPMS Target Axonius Federal Systems Ax-OS</dc:title><dc:publisher>DISA</dc:publisher><dc:type>DPMS Target</dc:type><dc:subject>Axonius Federal Systems Ax-OS</dc:subject><dc:identifier>5710</dc:identifier></reference><ident system="http://cyber.mil/cci">CCI-001184</ident><ident system="http://cyber.mil/cci">CCI-004909</ident><fixtext fixref="F-80056r1122688_fix">Select the gear icon (System Settings) &gt;&gt; Privacy and Security &gt;&gt; Certificate and Encryption.

Under Certificate Verifications Settings, select "Use OCSP".

Under SSL Trust &amp; CA Settings, select "Use custom certificate" and configure for a DOD PKI (or other AO-approved certificate).

Under Mutual TLS Settings, enable the "Enable mutual TLS" slide bar. Check the "Enforce client certificate validation" box.

Under Encryption Settings, ensure the "Allow legacy SSL cipher suites for adapters" box is unchecked.</fixtext><fix id="F-80056r1122688_fix" /><check system="C-80151r1122687_chk"><check-content-ref href="AFS_Ax-OS_STIG.xml" name="M" /><check-content>Select the gear icon (System Settings) &gt;&gt; Privacy and Security &gt;&gt; Certificate and Encryption.

Under SSL Certificate, if the certificate has not been changed from the self-signed default certificate, unless otherwise approved by the authorizing official (AO), this is a finding.

Under Certificate Verifications Settings, if "Use OCSP" is not selected, this is a finding.

Under SSL Trust &amp; CA Settings, if "Use custom certificate" is not selected and configured for a DOD PKI (or other AO-approved certificate), this is a finding.

Under Mutual TLS Settings, if the "Enable mutual TLS" slide bar is not enabled, and the "Enforce client certificate validation" box is unchecked, this is a finding.

Under Encryption Settings, if the "Allow legacy SSL cipher suites for adapters" is checked, this is a finding.</check-content></check></Rule></Group><Group id="V-276014"><title>SRG-APP-000358</title><description>&lt;GroupDescription&gt;&lt;/GroupDescription&gt;</description><Rule id="SV-276014r1122692_rule" weight="10.0" severity="high"><version>AXOS-00-000070</version><title>Ax-OS must off-load audit records onto a different system or media than the system being audited.</title><description>&lt;VulnDiscussion&gt;Information stored in one location is vulnerable to accidental or incidental deletion or alteration.

Off-loading is a common process in information systems with limited audit storage capacity.

Satisfies: SRG-APP-000358, SRG-APP-000086, SRG-APP-000090, SRG-APP-000097, SRG-APP-000108, SRG-APP-000111, SRG-APP-000115, SRG-APP-000116, SRG-APP-000118, SRG-APP-000120, SRG-APP-000121, SRG-APP-000122, SRG-APP-000123, SRG-APP-000125, SRG-APP-000181, SRG-APP-000267, SRG-APP-000275, SRG-APP-000291, SRG-APP-000292, SRG-APP-000293, SRG-APP-000294, SRG-APP-000320, SRG-APP-000357, SRG-APP-000359, SRG-APP-000360, SRG-APP-000362, SRG-APP-000363, SRG-APP-000364, SRG-APP-000365, SRG-APP-000366, SRG-APP-000367, SRG-APP-000368, SRG-APP-000369, SRG-APP-000370, SRG-APP-000376, SRG-APP-000515, SRG-APP-000745, SRG-APP-000750, SRG-APP-000755, SRG-APP-000760, SRG-APP-000765, SRG-APP-000770, SRG-APP-000775, SRG-APP-000780, SRG-APP-000785, SRG-APP-000790, SRG-APP-000795, SRG-APP-000800, SRG-APP-000945, SRG-APP-000950, SRG-APP-000955&lt;/VulnDiscussion&gt;&lt;FalsePositives&gt;&lt;/FalsePositives&gt;&lt;FalseNegatives&gt;&lt;/FalseNegatives&gt;&lt;Documentable&gt;false&lt;/Documentable&gt;&lt;Mitigations&gt;&lt;/Mitigations&gt;&lt;SeverityOverrideGuidance&gt;&lt;/SeverityOverrideGuidance&gt;&lt;PotentialImpacts&gt;&lt;/PotentialImpacts&gt;&lt;ThirdPartyTools&gt;&lt;/ThirdPartyTools&gt;&lt;MitigationControl&gt;&lt;/MitigationControl&gt;&lt;Responsibility&gt;&lt;/Responsibility&gt;&lt;IAControls&gt;&lt;/IAControls&gt;</description><reference><dc:title>DPMS Target Axonius Federal Systems Ax-OS</dc:title><dc:publisher>DISA</dc:publisher><dc:type>DPMS Target</dc:type><dc:subject>Axonius Federal Systems Ax-OS</dc:subject><dc:identifier>5710</dc:identifier></reference><ident system="http://cyber.mil/cci">CCI-001851</ident><ident system="http://cyber.mil/cci">CCI-000174</ident><ident system="http://cyber.mil/cci">CCI-000171</ident><ident system="http://cyber.mil/cci">CCI-000132</ident><ident system="http://cyber.mil/cci">CCI-000139</ident><ident system="http://cyber.mil/cci">CCI-000154</ident><ident system="http://cyber.mil/cci">CCI-000158</ident><ident system="http://cyber.mil/cci">CCI-000159</ident><ident system="http://cyber.mil/cci">CCI-000162</ident><ident system="http://cyber.mil/cci">CCI-000164</ident><ident system="http://cyber.mil/cci">CCI-001493</ident><ident system="http://cyber.mil/cci">CCI-001494</ident><ident system="http://cyber.mil/cci">CCI-001495</ident><ident system="http://cyber.mil/cci">CCI-001348</ident><ident system="http://cyber.mil/cci">CCI-001876</ident><ident system="http://cyber.mil/cci">CCI-001314</ident><ident system="http://cyber.mil/cci">CCI-001294</ident><ident system="http://cyber.mil/cci">CCI-000015</ident><ident system="http://cyber.mil/cci">CCI-001849</ident><ident system="http://cyber.mil/cci">CCI-001855</ident><ident system="http://cyber.mil/cci">CCI-001858</ident><ident system="http://cyber.mil/cci">CCI-001875</ident><ident system="http://cyber.mil/cci">CCI-001877</ident><ident system="http://cyber.mil/cci">CCI-001878</ident><ident system="http://cyber.mil/cci">CCI-001879</ident><ident system="http://cyber.mil/cci">CCI-001880</ident><ident system="http://cyber.mil/cci">CCI-001881</ident><ident system="http://cyber.mil/cci">CCI-001882</ident><ident system="http://cyber.mil/cci">CCI-001896</ident><ident system="http://cyber.mil/cci">CCI-003821</ident><ident system="http://cyber.mil/cci">CCI-003822</ident><ident system="http://cyber.mil/cci">CCI-003823</ident><ident system="http://cyber.mil/cci">CCI-003824</ident><ident system="http://cyber.mil/cci">CCI-003825</ident><ident system="http://cyber.mil/cci">CCI-003826</ident><ident system="http://cyber.mil/cci">CCI-003827</ident><ident system="http://cyber.mil/cci">CCI-003828</ident><ident system="http://cyber.mil/cci">CCI-003829</ident><ident system="http://cyber.mil/cci">CCI-003830</ident><ident system="http://cyber.mil/cci">CCI-003831</ident><ident system="http://cyber.mil/cci">CCI-003834</ident><ident system="http://cyber.mil/cci">CCI-004992</ident><ident system="http://cyber.mil/cci">CCI-004996</ident><ident system="http://cyber.mil/cci">CCI-004997</ident><fixtext fixref="F-80057r1122691_fix">Select the gear icon (System Settings) &gt;&gt; External Integrations &gt;&gt; Syslog.

Under the Syslog menu, enable "Use Syslog".

Under the Syslog menu, configure "Syslog instance" for an external log server.</fixtext><fix id="F-80057r1122691_fix" /><check system="C-80152r1122690_chk"><check-content-ref href="AFS_Ax-OS_STIG.xml" name="M" /><check-content>Select the gear icon (System Settings) &gt;&gt; External Integrations &gt;&gt; Syslog.

Under the Syslog menu, if the "Use Syslog" slide bar is not selected, this is a finding.

Under the Syslog menu, if "Syslog instance" has not been configured for an external log server(or otherwise proven Syslog is being captured by an external log server), this is a finding.</check-content></check></Rule></Group><Group id="V-276015"><title>SRG-APP-000414</title><description>&lt;GroupDescription&gt;&lt;/GroupDescription&gt;</description><Rule id="SV-276015r1122695_rule" weight="10.0" severity="medium"><version>AXOS-00-000075</version><title>Ax-OS must implement privileged access authorization to all information systems and infrastructure components for selected organization-defined vulnerability scanning activities.</title><description>&lt;VulnDiscussion&gt;In certain situations, the nature of the vulnerability scanning may be more intrusive, or the information system component that is the subject of the scanning may contain highly sensitive information. Privileged access authorization to selected system components facilitates more thorough vulnerability scanning and also protects the sensitive nature of such scanning.

The vulnerability scanning application must use privileged access authorization for the scanning account.&lt;/VulnDiscussion&gt;&lt;FalsePositives&gt;&lt;/FalsePositives&gt;&lt;FalseNegatives&gt;&lt;/FalseNegatives&gt;&lt;Documentable&gt;false&lt;/Documentable&gt;&lt;Mitigations&gt;&lt;/Mitigations&gt;&lt;SeverityOverrideGuidance&gt;&lt;/SeverityOverrideGuidance&gt;&lt;PotentialImpacts&gt;&lt;/PotentialImpacts&gt;&lt;ThirdPartyTools&gt;&lt;/ThirdPartyTools&gt;&lt;MitigationControl&gt;&lt;/MitigationControl&gt;&lt;Responsibility&gt;&lt;/Responsibility&gt;&lt;IAControls&gt;&lt;/IAControls&gt;</description><reference><dc:title>DPMS Target Axonius Federal Systems Ax-OS</dc:title><dc:publisher>DISA</dc:publisher><dc:type>DPMS Target</dc:type><dc:subject>Axonius Federal Systems Ax-OS</dc:subject><dc:identifier>5710</dc:identifier></reference><ident system="http://cyber.mil/cci">CCI-001067</ident><fixtext fixref="F-80058r1122694_fix">From the Axonius Toolbox (accessed via SSH) Main Actions Menu, select the following options:

Compliance Actions &gt;&gt; Advanced Compliance Actions &gt;&gt; Add Tenable Scan Account

Enter the username.</fixtext><fix id="F-80058r1122694_fix" /><check system="C-80153r1122693_chk"><check-content-ref href="AFS_Ax-OS_STIG.xml" name="M" /><check-content>From the Axonius Toolbox (accessed via Secure Shell [SSH]) Main Actions Menu, select the following options:

Compliance Actions &gt;&gt; Advanced Compliance Actions &gt;&gt; Update Tenable Scan Account Permissions

Enter the scanning account username.

If no scanning account has been set, this is a finding.</check-content></check></Rule></Group><Group id="V-276016"><title>SRG-APP-000925</title><description>&lt;GroupDescription&gt;&lt;/GroupDescription&gt;</description><Rule id="SV-276016r1123260_rule" weight="10.0" severity="medium"><version>AXOS-00-000080</version><title>Ax-OS must compare the internal system clocks on an organization-defined frequency with an organization-defined authoritative time source.</title><description>&lt;VulnDiscussion&gt;Synchronization of internal system clocks with an authoritative source provides uniformity of time stamps for systems with multiple system clocks and systems connected over a network.

Satisfies: SRG-APP-000925, SRG-APP-000371, SRG-APP-000372, SRG-APP-000374, SRG-APP-000920&lt;/VulnDiscussion&gt;&lt;FalsePositives&gt;&lt;/FalsePositives&gt;&lt;FalseNegatives&gt;&lt;/FalseNegatives&gt;&lt;Documentable&gt;false&lt;/Documentable&gt;&lt;Mitigations&gt;&lt;/Mitigations&gt;&lt;SeverityOverrideGuidance&gt;&lt;/SeverityOverrideGuidance&gt;&lt;PotentialImpacts&gt;&lt;/PotentialImpacts&gt;&lt;ThirdPartyTools&gt;&lt;/ThirdPartyTools&gt;&lt;MitigationControl&gt;&lt;/MitigationControl&gt;&lt;Responsibility&gt;&lt;/Responsibility&gt;&lt;IAControls&gt;&lt;/IAControls&gt;</description><reference><dc:title>DPMS Target Axonius Federal Systems Ax-OS</dc:title><dc:publisher>DISA</dc:publisher><dc:type>DPMS Target</dc:type><dc:subject>Axonius Federal Systems Ax-OS</dc:subject><dc:identifier>5710</dc:identifier></reference><ident system="http://cyber.mil/cci">CCI-004923</ident><ident system="http://cyber.mil/cci">CCI-004926</ident><ident system="http://cyber.mil/cci">CCI-001890</ident><ident system="http://cyber.mil/cci">CCI-004922</ident><fixtext fixref="F-80059r1123241_fix">From the Axonius Toolbox (accessed via SSH) Main Actions Menu, select the following options:

System Actions &gt;&gt; Advanced System Actions &gt;&gt; Configure NTP

Enter the hostname/IP of an AO-approved authoritative time source.</fixtext><fix id="F-80059r1123241_fix" /><check system="C-80154r1123240_chk"><check-content-ref href="AFS_Ax-OS_STIG.xml" name="M" /><check-content>From the Axonius Toolbox (accessed via Secure Shell [SSH]) Main Actions Menu, select the following options:

System Actions &gt;&gt; Advanced System Actions &gt;&gt; NTP Sources

If any NTP sources listed are not an authoritative time source approved by the authorizing official (AO), this is a finding.</check-content></check></Rule></Group></Benchmark>