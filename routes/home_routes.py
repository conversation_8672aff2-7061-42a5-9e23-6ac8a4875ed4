from flask import Blueprint, render_template, session, abort, redirect, url_for
from helper import *
from encryption import AES256

import subprocess
import platform
import re
import json

import psutil

home_bp = Blueprint('home', __name__)

def check_session(func):
    def wrapper(*args, **kwargs):
        try:
            if 'username' in session:
                return func(*args, **kwargs)
            else:
                return redirect(url_for('admin.login'))
        except KeyError:
             return redirect(url_for('admin.login'))
    wrapper.__name__ = func.__name__
    return wrapper

@home_bp.route('/')
@check_session
def home():
    page_title = "Home"    
    role_id = get_role_id('<EMAIL>')
    system_menu = build_menu(page_title.lower(), role_id)
    page_content = ''
    
    cpu_info = parse_cpu_info()
    print(display_cpu_info(cpu_info))
    print(get_mount_points_info())
    disk_info = get_mount_points_info()
    return render_template('home/home.html',menu=system_menu,page_title=page_title,page_content=page_content, disk_info = disk_info)

def get_cpu_info_linux():
    """Fetch and parse CPU information on Linux using 'lscpu'."""
    try:
        output = subprocess.check_output(['lscpu'], text=True)
        cpu_info = {}
        for line in output.splitlines():
            key, _, value = line.partition(':')
            cpu_info[key.strip()] = value.strip()
        return cpu_info
    except subprocess.CalledProcessError as e:
        print(f"Error fetching CPU info on Linux: {e}")
        return None

def get_process_info():
    cpu_info = subprocess.run(
        ["sysctl", "-a"],
        capture_output=True,
        text=True
    )
    
    # Filter relevant CPU information
    cpu_lines = [line for line in cpu_info.stdout.splitlines() if "cpu" in line.lower()]
    
    for line in cpu_lines:
        print(line)

def parse_cpu_info():
    """Detect the OS and parse CPU information accordingly."""
    system = platform.system()
    if system == 'Linux':
        cpu_info = get_cpu_info_linux()
    else:
        print(f"Unsupported OS: {system}")
        return None

    return cpu_info

def display_cpu_info(cpu_info):
    """Pretty print the CPU information."""
    print(get_process_info())
    if cpu_info:
        print(json.dumps(cpu_info, indent=4))
    else:
        print("No CPU information found.")
        
def get_mount_points_info():
    partitions = psutil.disk_partitions()
    df = pd.DataFrame(columns=['partition','free'])
    # Loop through all partitions and print their usage information
    for partition in partitions:
        try:
            usage = psutil.disk_usage(partition.mountpoint)
            # print(f"Mount Point: {partition.mountpoint}")
            # print(f"  Total Size: {usage.total / (1024 ** 3):.2f} GB")
            # print(f"  Used Space: {usage.used / (1024 ** 3):.2f} GB")
            # print(f"  Free Space: {usage.free / (1024 ** 3):.2f} GB")
            # print(f"  Free Space Percentage: {usage.percent}%")
            # print("-" * 40)
            df = df.append({'partition': partition.mountpoint, 'free': usage.percent}, ignore_index=True)
        except PermissionError:
            # Handle cases where we don't have permission to access certain mount points
            print(f"Mount Point: {partition.mountpoint} - Permission Denied")
            df = df.append({'partition': partition.mountpoint, 'free': 0}, ignore_index=True)

            print("-" * 40)
    return df