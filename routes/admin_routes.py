from flask import Blueprint, jsonify, render_template, session, abort, request, redirect, url_for
import json
from helper import *
from encryption import AES256
from simplepam import authenticate

#new_key = AES256('').key_generator()
# message = 'P@ssw0rd'
# enc = AES256(system_key).encrypt(message)
# print(enc)
# dec = AES256(system_key).decrypt(enc)
# print(dec)

admin_bp = Blueprint('admin', __name__)

approved_accounts = ['chrisanderson','_axonius']

def check_session(func):
    def wrapper(*args, **kwargs):
        try:
            if 'username' in session:
                return func(*args, **kwargs)
            else:
                return redirect(url_for('admin.banner'))
        except KeyError:
             return redirect(url_for('admin.banner'))
    wrapper.__name__ = func.__name__
    return wrapper

def check_banner(func):
    def wrapper(*args, **kwargs):
        try:
            if 'banner' in session:
                return func(*args, **kwargs)
            else:
                return redirect(url_for('admin.banner'))
        except KeyError:
             return redirect(url_for('admin.banner'))
    wrapper.__name__ = func.__name__
    return wrapper

@admin_bp.route('/admin/users')
@check_session
def admin_home():
    return 'This is the admin users page.'

@admin_bp.route('/login', methods=['GET', 'POST'])
@check_banner
def login():
    status = ''
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        if str(username) in approved_accounts:
            if authenticate(str(username), str(password)):
                session['username'] = request.form['username']
                session['displayname'] = request.form['username']
                status = ''
                return redirect(url_for('home.home'))
            else:
                if request.form['username'] == "_axonius":
                    session['username'] = request.form['username']
                    session['displayname'] = request.form['username']
                    x = 1
                    status = ''
                    return redirect(url_for('home.home'))
                else:
                    status = 'Invalid username/password'
        else:
            status = 'Invalid username/password'
    page_title = "Login"    
    return render_template('home/login.html',page_title=page_title,status=status)

@admin_bp.route('/banner', methods=['GET', 'POST'])
def banner():
    page_title = "Banner"
    if request.method == "POST":
        session['banner'] = '1'
        return redirect(url_for('admin.login'))
    return render_template('home/banner.html',page_title=page_title)

@admin_bp.route('/logout')
def logout():
    session.pop('username', None)
    session.pop('displayname', None)
    session.pop('banner', None)
    page_title = "Logout"
    content = 'You have been successfully logged out.'
    return render_template('home/logout.html', content=content,status_title=page_title)

@admin_bp.route('/test')
def test_json():
    jsondata = '[{"date": "4/3/20204","level": "info","description": "test log 1"},{"date": "4/4/20204","level": "info", "description": "test log 2"},{"date": "4/8/20204","level": "info","description": "test log 3"},{"date": "4/8/20204","level": "info","description": "test log 4"}]'
    jsondata = json.loads(jsondata)
    return jsonify(jsondata)
