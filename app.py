#!/usr/bin/env python3

from flask import Flask, session, jsonify,render_template,abort
from routes.home_routes import home_bp
from routes.admin_routes import admin_bp
from werkzeug.exceptions import HTTPException
from datetime import timedelta
from helper import *
from dotenv import load_dotenv
import os
load_dotenv()

app_host = "0.0.0.0"
app_port = "80"
app_debug = True
system_key = "theKey2024" #os.getenv('system_key')
session_timeout = "15" #os.getenv('session_timeout')

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your_secret_key'
app.config['SESSION_REFRESH_EACH_REQUEST'] = True
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(minutes=int(session_timeout))

app.register_blueprint(home_bp)
app.register_blueprint(admin_bp)

@app.before_request
def make_session_permanent():
    session.modified = True

@app.context_processor
def inject_data():
    chrome_style = os.getenv('app_chrome') #'dark' #confg_value('chrome_style')
    title = os.getenv('app_title') #'Praetorian' #confg_value('title')
    app_version = os.getenv('app_ver') #'v 0.01' #+ confg_value('version')
    status = ''
        
    return dict(chrome_style=chrome_style,app_version=app_version,status=status,title=title)

@app.errorhandler(Exception)
def error_handler(e):
    code = 500
    page_title = "Status"
    if isinstance(e, HTTPException):
        code = e.code
    return render_template('home/error.html', error_content = str(e),status_title=page_title)

if __name__ == "__main__":
    app.run(host=app_host, port=app_port, debug=app_debug)