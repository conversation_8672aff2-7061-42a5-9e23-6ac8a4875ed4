<!DOCTYPE html>
<html lang="en">
    <head>
        <meta http-equiv="content-type" content="text/html; charset=utf-8" />
        <meta http-equiv="x-ua-compatible" content="ie=edge" />
        <title>
            {{title}}{% block title %}{% endblock %}
        </title>
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <link rel="shortcut icon" href="{{ url_for('static', filename='images/favicon.png') }}">
        <link rel="apple-touch-icon" sizes="128x128" href="{{ url_for('static', filename='images/favicon.png') }}">
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
        <link rel="stylesheet" href="{{ url_for('static', filename='stylesheets/master.css') }}" />
        <!--[if IE 8]>
        <link rel="stylesheet" href="{{ url_for('static', filename='stylesheets/ie8.css') }}" />
        <![endif]-->
        <!--[if !IE]><!-->
        <!-- script src="{{ url_for('static', filename='javascripts/iscroll.js') }}"></script -->
        <!--<![endif]-->
        <script src="{{ url_for('static', filename='javascripts/jquery-1.8.3.min.js') }}"></script>
        <script src="{{ url_for('static', filename='javascripts/master.js') }}"></script>
        {% block javascripts %}{% endblock %}
        {% block stylesheets %}{% endblock %}
        <style>
            .fade-in-logo {
            animation: fadeIn 5s;
            -webkit-animation: fadeIn 5s;
            -moz-animation: fadeIn 5s;
            -o-animation: fadeIn 5s;
            -ms-animation: fadeIn 5s;
            }
            
            @keyframes fadeIn {
            0% { opacity: 0; }
            100% { opacity: 1; }
            }
            
            @-moz-keyframes fadeIn {
            0% { opacity: 0; }
            100% { opacity: 1; }
            }
            
            @-webkit-keyframes fadeIn {
            0% { opacity: 0; }
            100% { opacity: 1; }
            }
            
            @-o-keyframes fadeIn {
            0% { opacity: 0; }
            100% { opacity: 1; }
            }
            
            @-ms-keyframes fadeIn {
            0% { opacity: 0; }
            100% { opacity: 1; }
            }
        </style>
    </head>
    <body>{% block content %}{% endblock %}
        <div id="sidebar" class="abs">
            <span id="nav_arrow"></span>
            <div class="abs header_upper chrome_{{chrome_style}}">{{ nav_title }}</div>{% include 'includes/navigation.html' %} 
        </div>
    </body>
</html>