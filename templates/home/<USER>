<!DOCTYPE html>
<html>

<head>
	<meta charset="UTF-8">
	<title>{{title}} : Login</title>
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <link rel="shortcut icon" href="{{ url_for('static', filename='images/favicon.png') }}">
    <link rel="apple-touch-icon" sizes="128x128" href="{{ url_for('static', filename='images/favicon.png') }}">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
	<script src="{{ url_for('static', filename='javascripts/jquery-1.8.3.min.js') }}"></script>
	<style>
		body { margin: 0; padding: 0; }
		
		html { 
			background: url('/static/images/sitelogo_large.png') no-repeat center center fixed; 
			-webkit-background-size: cover;
			-moz-background-size: cover;
			-o-background-size: cover;
			background-size: cover;
            background-color: #aaa;
		}
		
		#page-wrap { width: 60%; padding: 20px; background: white; -moz-box-shadow: 0 0 20px black; -webkit-box-shadow: 0 0 20px black; box-shadow: 0 0 20px black; 
            margin-top: 20px;
            margin-right:auto;
            margin-bottom:0;
            margin-left:auto;
            border-radius: 5px;
            border: 1px solid #000000;
			background: rgba(204, 204, 204, 0.85)}
        body {
            font-family: Arial,Georgia, Serif;
        }
		p {
			padding-bottom: 10px;
		}
		.button {
			padding: 10px;
			margin-top: 5px;
			border: 1px solid #aaaaaa;
			background-color: #afafaf;
			cursor: pointer;
			transition: background-color 0.3s ease;
		}
		
		.button:hover {
			background-color: #d85521; /* Change this color to whatever you like */
		}
	</style>
</head>

<body>

    <div id="page-wrap">
            <table style="width:100%; opacity: 1;">
                <tbody>
                    <tr>
                        <td style="width: 50px;vertical-align: middle;">
							<center><h1>DOD Banner</h1></center>
                            <p>
								"You are accessing a U.S. Government (USG) Information System (IS) that is provided for USG-authorized use only. By using this IS (which includes any device attached to this IS), you consent to the following conditions:
							</p>
							<p>
								<ul style="padding-left: 30px; ">
									<li>The USG routinely intercepts and monitors communications on this IS for purposes including, but not limited to, penetration testing, COMSEC monitoring, network operations and defense, personnel misconduct (PM), law enforcement (LE), and counterintelligence (CI) investigations.</li>
									<li>At any time, the USG may inspect and seize data stored on this IS.</li>
									<li>Communications using, or data stored on, this IS are not private, are subject to routine monitoring, interception, and search, and may be disclosed or used for any USG-authorized purpose.</li>
									<li>This IS includes security measures (e.g., authentication and access controls) to protect USG interests--not for your personal benefit or privacy.</li>
									<li>Notwithstanding the above, using this IS does not constitute consent to PM, LE or CI investigative searching or monitoring of the content of privileged communications, or work product, related to personal representation or services by attorneys, psychotherapists, or clergy, and their assistants. Such communications and work product are private and confidential. See User Agreement for details."</li>
								</ul>
							</p>
							<p>
								If the device cannot support the full DoD logon banner due to character limitations, the following text can be used:
							</p>
							<p>"I've read & consent to terms in IS user agreem't."
							</p>
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; color: red; padding-top:10px;">
                            {{ status2 | safe }}
                            <center>
								<form id="form-accept" name="form-accept" method="post">
                                <button id="accept" class="button">i agree</button
							</form>
						</center>
                        </td>
                    </tr>
                </tbody>
            </table>
    </div>
	<br><br>
 <style type="text/css" style="display: none !important;">
	* {
		margin: 0;
		padding: 0;
	}
	body {
		overflow-x: hidden;
	}
	#demo-top-bar {
		text-align: left;
		background: #222;
		position: relative;
		zoom: 1;
		width: 100% !important;
		z-index: 6000;
		padding: 20px 0 20px;
	}
	#demo-bar-inside {
		width: 960px;
		margin: 0 auto;
		position: relative;
		overflow: hidden;
	}
	#demo-bar-buttons {
		padding-top: 10px;
		float: right;
	}
	#demo-bar-buttons a {
		font-size: 12px;
		margin-left: 20px;
		color: white;
		margin: 2px 0;
		text-decoration: none;
		font: 14px "Lucida Grande", Sans-Serif !important;
	}
	#demo-bar-buttons a:hover,
	#demo-bar-buttons a:focus {
		text-decoration: underline;
	}
	#demo-bar-badge {
		display: inline-block;
		width: 302px;
		padding: 0 !important;
		margin: 0 !important;
		background-color: transparent !important;
	}
	#demo-bar-badge a {
		display: block;
		width: 100%;
		height: 38px;
		border-radius: 0;
		bottom: auto;
		margin: 0;
		background: url(/images/examples-logo.png) no-repeat;
		background-size: 100%;
		overflow: hidden;
		text-indent: -9999px;
	}
	#demo-bar-badge:before, #demo-bar-badge:after {
		display: none !important;
	}
	h1  {
		padding-bottom: 10px;
	}
</style>
<script>
	document.getElementById('accept').addEventListener('click', function () {
		const form = document.getElementById('form-accept');
		if (form.checkValidity()) {
			form.submit();
		} else {
			alert('Please fill out all required fields correctly.');
		}
	});
</script>
</body>

</html>