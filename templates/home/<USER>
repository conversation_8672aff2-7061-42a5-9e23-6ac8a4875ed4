{% extends "layouts/secure-base.html" %} {% block title %}: {{page_title}} {% endblock %}
  <!-- Specific Page CSS goes HERE  -->
  {% block stylesheets %}
  <link rel="stylesheet" href="{{ url_for('static', filename='stylesheets/modal.css') }}" />
  {% endblock stylesheets %} {% block content %}
  <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
  <style type="text/css">
    @media screen and (max-width: 700px) {
      .mobile-hide {
        display: none;
      }
    }
    input:required:invalid,
    input:focus:invalid {
      background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAeVJREFUeNqkU01oE1EQ/mazSTdRmqSxLVSJVKU9RYoHD8WfHr16kh5EFA8eSy6hXrwUPBSKZ6E9V1CU4tGf0DZWDEQrGkhprRDbCvlpavan3ezu+LLSUnADLZnHwHvzmJlvvpkhZkY7IqFNaTuAfPhhP/8Uo87SGSaDsP27hgYM/lUpy6lHdqsAtM+BPfvqKp3ufYKwcgmWCug6oKmrrG3PoaqngWjdd/922hOBs5C/jJA6x7AiUt8VYVUAVQXXShfIqCYRMZO8/N1N+B8H1sOUwivpSUSVCJ2MAjtVwBAIdv+AQkHQqbOgc+fBvorjyQENDcch16/BtkQdAlC4E6jrYHGgGU18Io3gmhzJuwub6/fQJYNi/YBpCifhbDaAPXFvCBVxXbvfbNGFeN8DkjogWAd8DljV3KRutcEAeHMN/HXZ4p9bhncJHCyhNx52R0Kv/XNuQvYBnM+CP7xddXL5KaJw0TMAF8qjnMvegeK/SLHubhpKDKIrJDlvXoMX3y9xcSMZyBQ+tpyk5hzsa2Ns7LGdfWdbL6fZvHn92d7dgROH/730YBLtiZmEdGPkFnhX4kxmjVe2xgPfCtrRd6GHRtEh9zsL8xVe+pwSzj+OtwvletZZ/wLeKD71L+ZeHHWZ/gowABkp7AwwnEjFAAAAAElFTkSuQmCC);
      background-position: right top;
      background-repeat: no-repeat;
    }
  
    input:required:valid {
      background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAepJREFUeNrEk79PFEEUx9/uDDd7v/AAQQnEQokmJCRGwc7/QeM/YGVxsZJQYI/EhCChICYmUJigNBSGzobQaI5SaYRw6imne0d2D/bYmZ3dGd+YQKEHYiyc5GUyb3Y+77vfeWNpreFfhvXfAWAAJtbKi7dff1rWK9vPHx3mThP2Iaipk5EzTg8Qmru38H7izmkFHAF4WH1R52654PR0Oamzj2dKxYt/Bbg1OPZuY3d9aU82VGem/5LtnJscLxWzfzRxaWNqWJP0XUadIbSzu5DuvUJpzq7sfYBKsP1GJeLB+PWpt8cCXm4+2+zLXx4guKiLXWA2Nc5ChOuacMEPv20FkT+dIawyenVi5VcAbcigWzXLeNiDRCdwId0LFm5IUMBIBgrp8wOEsFlfeCGm23/zoBZWn9a4C314A1nCoM1OAVccuGyCkPs/P+pIdVIOkG9pIh6YlyqCrwhRKD3GygK9PUBImIQQxRi4b2O+JcCLg8+e8NZiLVEygwCrWpYF0jQJziYU/ho2TUuCPTn8hHcQNuZy1/94sAMOzQHDeqaij7Cd8Dt8CatGhX3iWxgtFW/m29pnUjR7TSQcRCIAVW1FSr6KAVYdi+5Pj8yunviYHq7f72po3Y9dbi7CxzDO1+duzCXH9cEPAQYAhJELY/AqBtwAAAAASUVORK5CYII=);
      background-position: right top;
      background-repeat: no-repeat;
    }
    /*nav*/
    .nav{
        position: fixed;
        right: 0;
        top: 92px;
        width: 100%;
        height: calc(100vh - 70px);
        background-color: #cfcfcf;
        transform: translateX(100%);
        transition: transform 0.3s ease-in-out;
        background: rgba(0,0,0,0.5);
    }
    .nav-view{
        transform: translateX(0);
    }
    div .content{
        background-color: #ffffff; 
        width: 65%; 
        height: calc(100vh - 160px); 
        float: right; 
        border-left: 1px solid #000000; 
        padding: 15px;
        overflow-y: auto;
        z-index: -1;
    }
    @media screen and (max-width: 600px) {
        div .content {
            width: 90%; 
        }
    }
    meter::-webkit-meter-bar {background: #e6e6e9;} /*background color of bar*/
    meter::-webkit-meter-optimum-value {background: orange;}
    meter::-webkit-meter-suboptimum-value{background: green;}
    meter::-webkit-meter-even-less-good-value{background: red;}
 
  </style>
  <div id="main" class="abs">
    {% include 'includes/secure-header.html' %}
    <div class="abs header_lower chrome_{{chrome_style}}">
        <span class="float_left" style="padding-left: 5px;">{{page_title}}</span>
    </div>
    <div id="main_content" class="abs">
      <div id="main_content_inner">
        {{page_content | safe}}
        Home Page <a href="#" id="click-a">a</a> <a href="#" id="click-b">b</a> <span id="testing" name="holy crap this works">c</span>
        <div id="settings" class="nav">
            <div id="content" class="content">
                <a href="#" id="click-a" style="margin-top: 0; margin-right: 0; margin-bottom: 5px;" class="button float_right">Close</a>
                <table class="data">
                    <thead>
                        <th style="width: 75px;">Key</th>
                        <th>Value</th>
                    </thea>
                    <tbody>
                        <tr>
                            <td>Name</td>
                            <td><span id="test"></span></td>
                        </tr>
                        <tr>
                            <td>Data</td>
                            <td><input style="width: 98%;" id="test" name="test"></td>
                        </tr>
                    </tbody>
                </table>
                <a href="#" id="click-a" class="button float_right">Close</a>
            </div>
        </div>
        <div id="temp" class="nav">
            <div id="content" class="content">
                <a href="#" id="click-b" style="margin-top: 0; margin-right: 0" class="button float_right">Close</a>
                <span id="text"></span>
                <h4>Additional Details</h4>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Praesent suscipit odio ut congue finibus. Integer at interdum urna, non fermentum purus. Vivamus faucibus condimentum porttitor. Donec eget purus at enim aliquet commodo. Mauris eu sagittis lacus. Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Praesent nec lorem tincidunt, viverra neque in, auctor velit. Nunc elementum mollis lectus vitae tincidunt. Donec ut iaculis odio. Morbi sit amet lacinia ipsum, ornare rutrum purus. Nam eu velit dui. Cras placerat turpis at dolor ultrices, vel eleifend dui sollicitudin. In vel justo erat. Nam nec velit diam. Fusce blandit diam in erat interdum facilisis. In sagittis ligula sed ligula ornare, sed vehicula massa condimentum.
                <br><br>
                Cras ultrices purus a pulvinar porta. In at convallis mauris. In vitae iaculis leo. Aliquam placerat nibh libero, id iaculis velit sagittis sed. Proin aliquet urna metus, eu hendrerit augue pretium non. Interdum et malesuada fames ac ante ipsum primis in faucibus. Ut sit amet pulvinar enim, in eleifend urna. Cras ac odio vel dolor porttitor dapibus. Vivamus porta rutrum tellus quis mollis. Duis in dui ex. Sed et consequat mi, id scelerisque dolor. Duis rhoncus nec risus vel aliquet. Sed nec dolor eget justo consectetur pulvinar eget ac dui.
                <br><br>
                Fusce ut enim vel nunc iaculis posuere quis at velit. Proin vel accumsan nisi, aliquam tempor dui. Nullam porttitor mollis arcu sit amet laoreet. Nulla quis mattis purus, in interdum tellus. Donec id elit vitae velit venenatis lacinia vel ut lacus. Sed mi lorem, aliquet in tortor a, consequat venenatis est. Interdum et malesuada fames ac ante ipsum primis in faucibus. Sed et mauris vitae mauris fermentum posuere.
                <br><br>
                Sed tempor tincidunt purus, commodo suscipit est varius tempus. Phasellus eleifend sit amet libero ut fringilla. Vivamus eget laoreet nisi. Curabitur sapien est, venenatis et vehicula vitae, dictum vel massa. Nam rutrum ultricies ex sagittis fringilla. Curabitur quis neque sed dolor volutpat molestie facilisis ac mi. Sed elementum ullamcorper lacus, non vestibulum purus gravida eu. Donec non velit at risus ultricies placerat. Nulla tristique velit arcu, ac maximus mauris tristique semper. Pellentesque at justo ex. Suspendisse est massa, elementum a tincidunt nec, bibendum eu dui.
                <br><br>
                In porta dolor consequat lacus posuere, nec dapibus ipsum aliquet. In erat justo, vestibulum quis erat sit amet, placerat viverra metus. Nullam vitae sapien nibh. Quisque luctus pulvinar ligula, sagittis suscipit urna tristique vitae. Vestibulum quis lectus porttitor, facilisis ante id, tempus neque. Praesent cursus quam et sapien cursus auctor id ac massa. Morbi lorem turpis, consequat at ultricies vitae, viverra at lorem. In hac habitasse platea dictumst. Integer consectetur libero id eleifend varius. Ut in nulla libero. Aenean eros arcu, fermentum nec vehicula at, venenatis vel augue. Fusce posuere cursus lacus, mollis lacinia nisi blandit quis.
                <br><br>
            </div>
        </div>
        <br><br>
        <h1>System Drive Space Allocation</h2>
        <table class="data">
            <thead>
                <th> Mount </th>
                <th> Space </th>
            </thead>
            <tbody>
                {%for i, info in disk_info.iterrows()%}
                <tr><td>
                <label for="{{info['partition']}}">{{info['partition']}} ({{info['free']}}%)</label>
                </td><td style="width: 75%;">
                <meter id="{{info['partition']}}" style="width: 100%;" value="{{info['free']}}" min="0" low="70" high="90" max="100"></meter><br>
                </td></tr>
                {%endfor%}
            </tbody>
        </table>
        <table class="data" id="myTable">
            <thead>
                <th style="width: 100px;"> Date </th>
                <th style="width: 100px;"> Level </th>
                <th> Description </th>
            </thead>
            <tbody>
                <tr></tr>
            </tbody>
        </table>
        <table class="data" id="cvetable">
            <thead>
                <th style="width: 100px;"> Data </th>
                <th style="width: 100px;"> Level </th>
            </thead>
            <tbody>
                <tr id='cverow' name="cve-456">
                    <td>test item 1</td>
                    <td>High</td>
                </tr>
                <tr id='cverow' name="cve-123">
                    <td>test item 2</td>
                    <td>Low</td>
                </tr>
            </tbody>
        </table>
        </body>
        <script>
            $(document).ready(function(){
                $('#gauge svg text:first').attr('y', 40);
                $('a#click-a').click(function(){
                    var id = "";
                    $('input#test').val(id); 
                    $('span#test').text(id);
                    $('#settings').toggleClass('nav-view');
                });
                $('div#settings').click(function(){
                    $('#settings').toggleClass('nav-view');
                });
                $('a#click-b').click(function(){
                    $('span#text').html('<h1>test</h1>');
                    $('#temp').toggleClass('nav-view');
                });
                $('div#temp').click(function(){
                    $('#temp').toggleClass('nav-view');
                });
                $('span#testing').click(function(){
                    var id = $(this).attr('name');
                    $('input#test').val(id); 
                    $('span#test').text(id);
                    $('#settings').toggleClass('nav-view');
                });
                $('tr#cverow').click(function(){
                    var id = $(this).attr('name');
                    $('input#test').val(id); 
                    $('span#test').text(id);
                    $('#settings').toggleClass('nav-view');
                });
                $('button#form_submit').click(function(){
                    $.ajax({
                        url: 'https://api.example.com/endpoint',
                        method: 'POST',
                        data: JSON.stringify({ key1: 'value1', key2: 'value2' }),
                        contentType: 'application/json',
                        success: function(response) {
                            console.log('Data submitted successfully:', response);
                            $('#settings').toggleClass('nav-view');
                        },
                        error: function(xhr, status, error) {
                            console.error('Error submitting data:', error);
                        }
                    });
                });
                $('div.content').click(function(){return false;});
              });
            function addData() {
                const apiUrl = '/test';
                fetch(apiUrl)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(parsedResponse => {
                        parsedResponse.forEach((item, index) => {
                            addRow(item.date,item.level,item.description);
                        });
                    })
                    .catch(error => {
                        console.error('Error fetching or parsing data:', error);
                    });
                }
            function addRow(name,age,city) {
                if($("#myTable").html().includes(name)==false){
                    const urlParams = new URLSearchParams(window.location.search);
                    const myParam = urlParams.get('id');
                    let table = document.getElementById("myTable");
                    let row = table.insertRow(-1); // We are adding at the end
                    let c1 = row.insertCell(0);
                    let c2 = row.insertCell(1);
                    let c3 = row.insertCell(2);
                    c1.innerText = name
                    c2.innerText = age
                    c3.innerText = city
                }
            }
            function test() {
                /* addData() */
            }
            setInterval(function(){
                test()}, 5000)
        </script>
      </div>
    </div>
    <div class="abs footer_lower chrome_{{chrome_style}}">
      {% include 'includes/secure-footer.html' %}
    </div>
  </div>
  {% endblock content %}
  <!-- Specific Page JS goes HERE  -->
  {% block javascripts %}
    <script src="{{ url_for('static', filename='javascripts/modal.js') }}"></script>
    <script>Modal.initElements();</script>    
  {% endblock javascripts %}
  