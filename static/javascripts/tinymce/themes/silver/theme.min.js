/**
 * TinyMCE version 6.7.2 (2023-10-25)
 */
!function(){"use strict";const e=Object.getPrototypeOf,t=(e,t,o)=>{var n;return!!o(e,t.prototype)||(null===(n=e.constructor)||void 0===n?void 0:n.name)===t.name},o=e=>o=>(e=>{const o=typeof e;return null===e?"null":"object"===o&&Array.isArray(e)?"array":"object"===o&&t(e,String,((e,t)=>t.isPrototypeOf(e)))?"string":o})(o)===e,n=e=>t=>typeof t===e,s=e=>t=>e===t,r=o("string"),a=o("object"),i=o=>((o,n)=>a(o)&&t(o,n,((t,o)=>e(t)===o)))(o,Object),l=o("array"),c=s(null),d=n("boolean"),u=s(void 0),m=e=>null==e,g=e=>!m(e),p=n("function"),h=n("number"),f=(e,t)=>{if(l(e)){for(let o=0,n=e.length;o<n;++o)if(!t(e[o]))return!1;return!0}return!1},b=()=>{},v=e=>()=>e(),y=(e,t)=>(...o)=>e(t.apply(null,o)),x=e=>()=>e,w=e=>e,S=(e,t)=>e===t;function k(e,...t){return(...o)=>{const n=t.concat(o);return e.apply(null,n)}}const C=e=>t=>!e(t),O=e=>()=>{throw new Error(e)},_=e=>e(),T=x(!1),E=x(!0);class A{constructor(e,t){this.tag=e,this.value=t}static some(e){return new A(!0,e)}static none(){return A.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?A.some(e(this.value)):A.none()}bind(e){return this.tag?e(this.value):A.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:A.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return g(e)?A.some(e):A.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}A.singletonNone=new A(!1);const M=Array.prototype.slice,D=Array.prototype.indexOf,B=Array.prototype.push,F=(e,t)=>D.call(e,t),I=(e,t)=>{const o=F(e,t);return-1===o?A.none():A.some(o)},R=(e,t)=>F(e,t)>-1,N=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return!0;return!1},V=(e,t)=>{const o=[];for(let n=0;n<e;n++)o.push(t(n));return o},z=(e,t)=>{const o=[];for(let n=0;n<e.length;n+=t){const s=M.call(e,n,n+t);o.push(s)}return o},H=(e,t)=>{const o=e.length,n=new Array(o);for(let s=0;s<o;s++){const o=e[s];n[s]=t(o,s)}return n},L=(e,t)=>{for(let o=0,n=e.length;o<n;o++)t(e[o],o)},P=(e,t)=>{const o=[],n=[];for(let s=0,r=e.length;s<r;s++){const r=e[s];(t(r,s)?o:n).push(r)}return{pass:o,fail:n}},U=(e,t)=>{const o=[];for(let n=0,s=e.length;n<s;n++){const s=e[n];t(s,n)&&o.push(s)}return o},W=(e,t,o)=>(((e,t)=>{for(let o=e.length-1;o>=0;o--)t(e[o],o)})(e,((e,n)=>{o=t(o,e,n)})),o),j=(e,t,o)=>(L(e,((e,n)=>{o=t(o,e,n)})),o),G=(e,t)=>((e,t,o)=>{for(let n=0,s=e.length;n<s;n++){const s=e[n];if(t(s,n))return A.some(s);if(o(s,n))break}return A.none()})(e,t,T),$=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return A.some(o);return A.none()},q=e=>{const t=[];for(let o=0,n=e.length;o<n;++o){if(!l(e[o]))throw new Error("Arr.flatten item "+o+" was not an array, input: "+e);B.apply(t,e[o])}return t},X=(e,t)=>q(H(e,t)),Y=(e,t)=>{for(let o=0,n=e.length;o<n;++o)if(!0!==t(e[o],o))return!1;return!0},K=e=>{const t=M.call(e,0);return t.reverse(),t},J=(e,t)=>U(e,(e=>!R(t,e))),Z=(e,t)=>{const o={};for(let n=0,s=e.length;n<s;n++){const s=e[n];o[String(s)]=t(s,n)}return o},Q=e=>[e],ee=(e,t)=>{const o=M.call(e,0);return o.sort(t),o},te=(e,t)=>t>=0&&t<e.length?A.some(e[t]):A.none(),oe=e=>te(e,0),ne=e=>te(e,e.length-1),se=p(Array.from)?Array.from:e=>M.call(e),re=(e,t)=>{for(let o=0;o<e.length;o++){const n=t(e[o],o);if(n.isSome())return n}return A.none()},ae=Object.keys,ie=Object.hasOwnProperty,le=(e,t)=>{const o=ae(e);for(let n=0,s=o.length;n<s;n++){const s=o[n];t(e[s],s)}},ce=(e,t)=>de(e,((e,o)=>({k:o,v:t(e,o)}))),de=(e,t)=>{const o={};return le(e,((e,n)=>{const s=t(e,n);o[s.k]=s.v})),o},ue=e=>(t,o)=>{e[o]=t},me=(e,t,o,n)=>{le(e,((e,s)=>{(t(e,s)?o:n)(e,s)}))},ge=(e,t)=>{const o={};return me(e,t,ue(o),b),o},pe=(e,t)=>{const o=[];return le(e,((e,n)=>{o.push(t(e,n))})),o},he=(e,t)=>{const o=ae(e);for(let n=0,s=o.length;n<s;n++){const s=o[n],r=e[s];if(t(r,s,e))return A.some(r)}return A.none()},fe=e=>pe(e,w),be=(e,t)=>ve(e,t)?A.from(e[t]):A.none(),ve=(e,t)=>ie.call(e,t),ye=(e,t)=>ve(e,t)&&void 0!==e[t]&&null!==e[t],xe=(e,t,o=S)=>e.exists((e=>o(e,t))),we=e=>{const t=[],o=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(o);return t},Se=(e,t,o)=>e.isSome()&&t.isSome()?A.some(o(e.getOrDie(),t.getOrDie())):A.none(),ke=(e,t)=>null!=e?A.some(t(e)):A.none(),Ce=(e,t)=>e?A.some(t):A.none(),Oe=(e,t,o)=>""===t||e.length>=t.length&&e.substr(o,o+t.length)===t,_e=(e,t)=>Ee(e,t)?((e,t)=>e.substring(t))(e,t.length):e,Te=(e,t,o=0,n)=>{const s=e.indexOf(t,o);return-1!==s&&(!!u(n)||s+t.length<=n)},Ee=(e,t)=>Oe(e,t,0),Ae=(e,t)=>Oe(e,t,e.length-t.length),Me=(Ao=/^\s+|\s+$/g,e=>e.replace(Ao,"")),De=e=>e.length>0,Be=e=>void 0!==e.style&&p(e.style.getPropertyValue),Fe=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},Ie=(e,t)=>{const o=(t||document).createElement("div");if(o.innerHTML=e,!o.hasChildNodes()||o.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return Fe(o.childNodes[0])},Re=(e,t)=>{const o=(t||document).createElement(e);return Fe(o)},Ne=(e,t)=>{const o=(t||document).createTextNode(e);return Fe(o)},Ve=Fe,ze="undefined"!=typeof window?window:Function("return this;")(),He=(e,t)=>((e,t)=>{let o=null!=t?t:ze;for(let t=0;t<e.length&&null!=o;++t)o=o[e[t]];return o})(e.split("."),t),Le=Object.getPrototypeOf,Pe=e=>{const t=He("ownerDocument.defaultView",e);return a(e)&&((e=>((e,t)=>{const o=((e,t)=>He(e,t))(e,t);if(null==o)throw new Error(e+" not available on this browser");return o})("HTMLElement",e))(t).prototype.isPrototypeOf(e)||/^HTML\w*Element$/.test(Le(e).constructor.name))},Ue=e=>e.dom.nodeName.toLowerCase(),We=e=>t=>(e=>e.dom.nodeType)(t)===e,je=e=>Ge(e)&&Pe(e.dom),Ge=We(1),$e=We(3),qe=We(9),Xe=We(11),Ye=e=>t=>Ge(t)&&Ue(t)===e,Ke=(e,t)=>{const o=e.dom;if(1!==o.nodeType)return!1;{const e=o;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},Je=e=>1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount,Ze=(e,t)=>e.dom===t.dom,Qe=(e,t)=>{const o=e.dom,n=t.dom;return o!==n&&o.contains(n)},et=e=>Ve(e.dom.ownerDocument),tt=e=>qe(e)?e:et(e),ot=e=>Ve(tt(e).dom.documentElement),nt=e=>Ve(tt(e).dom.defaultView),st=e=>A.from(e.dom.parentNode).map(Ve),rt=e=>A.from(e.dom.parentElement).map(Ve),at=e=>A.from(e.dom.offsetParent).map(Ve),it=e=>H(e.dom.childNodes,Ve),lt=(e,t)=>{const o=e.dom.childNodes;return A.from(o[t]).map(Ve)},ct=e=>lt(e,0),dt=(e,t)=>({element:e,offset:t}),ut=(e,t)=>{const o=it(e);return o.length>0&&t<o.length?dt(o[t],0):dt(e,t)},mt=e=>Xe(e)&&g(e.dom.host),gt=p(Element.prototype.attachShadow)&&p(Node.prototype.getRootNode),pt=x(gt),ht=gt?e=>Ve(e.dom.getRootNode()):tt,ft=e=>mt(e)?e:Ve(tt(e).dom.body),bt=e=>{const t=ht(e);return mt(t)?A.some(t):A.none()},vt=e=>Ve(e.dom.host),yt=e=>{const t=$e(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const o=t.ownerDocument;return bt(Ve(t)).fold((()=>o.body.contains(t)),(n=yt,s=vt,e=>n(s(e))));var n,s},xt=()=>wt(Ve(document)),wt=e=>{const t=e.dom.body;if(null==t)throw new Error("Body is not available yet");return Ve(t)},St=(e,t,o)=>{if(!(r(o)||d(o)||h(o)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",o,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,o+"")},kt=(e,t,o)=>{St(e.dom,t,o)},Ct=(e,t)=>{const o=e.dom;le(t,((e,t)=>{St(o,t,e)}))},Ot=(e,t)=>{const o=e.dom.getAttribute(t);return null===o?void 0:o},_t=(e,t)=>A.from(Ot(e,t)),Tt=(e,t)=>{const o=e.dom;return!(!o||!o.hasAttribute)&&o.hasAttribute(t)},Et=(e,t)=>{e.dom.removeAttribute(t)},At=(e,t,o)=>{if(!r(o))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",o,":: Element ",e),new Error("CSS value must be a string: "+o);Be(e)&&e.style.setProperty(t,o)},Mt=(e,t)=>{Be(e)&&e.style.removeProperty(t)},Dt=(e,t,o)=>{const n=e.dom;At(n,t,o)},Bt=(e,t)=>{const o=e.dom;le(t,((e,t)=>{At(o,t,e)}))},Ft=(e,t)=>{const o=e.dom;le(t,((e,t)=>{e.fold((()=>{Mt(o,t)}),(e=>{At(o,t,e)}))}))},It=(e,t)=>{const o=e.dom,n=window.getComputedStyle(o).getPropertyValue(t);return""!==n||yt(e)?n:Rt(o,t)},Rt=(e,t)=>Be(e)?e.style.getPropertyValue(t):"",Nt=(e,t)=>{const o=e.dom,n=Rt(o,t);return A.from(n).filter((e=>e.length>0))},Vt=e=>{const t={},o=e.dom;if(Be(o))for(let e=0;e<o.style.length;e++){const n=o.style.item(e);t[n]=o.style[n]}return t},zt=(e,t,o)=>{const n=Re(e);return Dt(n,t,o),Nt(n,t).isSome()},Ht=(e,t)=>{const o=e.dom;Mt(o,t),xe(_t(e,"style").map(Me),"")&&Et(e,"style")},Lt=e=>e.dom.offsetWidth,Pt=(e,t)=>{const o=o=>{const n=t(o);if(n<=0||null===n){const t=It(o,e);return parseFloat(t)||0}return n},n=(e,t)=>j(t,((t,o)=>{const n=It(e,o),s=void 0===n?0:parseInt(n,10);return isNaN(s)?t:t+s}),0);return{set:(t,o)=>{if(!h(o)&&!o.match(/^[0-9]+$/))throw new Error(e+".set accepts only positive integer values. Value was "+o);const n=t.dom;Be(n)&&(n.style[e]=o+"px")},get:o,getOuter:o,aggregate:n,max:(e,t,o)=>{const s=n(e,o);return t>s?t-s:0}}},Ut=Pt("height",(e=>{const t=e.dom;return yt(e)?t.getBoundingClientRect().height:t.offsetHeight})),Wt=e=>Ut.get(e),jt=e=>Ut.getOuter(e),Gt=(e,t)=>({left:e,top:t,translate:(o,n)=>Gt(e+o,t+n)}),$t=Gt,qt=(e,t)=>void 0!==e?e:void 0!==t?t:0,Xt=e=>{const t=e.dom.ownerDocument,o=t.body,n=t.defaultView,s=t.documentElement;if(o===e.dom)return $t(o.offsetLeft,o.offsetTop);const r=qt(null==n?void 0:n.pageYOffset,s.scrollTop),a=qt(null==n?void 0:n.pageXOffset,s.scrollLeft),i=qt(s.clientTop,o.clientTop),l=qt(s.clientLeft,o.clientLeft);return Yt(e).translate(a-l,r-i)},Yt=e=>{const t=e.dom,o=t.ownerDocument.body;return o===t?$t(o.offsetLeft,o.offsetTop):yt(e)?(e=>{const t=e.getBoundingClientRect();return $t(t.left,t.top)})(t):$t(0,0)},Kt=Pt("width",(e=>e.dom.offsetWidth)),Jt=e=>Kt.get(e),Zt=e=>Kt.getOuter(e),Qt=e=>{let t,o=!1;return(...n)=>(o||(o=!0,t=e.apply(null,n)),t)},eo=()=>to(0,0),to=(e,t)=>({major:e,minor:t}),oo={nu:to,detect:(e,t)=>{const o=String(t).toLowerCase();return 0===e.length?eo():((e,t)=>{const o=((e,t)=>{for(let o=0;o<e.length;o++){const n=e[o];if(n.test(t))return n}})(e,t);if(!o)return{major:0,minor:0};const n=e=>Number(t.replace(o,"$"+e));return to(n(1),n(2))})(e,o)},unknown:eo},no=(e,t)=>{const o=String(t).toLowerCase();return G(e,(e=>e.search(o)))},so=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,ro=e=>t=>Te(t,e),ao=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>Te(e,"edge/")&&Te(e,"chrome")&&Te(e,"safari")&&Te(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,so],search:e=>Te(e,"chrome")&&!Te(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>Te(e,"msie")||Te(e,"trident")},{name:"Opera",versionRegexes:[so,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:ro("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:ro("firefox")},{name:"Safari",versionRegexes:[so,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(Te(e,"safari")||Te(e,"mobile/"))&&Te(e,"applewebkit")}],io=[{name:"Windows",search:ro("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>Te(e,"iphone")||Te(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:ro("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:ro("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:ro("linux"),versionRegexes:[]},{name:"Solaris",search:ro("sunos"),versionRegexes:[]},{name:"FreeBSD",search:ro("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:ro("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],lo={browsers:x(ao),oses:x(io)},co="Edge",uo="Chromium",mo="Opera",go="Firefox",po="Safari",ho=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isEdge:n(co),isChromium:n(uo),isIE:n("IE"),isOpera:n(mo),isFirefox:n(go),isSafari:n(po)}},fo=()=>ho({current:void 0,version:oo.unknown()}),bo=ho,vo=(x(co),x(uo),x("IE"),x(mo),x(go),x(po),"Windows"),yo="Android",xo="Linux",wo="macOS",So="Solaris",ko="FreeBSD",Co="ChromeOS",Oo=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isWindows:n(vo),isiOS:n("iOS"),isAndroid:n(yo),isMacOS:n(wo),isLinux:n(xo),isSolaris:n(So),isFreeBSD:n(ko),isChromeOS:n(Co)}},_o=()=>Oo({current:void 0,version:oo.unknown()}),To=Oo,Eo=(x(vo),x("iOS"),x(yo),x(xo),x(wo),x(So),x(ko),x(Co),e=>window.matchMedia(e).matches);var Ao;let Mo=Qt((()=>((e,t,o)=>{const n=lo.browsers(),s=lo.oses(),r=t.bind((e=>((e,t)=>re(t.brands,(t=>{const o=t.brand.toLowerCase();return G(e,(e=>{var t;return o===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:oo.nu(parseInt(t.version,10),0)})))})))(n,e))).orThunk((()=>((e,t)=>no(e,t).map((e=>{const o=oo.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(n,e))).fold(fo,bo),a=((e,t)=>no(e,t).map((e=>{const o=oo.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(s,e).fold(_o,To),i=((e,t,o,n)=>{const s=e.isiOS()&&!0===/ipad/i.test(o),r=e.isiOS()&&!s,a=e.isiOS()||e.isAndroid(),i=a||n("(pointer:coarse)"),l=s||!r&&a&&n("(min-device-width:768px)"),c=r||a&&!l,d=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(o),u=!c&&!l&&!d;return{isiPad:x(s),isiPhone:x(r),isTablet:x(l),isPhone:x(c),isTouch:x(i),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:x(d),isDesktop:x(u)}})(a,r,e,o);return{browser:r,os:a,deviceType:i}})(navigator.userAgent,A.from(navigator.userAgentData),Eo)));const Do=()=>Mo(),Bo=e=>{const t=Ve((e=>{if(pt()&&g(e.target)){const t=Ve(e.target);if(Ge(t)&&(e=>g(e.dom.shadowRoot))(t)&&e.composed&&e.composedPath){const t=e.composedPath();if(t)return oe(t)}}return A.from(e.target)})(e).getOr(e.target)),o=()=>e.stopPropagation(),n=()=>e.preventDefault(),s=y(n,o);return((e,t,o,n,s,r,a)=>({target:e,x:t,y:o,stop:n,prevent:s,kill:r,raw:a}))(t,e.clientX,e.clientY,o,n,s,e)},Fo=(e,t,o,n,s)=>{const r=((e,t)=>o=>{e(o)&&t(Bo(o))})(o,n);return e.dom.addEventListener(t,r,s),{unbind:k(Io,e,t,r,s)}},Io=(e,t,o,n)=>{e.dom.removeEventListener(t,o,n)},Ro=(e,t)=>{st(e).each((o=>{o.dom.insertBefore(t.dom,e.dom)}))},No=(e,t)=>{const o=(e=>A.from(e.dom.nextSibling).map(Ve))(e);o.fold((()=>{st(e).each((e=>{zo(e,t)}))}),(e=>{Ro(e,t)}))},Vo=(e,t)=>{ct(e).fold((()=>{zo(e,t)}),(o=>{e.dom.insertBefore(t.dom,o.dom)}))},zo=(e,t)=>{e.dom.appendChild(t.dom)},Ho=(e,t)=>{L(t,(t=>{zo(e,t)}))},Lo=e=>{e.dom.textContent="",L(it(e),(e=>{Po(e)}))},Po=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},Uo=e=>{const t=void 0!==e?e.dom:document,o=t.body.scrollLeft||t.documentElement.scrollLeft,n=t.body.scrollTop||t.documentElement.scrollTop;return $t(o,n)},Wo=(e,t,o)=>{const n=(void 0!==o?o.dom:document).defaultView;n&&n.scrollTo(e,t)},jo=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),Go=e=>{const t=void 0===e?window:e,o=t.document,n=Uo(Ve(o));return(e=>{const t=void 0===e?window:e;return Do().browser.isFirefox()?A.none():A.from(t.visualViewport)})(t).fold((()=>{const e=t.document.documentElement,o=e.clientWidth,s=e.clientHeight;return jo(n.left,n.top,o,s)}),(e=>jo(Math.max(e.pageLeft,n.left),Math.max(e.pageTop,n.top),e.width,e.height)))},$o=()=>Ve(document),qo=(e,t)=>e.view(t).fold(x([]),(t=>{const o=e.owner(t),n=qo(e,o);return[t].concat(n)}));var Xo=Object.freeze({__proto__:null,view:e=>{var t;return(e.dom===document?A.none():A.from(null===(t=e.dom.defaultView)||void 0===t?void 0:t.frameElement)).map(Ve)},owner:e=>et(e)});const Yo=e=>{const t=$o(),o=Uo(t),n=((e,t)=>{const o=t.owner(e),n=qo(t,o);return A.some(n)})(e,Xo);return n.fold(k(Xt,e),(t=>{const n=Yt(e),s=W(t,((e,t)=>{const o=Yt(t);return{left:e.left+o.left,top:e.top+o.top}}),{left:0,top:0});return $t(s.left+n.left+o.left,s.top+n.top+o.top)}))},Ko=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),Jo=e=>{const t=Xt(e),o=Zt(e),n=jt(e);return Ko(t.left,t.top,o,n)},Zo=e=>{const t=Yo(e),o=Zt(e),n=jt(e);return Ko(t.left,t.top,o,n)},Qo=(e,t)=>{const o=Math.max(e.x,t.x),n=Math.max(e.y,t.y),s=Math.min(e.right,t.right),r=Math.min(e.bottom,t.bottom);return Ko(o,n,s-o,r-n)},en=()=>Go(window);var tn=tinymce.util.Tools.resolve("tinymce.ThemeManager");const on=e=>{const t=t=>t(e),o=x(e),n=()=>s,s={tag:!0,inner:e,fold:(t,o)=>o(e),isValue:E,isError:T,map:t=>sn.value(t(e)),mapError:n,bind:t,exists:t,forall:t,getOr:o,or:n,getOrThunk:o,orThunk:n,getOrDie:o,each:t=>{t(e)},toOptional:()=>A.some(e)};return s},nn=e=>{const t=()=>o,o={tag:!1,inner:e,fold:(t,o)=>t(e),isValue:T,isError:E,map:t,mapError:t=>sn.error(t(e)),bind:t,exists:T,forall:E,getOr:w,or:w,getOrThunk:_,orThunk:_,getOrDie:O(String(e)),each:b,toOptional:A.none};return o},sn={value:on,error:nn,fromOption:(e,t)=>e.fold((()=>nn(t)),on)};var rn;!function(e){e[e.Error=0]="Error",e[e.Value=1]="Value"}(rn||(rn={}));const an=(e,t,o)=>e.stype===rn.Error?t(e.serror):o(e.svalue),ln=e=>({stype:rn.Value,svalue:e}),cn=e=>({stype:rn.Error,serror:e}),dn=ln,un=cn,mn=an,gn=(e,t,o,n)=>({tag:"field",key:e,newKey:t,presence:o,prop:n}),pn=(e,t,o)=>{switch(e.tag){case"field":return t(e.key,e.newKey,e.presence,e.prop);case"custom":return o(e.newKey,e.instantiator)}},hn=e=>(...t)=>{if(0===t.length)throw new Error("Can't merge zero objects");const o={};for(let n=0;n<t.length;n++){const s=t[n];for(const t in s)ve(s,t)&&(o[t]=e(o[t],s[t]))}return o},fn=hn(((e,t)=>i(e)&&i(t)?fn(e,t):t)),bn=hn(((e,t)=>t)),vn=e=>({tag:"defaultedThunk",process:e}),yn=e=>vn(x(e)),xn=e=>({tag:"mergeWithThunk",process:e}),wn=e=>{const t=(e=>{const t=[],o=[];return L(e,(e=>{an(e,(e=>o.push(e)),(e=>t.push(e)))})),{values:t,errors:o}})(e);return t.errors.length>0?(o=t.errors,y(un,q)(o)):dn(t.values);var o},Sn=e=>a(e)&&ae(e).length>100?" removed due to size":JSON.stringify(e,null,2),kn=(e,t)=>un([{path:e,getErrorInfo:t}]),Cn=e=>({extract:(t,o)=>((e,t)=>e.stype===rn.Error?t(e.serror):e)(e(o),(e=>((e,t)=>kn(e,x(t)))(t,e))),toString:x("val")}),On=Cn(dn),_n=(e,t,o,n)=>n(be(e,t).getOrThunk((()=>o(e)))),Tn=(e,t,o,n,s)=>{const r=e=>s.extract(t.concat([n]),e),a=e=>e.fold((()=>dn(A.none())),(e=>((e,t)=>e.stype===rn.Value?{stype:rn.Value,svalue:t(e.svalue)}:e)(s.extract(t.concat([n]),e),A.some)));switch(e.tag){case"required":return((e,t,o,n)=>be(t,o).fold((()=>((e,t,o)=>kn(e,(()=>'Could not find valid *required* value for "'+t+'" in '+Sn(o))))(e,o,t)),n))(t,o,n,r);case"defaultedThunk":return _n(o,n,e.process,r);case"option":return((e,t,o)=>o(be(e,t)))(o,n,a);case"defaultedOptionThunk":return((e,t,o,n)=>n(be(e,t).map((t=>!0===t?o(e):t))))(o,n,e.process,a);case"mergeWithThunk":return _n(o,n,x({}),(t=>{const n=fn(e.process(o),t);return r(n)}))}},En=e=>({extract:(t,o)=>e().extract(t,o),toString:()=>e().toString()}),An=e=>ae(ge(e,g)),Mn=e=>{const t=Dn(e),o=W(e,((e,t)=>pn(t,(t=>fn(e,{[t]:!0})),x(e))),{});return{extract:(e,n)=>{const s=d(n)?[]:An(n),r=U(s,(e=>!ye(o,e)));return 0===r.length?t.extract(e,n):((e,t)=>kn(e,(()=>"There are unsupported fields: ["+t.join(", ")+"] specified")))(e,r)},toString:t.toString}},Dn=e=>({extract:(t,o)=>((e,t,o)=>{const n={},s=[];for(const r of o)pn(r,((o,r,a,i)=>{const l=Tn(a,e,t,o,i);mn(l,(e=>{s.push(...e)}),(e=>{n[r]=e}))}),((e,o)=>{n[e]=o(t)}));return s.length>0?un(s):dn(n)})(t,o,e),toString:()=>{const t=H(e,(e=>pn(e,((e,t,o,n)=>e+" -> "+n.toString()),((e,t)=>"state("+e+")"))));return"obj{\n"+t.join("\n")+"}"}}),Bn=e=>({extract:(t,o)=>{const n=H(o,((o,n)=>e.extract(t.concat(["["+n+"]"]),o)));return wn(n)},toString:()=>"array("+e.toString()+")"}),Fn=(e,t)=>{const o=void 0!==t?t:w;return{extract:(t,n)=>{const s=[];for(const r of e){const e=r.extract(t,n);if(e.stype===rn.Value)return{stype:rn.Value,svalue:o(e.svalue)};s.push(e)}return wn(s)},toString:()=>"oneOf("+H(e,(e=>e.toString())).join(", ")+")"}},In=(e,t)=>({extract:(o,n)=>{const s=ae(n),r=((t,o)=>Bn(Cn(e)).extract(t,o))(o,s);return((e,t)=>e.stype===rn.Value?t(e.svalue):e)(r,(e=>{const s=H(e,(e=>gn(e,e,{tag:"required",process:{}},t)));return Dn(s).extract(o,n)}))},toString:()=>"setOf("+t.toString()+")"}),Rn=y(Bn,Dn),Nn=x(On),Vn=(e,t)=>Cn((o=>{const n=typeof o;return e(o)?dn(o):un(`Expected type: ${t} but got: ${n}`)})),zn=Vn(h,"number"),Hn=Vn(r,"string"),Ln=Vn(d,"boolean"),Pn=Vn(p,"function"),Un=e=>{if(Object(e)!==e)return!0;switch({}.toString.call(e).slice(8,-1)){case"Boolean":case"Number":case"String":case"Date":case"RegExp":case"Blob":case"FileList":case"ImageData":case"ImageBitmap":case"ArrayBuffer":return!0;case"Array":case"Object":return Object.keys(e).every((t=>Un(e[t])));default:return!1}},Wn=Cn((e=>Un(e)?dn(e):un("Expected value to be acceptable for sending via postMessage"))),jn=(e,t)=>({extract:(o,n)=>be(n,e).fold((()=>((e,t)=>kn(e,(()=>'Choice schema did not contain choice key: "'+t+'"')))(o,e)),(e=>((e,t,o,n)=>be(o,n).fold((()=>((e,t,o)=>kn(e,(()=>'The chosen schema: "'+o+'" did not exist in branches: '+Sn(t))))(e,o,n)),(o=>o.extract(e.concat(["branch: "+n]),t))))(o,n,t,e))),toString:()=>"chooseOn("+e+"). Possible values: "+ae(t)}),Gn=e=>Cn((t=>e(t).fold(un,dn))),$n=(e,t)=>In((t=>e(t).fold(cn,ln)),t),qn=(e,t,o)=>{return n=((e,t,o)=>((e,t)=>e.stype===rn.Error?{stype:rn.Error,serror:t(e.serror)}:e)(t.extract([e],o),(e=>({input:o,errors:e}))))(e,t,o),an(n,sn.error,sn.value);var n},Xn=e=>e.fold((e=>{throw new Error(Kn(e))}),w),Yn=(e,t,o)=>Xn(qn(e,t,o)),Kn=e=>"Errors: \n"+(e=>{const t=e.length>10?e.slice(0,10).concat([{path:[],getErrorInfo:x("... (only showing first ten failures)")}]):e;return H(t,(e=>"Failed path: ("+e.path.join(" > ")+")\n"+e.getErrorInfo()))})(e.errors).join("\n")+"\n\nInput object: "+Sn(e.input),Jn=(e,t)=>jn(e,ce(t,Dn)),Zn=(e,t)=>((e,t)=>{const o=Qt(t);return{extract:(e,t)=>o().extract(e,t),toString:()=>o().toString()}})(0,t),Qn=gn,es=(e,t)=>({tag:"custom",newKey:e,instantiator:t}),ts=e=>Gn((t=>R(e,t)?sn.value(t):sn.error(`Unsupported value: "${t}", choose one of "${e.join(", ")}".`))),os=e=>Qn(e,e,{tag:"required",process:{}},Nn()),ns=(e,t)=>Qn(e,e,{tag:"required",process:{}},t),ss=e=>ns(e,zn),rs=e=>ns(e,Hn),as=(e,t)=>Qn(e,e,{tag:"required",process:{}},ts(t)),is=e=>ns(e,Pn),ls=(e,t)=>Qn(e,e,{tag:"required",process:{}},Dn(t)),cs=(e,t)=>Qn(e,e,{tag:"required",process:{}},Rn(t)),ds=(e,t)=>Qn(e,e,{tag:"required",process:{}},Bn(t)),us=e=>Qn(e,e,{tag:"option",process:{}},Nn()),ms=(e,t)=>Qn(e,e,{tag:"option",process:{}},t),gs=e=>ms(e,zn),ps=e=>ms(e,Hn),hs=(e,t)=>ms(e,ts(t)),fs=e=>ms(e,Pn),bs=(e,t)=>ms(e,Bn(t)),vs=(e,t)=>ms(e,Dn(t)),ys=(e,t)=>Qn(e,e,yn(t),Nn()),xs=(e,t,o)=>Qn(e,e,yn(t),o),ws=(e,t)=>xs(e,t,zn),Ss=(e,t)=>xs(e,t,Hn),ks=(e,t,o)=>xs(e,t,ts(o)),Cs=(e,t)=>xs(e,t,Ln),Os=(e,t)=>xs(e,t,Pn),_s=(e,t,o)=>xs(e,t,Bn(o)),Ts=(e,t,o)=>xs(e,t,Dn(o)),Es=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},As=e=>{if(!l(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");const t=[],o={};return L(e,((n,s)=>{const r=ae(n);if(1!==r.length)throw new Error("one and only one name per case");const a=r[0],i=n[a];if(void 0!==o[a])throw new Error("duplicate key detected:"+a);if("cata"===a)throw new Error("cannot have a case named cata (sorry)");if(!l(i))throw new Error("case arguments must be an array");t.push(a),o[a]=(...o)=>{const n=o.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+a+". Expected "+i.length+" ("+i+"), got "+n);return{fold:(...t)=>{if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);return t[s].apply(null,o)},match:e=>{const n=ae(e);if(t.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+n.join(","));if(!Y(t,(e=>R(n,e))))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+t.join(", "));return e[a].apply(null,o)},log:e=>{console.log(e,{constructors:t,constructor:a,params:o})}}}})),o};As([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]);const Ms=(e,t)=>((e,t)=>({[e]:t}))(e,t),Ds=e=>(e=>{const t={};return L(e,(e=>{t[e.key]=e.value})),t})(e),Bs=e=>p(e)?e:T,Fs=(e,t,o)=>{let n=e.dom;const s=Bs(o);for(;n.parentNode;){n=n.parentNode;const e=Ve(n),o=t(e);if(o.isSome())return o;if(s(e))break}return A.none()},Is=(e,t,o)=>{const n=t(e),s=Bs(o);return n.orThunk((()=>s(e)?A.none():Fs(e,t,s)))},Rs=(e,t)=>Ze(e.element,t.event.target),Ns={can:E,abort:T,run:b},Vs=e=>{if(!ye(e,"can")&&!ye(e,"abort")&&!ye(e,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(e,null,2)+" does not have can, abort, or run!");return{...Ns,...e}},zs=x,Hs=zs("touchstart"),Ls=zs("touchmove"),Ps=zs("touchend"),Us=zs("touchcancel"),Ws=zs("mousedown"),js=zs("mousemove"),Gs=zs("mouseout"),$s=zs("mouseup"),qs=zs("mouseover"),Xs=zs("focusin"),Ys=zs("focusout"),Ks=zs("keydown"),Js=zs("keyup"),Zs=zs("input"),Qs=zs("change"),er=zs("click"),tr=zs("transitioncancel"),or=zs("transitionend"),nr=zs("transitionstart"),sr=zs("selectstart"),rr=e=>x("alloy."+e),ar={tap:rr("tap")},ir=rr("focus"),lr=rr("blur.post"),cr=rr("paste.post"),dr=rr("receive"),ur=rr("execute"),mr=rr("focus.item"),gr=ar.tap,pr=rr("longpress"),hr=rr("sandbox.close"),fr=rr("typeahead.cancel"),br=rr("system.init"),vr=rr("system.touchmove"),yr=rr("system.touchend"),xr=rr("system.scroll"),wr=rr("system.resize"),Sr=rr("system.attached"),kr=rr("system.detached"),Cr=rr("system.dismissRequested"),Or=rr("system.repositionRequested"),_r=rr("focusmanager.shifted"),Tr=rr("slotcontainer.visibility"),Er=rr("system.external.element.scroll"),Ar=rr("change.tab"),Mr=rr("dismiss.tab"),Dr=rr("highlight"),Br=rr("dehighlight"),Fr=(e,t)=>{Vr(e,e.element,t,{})},Ir=(e,t,o)=>{Vr(e,e.element,t,o)},Rr=e=>{Fr(e,ur())},Nr=(e,t,o)=>{Vr(e,t,o,{})},Vr=(e,t,o,n)=>{const s={target:t,...n};e.getSystem().triggerEvent(o,t,s)},zr=(e,t,o,n)=>{e.getSystem().triggerEvent(o,t,n.event)},Hr=e=>Ds(e),Lr=(e,t)=>({key:e,value:Vs({abort:t})}),Pr=e=>({key:e,value:Vs({run:(e,t)=>{t.event.prevent()}})}),Ur=(e,t)=>({key:e,value:Vs({run:t})}),Wr=(e,t,o)=>({key:e,value:Vs({run:(e,n)=>{t.apply(void 0,[e,n].concat(o))}})}),jr=e=>t=>({key:e,value:Vs({run:(e,o)=>{Rs(e,o)&&t(e,o)}})}),Gr=(e,t,o)=>((e,t)=>Ur(e,((o,n)=>{o.getSystem().getByUid(t).each((t=>{zr(t,t.element,e,n)}))})))(e,t.partUids[o]),$r=(e,t)=>Ur(e,((e,o)=>{const n=o.event,s=e.getSystem().getByDom(n.target).getOrThunk((()=>Is(n.target,(t=>e.getSystem().getByDom(t).toOptional()),T).getOr(e)));t(e,s,o)})),qr=e=>Ur(e,((e,t)=>{t.cut()})),Xr=e=>Ur(e,((e,t)=>{t.stop()})),Yr=(e,t)=>jr(e)(t),Kr=jr(Sr()),Jr=jr(kr()),Zr=jr(br()),Qr=(ra=ur(),e=>Ur(ra,e)),ea=e=>e.dom.innerHTML,ta=(e,t)=>{const o=et(e).dom,n=Ve(o.createDocumentFragment()),s=((e,t)=>{const o=(t||document).createElement("div");return o.innerHTML=e,it(Ve(o))})(t,o);Ho(n,s),Lo(e),zo(e,n)},oa=e=>mt(e)?"#shadow-root":(e=>{const t=Re("div"),o=Ve(e.dom.cloneNode(!0));return zo(t,o),ea(t)})((e=>((e,t)=>Ve(e.dom.cloneNode(!1)))(e))(e)),na=e=>oa(e),sa=Hr([((e,t)=>({key:e,value:Vs({can:(e,t)=>{const o=t.event,n=o.originator,s=o.target;return!((e,t,o)=>Ze(t,e.element)&&!Ze(t,o))(e,n,s)||(console.warn(ir()+" did not get interpreted by the desired target. \nOriginator: "+na(n)+"\nTarget: "+na(s)+"\nCheck the "+ir()+" event handlers"),!1)}})}))(ir())]);var ra,aa=Object.freeze({__proto__:null,events:sa});let ia=0;const la=e=>{const t=(new Date).getTime(),o=Math.floor(1e9*Math.random());return ia++,e+"_"+o+ia+String(t)},ca=x("alloy-id-"),da=x("data-alloy-id"),ua=ca(),ma=da(),ga=(e,t)=>{Object.defineProperty(e.dom,ma,{value:t,writable:!0})},pa=e=>{const t=Ge(e)?e.dom[ma]:null;return A.from(t)},ha=e=>la(e),fa=w,ba=e=>{const t=t=>`The component must be in a context to execute: ${t}`+(e?"\n"+na(e().element)+" is not in context.":""),o=e=>()=>{throw new Error(t(e))},n=e=>()=>{console.warn(t(e))};return{debugInfo:x("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn"),broadcastEvent:n("broadcastEvent"),build:o("build"),buildOrPatch:o("buildOrPatch"),addToWorld:o("addToWorld"),removeFromWorld:o("removeFromWorld"),addToGui:o("addToGui"),removeFromGui:o("removeFromGui"),getByUid:o("getByUid"),getByDom:o("getByDom"),isConnected:T}},va=ba(),ya=e=>H(e,(e=>Ae(e,"/*")?e.substring(0,e.length-2):e)),xa=(e,t)=>{const o=e.toString(),n=o.indexOf(")")+1,s=o.indexOf("("),r=o.substring(s+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:ya(r)}),e},wa=la("alloy-premade"),Sa=e=>(Object.defineProperty(e.element.dom,wa,{value:e.uid,writable:!0}),Ms(wa,e)),ka=e=>be(e,wa),Ca=e=>((e,t)=>{const o=t.toString(),n=o.indexOf(")")+1,s=o.indexOf("("),r=o.substring(s+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:"OVERRIDE",parameters:ya(r.slice(1))}),e})(((t,...o)=>e(t.getApis(),t,...o)),e),Oa={init:()=>_a({readState:x("No State required")})},_a=e=>e,Ta=(e,t)=>{const o={};return le(e,((e,n)=>{le(e,((e,s)=>{const r=be(o,s).getOr([]);o[s]=r.concat([t(n,e)])}))})),o},Ea=e=>({classes:u(e.classes)?[]:e.classes,attributes:u(e.attributes)?{}:e.attributes,styles:u(e.styles)?{}:e.styles}),Aa=e=>e.cHandler,Ma=(e,t)=>({name:e,handler:t}),Da=(e,t)=>{const o={};return L(e,(e=>{o[e.name()]=e.handlers(t)})),o},Ba=(e,t,o)=>{const n=t[o];return n?((e,t,o,n)=>{try{const s=ee(o,((o,s)=>{const r=o[t],a=s[t],i=n.indexOf(r),l=n.indexOf(a);if(-1===i)throw new Error("The ordering for "+e+" does not have an entry for "+r+".\nOrder specified: "+JSON.stringify(n,null,2));if(-1===l)throw new Error("The ordering for "+e+" does not have an entry for "+a+".\nOrder specified: "+JSON.stringify(n,null,2));return i<l?-1:l<i?1:0}));return sn.value(s)}catch(e){return sn.error([e])}})("Event: "+o,"name",e,n).map((e=>(e=>{const t=((e,t)=>(...t)=>j(e,((e,o)=>e&&(e=>e.can)(o).apply(void 0,t)),!0))(e),o=((e,t)=>(...t)=>j(e,((e,o)=>e||(e=>e.abort)(o).apply(void 0,t)),!1))(e);return{can:t,abort:o,run:(...t)=>{L(e,(e=>{e.run.apply(void 0,t)}))}}})(H(e,(e=>e.handler))))):((e,t)=>sn.error(["The event ("+e+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(H(t,(e=>e.name)),null,2)]))(o,e)},Fa=(e,t)=>((e,t)=>{const o=(e=>{const t=[],o=[];return L(e,(e=>{e.fold((e=>{t.push(e)}),(e=>{o.push(e)}))})),{errors:t,values:o}})(e);return o.errors.length>0?(n=o.errors,sn.error(q(n))):((e,t)=>0===e.length?sn.value(t):sn.value(fn(t,bn.apply(void 0,e))))(o.values,t);var n})(pe(e,((e,o)=>(1===e.length?sn.value(e[0].handler):Ba(e,t,o)).map((n=>{const s=(e=>{const t=(e=>p(e)?{can:E,abort:T,run:e}:e)(e);return(e,o,...n)=>{const s=[e,o].concat(n);t.abort.apply(void 0,s)?o.stop():t.can.apply(void 0,s)&&t.run.apply(void 0,s)}})(n),r=e.length>1?U(t[o],(t=>N(e,(e=>e.name===t)))).join(" > "):e[0].name;return Ms(o,((e,t)=>({handler:e,purpose:t}))(s,r))})))),{}),Ia="alloy.base.behaviour",Ra=Dn([Qn("dom","dom",{tag:"required",process:{}},Dn([os("tag"),ys("styles",{}),ys("classes",[]),ys("attributes",{}),us("value"),us("innerHtml")])),os("components"),os("uid"),ys("events",{}),ys("apis",{}),Qn("eventOrder","eventOrder",(ii={[ur()]:["disabling",Ia,"toggling","typeaheadevents"],[ir()]:[Ia,"focusing","keying"],[br()]:[Ia,"disabling","toggling","representing"],[Zs()]:[Ia,"representing","streaming","invalidating"],[kr()]:[Ia,"representing","item-events","tooltipping"],[Ws()]:["focusing",Ia,"item-type-events"],[Hs()]:["focusing",Ia,"item-type-events"],[qs()]:["item-type-events","tooltipping"],[dr()]:["receiving","reflecting","tooltipping"]},xn(x(ii))),Nn()),us("domModification")]),Na=e=>e.events,Va=(e,t)=>{const o=Ot(e,t);return void 0===o||""===o?[]:o.split(" ")},za=e=>void 0!==e.dom.classList,Ha=e=>Va(e,"class"),La=(e,t)=>{za(e)?e.dom.classList.add(t):((e,t)=>{((e,t,o)=>{const n=Va(e,t).concat([o]);kt(e,t,n.join(" "))})(e,"class",t)})(e,t)},Pa=(e,t)=>{za(e)?e.dom.classList.remove(t):((e,t)=>{((e,t,o)=>{const n=U(Va(e,t),(e=>e!==o));n.length>0?kt(e,t,n.join(" ")):Et(e,t)})(e,"class",t)})(e,t),(e=>{0===(za(e)?e.dom.classList:Ha(e)).length&&Et(e,"class")})(e)},Ua=(e,t)=>za(e)&&e.dom.classList.contains(t),Wa=(e,t)=>{L(t,(t=>{La(e,t)}))},ja=(e,t)=>{L(t,(t=>{Pa(e,t)}))},Ga=(e,t)=>Y(t,(t=>Ua(e,t))),$a=e=>e.dom.value,qa=(e,t)=>{if(void 0===t)throw new Error("Value.set was undefined");e.dom.value=t},Xa=(e,t,o)=>{o.fold((()=>zo(e,t)),(e=>{Ze(e,t)||(Ro(e,t),Po(e))}))},Ya=(e,t,o)=>{const n=H(t,o),s=it(e);return L(s.slice(n.length),Po),n},Ka=(e,t,o,n)=>{const s=lt(e,t),r=n(o,s),a=((e,t,o)=>lt(e,t).map((e=>{if(o.exists((t=>!Ze(t,e)))){const t=o.map(Ue).getOr("span"),n=Re(t);return Ro(e,n),n}return e})))(e,t,s);return Xa(e,r.element,a),r},Ja=(e,t)=>{const o=ae(e),n=ae(t),s=J(n,o),r=((e,o)=>{const n={},s={};return me(e,((e,o)=>!ve(t,o)||e!==t[o]),ue(n),ue(s)),{t:n,f:s}})(e).t;return{toRemove:s,toSet:r}},Za=(e,t)=>{const{class:o,style:n,...s}=(e=>j(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}))(t),{toSet:r,toRemove:a}=Ja(e.attributes,s),i=Vt(t),{toSet:l,toRemove:c}=Ja(e.styles,i),d=(e=>za(e)?(e=>{const t=e.dom.classList,o=new Array(t.length);for(let e=0;e<t.length;e++){const n=t.item(e);null!==n&&(o[e]=n)}return o})(e):Ha(e))(t),u=J(d,e.classes),m=J(e.classes,d);return L(a,(e=>Et(t,e))),Ct(t,r),Wa(t,m),ja(t,u),L(c,(e=>Ht(t,e))),Bt(t,l),e.innerHtml.fold((()=>{const o=e.domChildren;((e,t)=>{Ya(e,t,((t,o)=>{const n=lt(e,o);return Xa(e,t,n),t}))})(t,o)}),(e=>{ta(t,e)})),(()=>{const o=t,n=e.value.getOrUndefined();n!==$a(o)&&qa(o,null!=n?n:"")})(),t},Qa=e=>{const t=(e=>{const t=be(e,"behaviours").getOr({});return X(ae(t),(e=>{const o=t[e];return g(o)?[o.me]:[]}))})(e);return((e,t)=>((e,t)=>{const o=H(t,(e=>vs(e.name(),[os("config"),ys("state",Oa)]))),n=qn("component.behaviours",Dn(o),e.behaviours).fold((t=>{throw new Error(Kn(t)+"\nComplete spec:\n"+JSON.stringify(e,null,2))}),w);return{list:t,data:ce(n,(e=>{const t=e.map((e=>({config:e.config,state:e.state.init(e.config)})));return x(t)}))}})(e,t))(e,t)},ei=(e,t)=>{const o=()=>m,n=Es(va),s=Xn((e=>qn("custom.definition",Ra,e))(e)),r=Qa(e),a=(e=>e.list)(r),i=(e=>e.data)(r),l=((e,t,o)=>{const n={...(s=e).dom,uid:s.uid,domChildren:H(s.components,(e=>e.element))};var s;const r=(e=>e.domModification.fold((()=>Ea({})),Ea))(e),a={"alloy.base.modification":r},i=t.length>0?((e,t,o,n)=>{const s={...t};L(o,(t=>{s[t.name()]=t.exhibit(e,n)}));const r=Ta(s,((e,t)=>({name:e,modification:t}))),a=e=>W(e,((e,t)=>({...t.modification,...e})),{}),i=W(r.classes,((e,t)=>t.modification.concat(e)),[]),l=a(r.attributes),c=a(r.styles);return Ea({classes:i,attributes:l,styles:c})})(o,a,t,n):r;return l=n,c=i,{...l,attributes:{...l.attributes,...c.attributes},styles:{...l.styles,...c.styles},classes:l.classes.concat(c.classes)};var l,c})(s,a,i),c=((e,t)=>{const o=t.filter((t=>Ue(t)===e.tag&&!(e=>e.innerHtml.isSome()&&e.domChildren.length>0)(e)&&!(e=>ve(e.dom,wa))(t))).bind((t=>((e,t)=>{try{const o=Za(e,t);return A.some(o)}catch(e){return A.none()}})(e,t))).getOrThunk((()=>(e=>{const t=Re(e.tag);Ct(t,e.attributes),Wa(t,e.classes),Bt(t,e.styles),e.innerHtml.each((e=>ta(t,e)));const o=e.domChildren;return Ho(t,o),e.value.each((e=>{qa(t,e)})),t})(e)));return ga(o,e.uid),o})(l,t),d=((e,t,o)=>{const n={"alloy.base.behaviour":Na(e)};return((e,t,o,n)=>{const s=((e,t,o)=>{const n={...o,...Da(t,e)};return Ta(n,Ma)})(e,o,n);return Fa(s,t)})(o,e.eventOrder,t,n).getOrDie()})(s,a,i),u=Es(s.components),m={uid:e.uid,getSystem:n.get,config:t=>{const o=i;return(p(o[t.name()])?o[t.name()]:()=>{throw new Error("Could not find "+t.name()+" in "+JSON.stringify(e,null,2))})()},hasConfigured:e=>p(i[e.name()]),spec:e,readState:e=>i[e]().map((e=>e.state.readState())).getOr("not enabled"),getApis:()=>s.apis,connect:e=>{n.set(e)},disconnect:()=>{n.set(ba(o))},element:c,syncComponents:()=>{const e=it(c),t=X(e,(e=>n.get().getByDom(e).fold((()=>[]),Q)));u.set(t)},components:u.get,events:d};return m},ti=e=>{const t=Ne(e);return oi({element:t})},oi=e=>{const t=Yn("external.component",Mn([os("element"),us("uid")]),e),o=Es(ba()),n=t.uid.getOrThunk((()=>ha("external")));ga(t.element,n);const s={uid:n,getSystem:o.get,config:A.none,hasConfigured:T,connect:e=>{o.set(e)},disconnect:()=>{o.set(ba((()=>s)))},getApis:()=>({}),element:t.element,spec:e,readState:x("No state"),syncComponents:b,components:x([]),events:{}};return Sa(s)},ni=ha,si=(e,t)=>ka(e).getOrThunk((()=>((e,t)=>{const{events:o,...n}=fa(e),s=((e,t)=>{const o=be(e,"components").getOr([]);return t.fold((()=>H(o,ri)),(e=>H(o,((t,o)=>si(t,lt(e,o))))))})(n,t),r={...n,events:{...aa,...o},components:s};return sn.value(ei(r,t))})((e=>ve(e,"uid"))(e)?e:{uid:ni(""),...e},t).getOrDie())),ri=e=>si(e,A.none()),ai=Sa;var ii,li=(e,t,o,n,s)=>e(o,n)?A.some(o):p(s)&&s(o)?A.none():t(o,n,s);const ci=(e,t,o)=>{let n=e.dom;const s=p(o)?o:T;for(;n.parentNode;){n=n.parentNode;const e=Ve(n);if(t(e))return A.some(e);if(s(e))break}return A.none()},di=(e,t,o)=>li(((e,t)=>t(e)),ci,e,t,o),ui=(e,t,o)=>di(e,t,o).isSome(),mi=(e,t,o)=>ci(e,(e=>Ke(e,t)),o),gi=(e,t)=>((e,o)=>G(e.dom.childNodes,(e=>{return o=Ve(e),Ke(o,t);var o})).map(Ve))(e),pi=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return Je(o)?A.none():A.from(o.querySelector(e)).map(Ve)})(t,e),hi=(e,t,o)=>li(((e,t)=>Ke(e,t)),mi,e,t,o),fi="aria-controls",bi=()=>{const e=la(fi);return{id:e,link:t=>{kt(t,fi,e)},unlink:e=>{Et(e,fi)}}},vi=(e,t)=>ui(t,(t=>Ze(t,e.element)),T)||((e,t)=>(e=>di(e,(e=>{if(!Ge(e))return!1;const t=Ot(e,"id");return void 0!==t&&t.indexOf(fi)>-1})).bind((e=>{const t=Ot(e,"id"),o=ht(e);return pi(o,`[${fi}="${t}"]`)})))(t).exists((t=>vi(e,t))))(e,t);var yi;!function(e){e[e.STOP=0]="STOP",e[e.NORMAL=1]="NORMAL",e[e.LOGGING=2]="LOGGING"}(yi||(yi={}));const xi=Es({}),wi=["alloy/data/Fields","alloy/debugging/Debugging"],Si=(e,t,o)=>((e,t,o)=>{switch(be(xi.get(),e).orThunk((()=>{const t=ae(xi.get());return re(t,(t=>e.indexOf(t)>-1?A.some(xi.get()[t]):A.none()))})).getOr(yi.NORMAL)){case yi.NORMAL:return o(ki());case yi.LOGGING:{const n=((e,t)=>{const o=[],n=(new Date).getTime();return{logEventCut:(e,t,n)=>{o.push({outcome:"cut",target:t,purpose:n})},logEventStopped:(e,t,n)=>{o.push({outcome:"stopped",target:t,purpose:n})},logNoParent:(e,t,n)=>{o.push({outcome:"no-parent",target:t,purpose:n})},logEventNoHandlers:(e,t)=>{o.push({outcome:"no-handlers-left",target:t})},logEventResponse:(e,t,n)=>{o.push({outcome:"response",purpose:n,target:t})},write:()=>{const s=(new Date).getTime();R(["mousemove","mouseover","mouseout",br()],e)||console.log(e,{event:e,time:s-n,target:t.dom,sequence:H(o,(e=>R(["cut","stopped","response"],e.outcome)?"{"+e.purpose+"} "+e.outcome+" at ("+na(e.target)+")":e.outcome))})}}})(e,t),s=o(n);return n.write(),s}case yi.STOP:return!0}})(e,t,o),ki=x({logEventCut:b,logEventStopped:b,logNoParent:b,logEventNoHandlers:b,logEventResponse:b,write:b}),Ci=x([os("menu"),os("selectedMenu")]),Oi=x([os("item"),os("selectedItem")]);x(Dn(Oi().concat(Ci())));const _i=x(Dn(Oi())),Ti=ls("initSize",[os("numColumns"),os("numRows")]),Ei=()=>ls("markers",[os("backgroundMenu")].concat(Ci()).concat(Oi())),Ai=e=>ls("markers",H(e,os)),Mi=(e,t,o)=>((()=>{const e=new Error;if(void 0!==e.stack){const t=e.stack.split("\n");G(t,(e=>e.indexOf("alloy")>0&&!N(wi,(t=>e.indexOf(t)>-1)))).getOr("unknown")}})(),Qn(t,t,o,Gn((e=>sn.value(((...t)=>e.apply(void 0,t))))))),Di=e=>Mi(0,e,yn(b)),Bi=e=>Mi(0,e,yn(A.none)),Fi=e=>Mi(0,e,{tag:"required",process:{}}),Ii=e=>Mi(0,e,{tag:"required",process:{}}),Ri=(e,t)=>es(e,x(t)),Ni=e=>es(e,w),Vi=x(Ti),zi=(e,t,o,n,s,r,a,i=!1)=>({x:e,y:t,bubble:o,direction:n,placement:s,restriction:r,label:`${a}-${s}`,alwaysFit:i}),Hi=As([{southeast:[]},{southwest:[]},{northeast:[]},{northwest:[]},{south:[]},{north:[]},{east:[]},{west:[]}]),Li=Hi.southeast,Pi=Hi.southwest,Ui=Hi.northeast,Wi=Hi.northwest,ji=Hi.south,Gi=Hi.north,$i=Hi.east,qi=Hi.west,Xi=(e,t,o,n)=>{const s=e+t;return s>n?o:s<o?n:s},Yi=(e,t,o)=>Math.min(Math.max(e,t),o),Ki=(e,t)=>Z(["left","right","top","bottom"],(o=>be(t,o).map((t=>((e,t)=>{switch(t){case 1:return e.x;case 0:return e.x+e.width;case 2:return e.y;case 3:return e.y+e.height}})(e,t))))),Ji="layout",Zi=e=>e.x,Qi=(e,t)=>e.x+e.width/2-t.width/2,el=(e,t)=>e.x+e.width-t.width,tl=(e,t)=>e.y-t.height,ol=e=>e.y+e.height,nl=(e,t)=>e.y+e.height/2-t.height/2,sl=(e,t,o)=>zi(Zi(e),ol(e),o.southeast(),Li(),"southeast",Ki(e,{left:1,top:3}),Ji),rl=(e,t,o)=>zi(el(e,t),ol(e),o.southwest(),Pi(),"southwest",Ki(e,{right:0,top:3}),Ji),al=(e,t,o)=>zi(Zi(e),tl(e,t),o.northeast(),Ui(),"northeast",Ki(e,{left:1,bottom:2}),Ji),il=(e,t,o)=>zi(el(e,t),tl(e,t),o.northwest(),Wi(),"northwest",Ki(e,{right:0,bottom:2}),Ji),ll=(e,t,o)=>zi(Qi(e,t),tl(e,t),o.north(),Gi(),"north",Ki(e,{bottom:2}),Ji),cl=(e,t,o)=>zi(Qi(e,t),ol(e),o.south(),ji(),"south",Ki(e,{top:3}),Ji),dl=(e,t,o)=>zi((e=>e.x+e.width)(e),nl(e,t),o.east(),$i(),"east",Ki(e,{left:0}),Ji),ul=(e,t,o)=>zi(((e,t)=>e.x-t.width)(e,t),nl(e,t),o.west(),qi(),"west",Ki(e,{right:1}),Ji),ml=()=>[sl,rl,al,il,cl,ll,dl,ul],gl=()=>[rl,sl,il,al,cl,ll,dl,ul],pl=()=>[al,il,sl,rl,ll,cl],hl=()=>[il,al,rl,sl,ll,cl],fl=()=>[sl,rl,al,il,cl,ll],bl=()=>[rl,sl,il,al,cl,ll];var vl=Object.freeze({__proto__:null,events:e=>Hr([Ur(dr(),((t,o)=>{const n=e.channels,s=ae(n),r=o,a=((e,t)=>t.universal?e:U(e,(e=>R(t.channels,e))))(s,r);L(a,(e=>{const o=n[e],s=o.schema,a=Yn("channel["+e+"] data\nReceiver: "+na(t.element),s,r.data);o.onReceive(t,a)}))}))])}),yl=[ns("channels",$n(sn.value,Mn([Fi("onReceive"),ys("schema",Nn())])))];const xl=(e,t,o)=>Zr(((n,s)=>{o(n,e,t)})),wl=e=>({key:e,value:void 0}),Sl=(e,t,o,n,s,r,a)=>{const i=e=>ye(e,o)?e[o]():A.none(),l=ce(s,((e,t)=>((e,t,o)=>((e,t,o)=>{const n=o.toString(),s=n.indexOf(")")+1,r=n.indexOf("("),a=n.substring(r+1,s-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:ya(a.slice(0,1).concat(a.slice(3)))}),e})(((n,...s)=>{const r=[n].concat(s);return n.config({name:x(e)}).fold((()=>{throw new Error("We could not find any behaviour configuration for: "+e+". Using API: "+o)}),(e=>{const o=Array.prototype.slice.call(r,1);return t.apply(void 0,[n,e.config,e.state].concat(o))}))}),o,t))(o,e,t))),c={...ce(r,((e,t)=>xa(e,t))),...l,revoke:k(wl,o),config:t=>{const n=Yn(o+"-config",e,t);return{key:o,value:{config:n,me:c,configAsRaw:Qt((()=>Yn(o+"-config",e,t))),initialConfig:t,state:a}}},schema:x(t),exhibit:(e,t)=>Se(i(e),be(n,"exhibit"),((e,o)=>o(t,e.config,e.state))).getOrThunk((()=>Ea({}))),name:x(o),handlers:e=>i(e).map((e=>be(n,"events").getOr((()=>({})))(e.config,e.state))).getOr({})};return c},kl=e=>Ds(e),Cl=Mn([os("fields"),os("name"),ys("active",{}),ys("apis",{}),ys("state",Oa),ys("extra",{})]),Ol=e=>{const t=Yn("Creating behaviour: "+e.name,Cl,e);return((e,t,o,n,s,r)=>{const a=Mn(e),i=vs(t,[("config",l=e,ms("config",Mn(l)))]);var l;return Sl(a,i,t,o,n,s,r)})(t.fields,t.name,t.active,t.apis,t.extra,t.state)},_l=Mn([os("branchKey"),os("branches"),os("name"),ys("active",{}),ys("apis",{}),ys("state",Oa),ys("extra",{})]),Tl=e=>{const t=Yn("Creating behaviour: "+e.name,_l,e);return((e,t,o,n,s,r)=>{const a=e,i=vs(t,[ms("config",e)]);return Sl(a,i,t,o,n,s,r)})(Jn(t.branchKey,t.branches),t.name,t.active,t.apis,t.extra,t.state)},El=x(void 0),Al=Ol({fields:yl,name:"receiving",active:vl});var Ml=Object.freeze({__proto__:null,exhibit:(e,t)=>Ea({classes:[],styles:t.useFixed()?{}:{position:"relative"}})});const Dl=e=>e.dom.focus(),Bl=e=>e.dom.blur(),Fl=e=>{const t=ht(e).dom;return e.dom===t.activeElement},Il=(e=$o())=>A.from(e.dom.activeElement).map(Ve),Rl=e=>Il(ht(e)).filter((t=>e.dom.contains(t.dom))),Nl=(e,t)=>{const o=ht(t),n=Il(o).bind((e=>{const o=t=>Ze(e,t);return o(t)?A.some(t):((e,t)=>{const o=e=>{for(let n=0;n<e.childNodes.length;n++){const s=Ve(e.childNodes[n]);if(t(s))return A.some(s);const r=o(e.childNodes[n]);if(r.isSome())return r}return A.none()};return o(e.dom)})(t,o)})),s=e(t);return n.each((e=>{Il(o).filter((t=>Ze(t,e))).fold((()=>{Dl(e)}),b)})),s},Vl=(e,t,o,n,s)=>{const r=e=>e+"px";return{position:e,left:t.map(r),top:o.map(r),right:n.map(r),bottom:s.map(r)}},zl=(e,t)=>{Ft(e,(e=>({...e,position:A.some(e.position)}))(t))},Hl=As([{none:[]},{relative:["x","y","width","height"]},{fixed:["x","y","width","height"]}]),Ll=(e,t,o,n,s,r)=>{const a=t.rect,i=a.x-o,l=a.y-n,c=s-(i+a.width),d=r-(l+a.height),u=A.some(i),m=A.some(l),g=A.some(c),p=A.some(d),h=A.none();return t.direction.fold((()=>Vl(e,u,m,h,h)),(()=>Vl(e,h,m,g,h)),(()=>Vl(e,u,h,h,p)),(()=>Vl(e,h,h,g,p)),(()=>Vl(e,u,m,h,h)),(()=>Vl(e,u,h,h,p)),(()=>Vl(e,u,m,h,h)),(()=>Vl(e,h,m,g,h)))},Pl=(e,t)=>e.fold((()=>{const e=t.rect;return Vl("absolute",A.some(e.x),A.some(e.y),A.none(),A.none())}),((e,o,n,s)=>Ll("absolute",t,e,o,n,s)),((e,o,n,s)=>Ll("fixed",t,e,o,n,s))),Ul=(e,t)=>{const o=k(Yo,t),n=e.fold(o,o,(()=>{const e=Uo();return Yo(t).translate(-e.left,-e.top)})),s=Zt(t),r=jt(t);return Ko(n.left,n.top,s,r)},Wl=(e,t)=>t.fold((()=>e.fold(en,en,Ko)),(t=>e.fold(x(t),x(t),(()=>{const o=jl(e,t.x,t.y);return Ko(o.left,o.top,t.width,t.height)})))),jl=(e,t,o)=>{const n=$t(t,o);return e.fold(x(n),x(n),(()=>{const e=Uo();return n.translate(-e.left,-e.top)}))};Hl.none;const Gl=Hl.relative,$l=Hl.fixed,ql="data-alloy-placement",Xl=e=>_t(e,ql),Yl=As([{fit:["reposition"]},{nofit:["reposition","visibleW","visibleH","isVisible"]}]),Kl=(e,t,o,n)=>{const s=e.bubble,r=s.offset,a=((e,t,o)=>{const n=(n,s)=>t[n].map((t=>{const r="top"===n||"bottom"===n,a=r?o.top:o.left,i=("left"===n||"top"===n?Math.max:Math.min)(t,s)+a;return r?Yi(i,e.y,e.bottom):Yi(i,e.x,e.right)})).getOr(s),s=n("left",e.x),r=n("top",e.y),a=n("right",e.right),i=n("bottom",e.bottom);return Ko(s,r,a-s,i-r)})(n,e.restriction,r),i=e.x+r.left,l=e.y+r.top,c=Ko(i,l,t,o),{originInBounds:d,sizeInBounds:u,visibleW:m,visibleH:g}=((e,t)=>{const{x:o,y:n,right:s,bottom:r}=t,{x:a,y:i,right:l,bottom:c,width:d,height:u}=e;return{originInBounds:a>=o&&a<=s&&i>=n&&i<=r,sizeInBounds:l<=s&&l>=o&&c<=r&&c>=n,visibleW:Math.min(d,a>=o?s-a:l-o),visibleH:Math.min(u,i>=n?r-i:c-n)}})(c,a),p=d&&u,h=p?c:((e,t)=>{const{x:o,y:n,right:s,bottom:r}=t,{x:a,y:i,width:l,height:c}=e,d=Math.max(o,s-l),u=Math.max(n,r-c),m=Yi(a,o,d),g=Yi(i,n,u),p=Math.min(m+l,s)-m,h=Math.min(g+c,r)-g;return Ko(m,g,p,h)})(c,a),f=h.width>0&&h.height>0,{maxWidth:b,maxHeight:v}=((e,t,o)=>{const n=x(t.bottom-o.y),s=x(o.bottom-t.y),r=((e,t,o,n)=>e.fold(t,t,n,n,t,n,o,o))(e,s,s,n),a=x(t.right-o.x),i=x(o.right-t.x),l=((e,t,o,n)=>e.fold(t,n,t,n,o,o,t,n))(e,i,i,a);return{maxWidth:l,maxHeight:r}})(e.direction,h,n),y={rect:h,maxHeight:v,maxWidth:b,direction:e.direction,placement:e.placement,classes:{on:s.classesOn,off:s.classesOff},layout:e.label,testY:l};return p||e.alwaysFit?Yl.fit(y):Yl.nofit(y,m,g,f)},Jl=e=>{const t=Es(A.none()),o=()=>t.get().each(e);return{clear:()=>{o(),t.set(A.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{o(),t.set(A.some(e))}}},Zl=()=>Jl((e=>e.unbind())),Ql=()=>{const e=Jl(b);return{...e,on:t=>e.get().each(t)}},ec=E,tc=(e,t,o)=>((e,t,o,n)=>Fo(e,t,o,n,!1))(e,t,ec,o),oc=(e,t,o)=>((e,t,o,n)=>Fo(e,t,o,n,!0))(e,t,ec,o),nc=Bo,sc=["top","bottom","right","left"],rc="data-alloy-transition-timer",ac=(e,t,o,n,s,a)=>{const i=((e,t,o)=>o.exists((o=>{const n=e.mode;return"all"===n||o[n]!==t[n]})))(n,s,a);if(i||((e,t)=>Ga(e,t.classes))(e,n)){Dt(e,"position",o.position);const a=Ul(t,e),l=Pl(t,{...s,rect:a}),c=Z(sc,(e=>l[e]));((e,t)=>{const o=e=>parseFloat(e).toFixed(3);return he(t,((t,n)=>!((e,t,o=S)=>Se(e,t,o).getOr(e.isNone()&&t.isNone()))(e[n].map(o),t.map(o)))).isSome()})(o,c)&&(Ft(e,c),i&&((e,t)=>{Wa(e,t.classes),_t(e,rc).each((t=>{clearTimeout(parseInt(t,10)),Et(e,rc)})),((e,t)=>{const o=Zl(),n=Zl();let s;const a=t=>{var o;const n=null!==(o=t.raw.pseudoElement)&&void 0!==o?o:"";return Ze(t.target,e)&&!De(n)&&R(sc,t.raw.propertyName)},i=r=>{if(m(r)||a(r)){o.clear(),n.clear();const a=null==r?void 0:r.raw.type;(m(a)||a===or())&&(clearTimeout(s),Et(e,rc),ja(e,t.classes))}},l=tc(e,nr(),(t=>{a(t)&&(l.unbind(),o.set(tc(e,or(),i)),n.set(tc(e,tr(),i)))})),c=(e=>{const t=t=>{const o=It(e,t).split(/\s*,\s*/);return U(o,De)},o=e=>{if(r(e)&&/^[\d.]+/.test(e)){const t=parseFloat(e);return Ae(e,"ms")?t:1e3*t}return 0},n=t("transition-delay"),s=t("transition-duration");return j(s,((e,t,s)=>{const r=o(n[s])+o(t);return Math.max(e,r)}),0)})(e);requestAnimationFrame((()=>{s=setTimeout(i,c+17),kt(e,rc,s)}))})(e,t)})(e,n),Lt(e))}else ja(e,n.classes)},ic=(e,t)=>{((e,t)=>{const o=Ut.max(e,t,["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"]);Dt(e,"max-height",o+"px")})(e,Math.floor(t))},lc=x(((e,t)=>{ic(e,t),Bt(e,{"overflow-x":"hidden","overflow-y":"auto"})})),cc=x(((e,t)=>{ic(e,t)})),dc=(e,t,o)=>void 0===e[t]?o:e[t],uc=(e,t,o,n)=>{const s=((e,t,o,n)=>{Ht(t,"max-height"),Ht(t,"max-width");const s={width:Zt(r=t),height:jt(r)};var r;return((e,t,o,n,s,r)=>{const a=n.width,i=n.height,l=(t,l,c,d,u)=>{const m=t(o,n,s,e,r),g=Kl(m,a,i,r);return g.fold(x(g),((e,t,o,n)=>(u===n?o>d||t>c:!u&&n)?g:Yl.nofit(l,c,d,u)))};return j(t,((e,t)=>{const o=k(l,t);return e.fold(x(e),o)}),Yl.nofit({rect:o,maxHeight:n.height,maxWidth:n.width,direction:Li(),placement:"southeast",classes:{on:[],off:[]},layout:"none",testY:o.y},-1,-1,!1)).fold(w,w)})(t,n.preference,e,s,o,n.bounds)})(e,t,o,n);return((e,t,o)=>{const n=Pl(o.origin,t);o.transition.each((s=>{ac(e,o.origin,n,s,t,o.lastPlacement)})),zl(e,n)})(t,s,n),((e,t)=>{((e,t)=>{kt(e,ql,t)})(e,t.placement)})(t,s),((e,t)=>{const o=t.classes;ja(e,o.off),Wa(e,o.on)})(t,s),((e,t,o)=>{(0,o.maxHeightFunction)(e,t.maxHeight)})(t,s,n),((e,t,o)=>{(0,o.maxWidthFunction)(e,t.maxWidth)})(t,s,n),{layout:s.layout,placement:s.placement}},mc=["valignCentre","alignLeft","alignRight","alignCentre","top","bottom","left","right","inset"],gc=(e,t,o,n=1)=>{const s=e*n,r=t*n,a=e=>be(o,e).getOr([]),i=(e,t,o)=>{const n=J(mc,o);return{offset:$t(e,t),classesOn:X(o,a),classesOff:X(n,a)}};return{southeast:()=>i(-e,t,["top","alignLeft"]),southwest:()=>i(e,t,["top","alignRight"]),south:()=>i(-e/2,t,["top","alignCentre"]),northeast:()=>i(-e,-t,["bottom","alignLeft"]),northwest:()=>i(e,-t,["bottom","alignRight"]),north:()=>i(-e/2,-t,["bottom","alignCentre"]),east:()=>i(e,-t/2,["valignCentre","left"]),west:()=>i(-e,-t/2,["valignCentre","right"]),insetNortheast:()=>i(s,r,["top","alignLeft","inset"]),insetNorthwest:()=>i(-s,r,["top","alignRight","inset"]),insetNorth:()=>i(-s/2,r,["top","alignCentre","inset"]),insetSoutheast:()=>i(s,-r,["bottom","alignLeft","inset"]),insetSouthwest:()=>i(-s,-r,["bottom","alignRight","inset"]),insetSouth:()=>i(-s/2,-r,["bottom","alignCentre","inset"]),insetEast:()=>i(-s,-r/2,["valignCentre","right","inset"]),insetWest:()=>i(s,-r/2,["valignCentre","left","inset"])}},pc=()=>gc(0,0,{}),hc=w,fc=(e,t)=>o=>"rtl"===bc(o)?t:e,bc=e=>"rtl"===It(e,"direction")?"rtl":"ltr";var vc;!function(e){e.TopToBottom="toptobottom",e.BottomToTop="bottomtotop"}(vc||(vc={}));const yc="data-alloy-vertical-dir",xc=e=>ui(e,(e=>Ge(e)&&Ot(e,"data-alloy-vertical-dir")===vc.BottomToTop)),wc=()=>vs("layouts",[os("onLtr"),os("onRtl"),us("onBottomLtr"),us("onBottomRtl")]),Sc=(e,t,o,n,s,r,a)=>{const i=a.map(xc).getOr(!1),l=t.layouts.map((t=>t.onLtr(e))),c=t.layouts.map((t=>t.onRtl(e))),d=i?t.layouts.bind((t=>t.onBottomLtr.map((t=>t(e))))).or(l).getOr(s):l.getOr(o),u=i?t.layouts.bind((t=>t.onBottomRtl.map((t=>t(e))))).or(c).getOr(r):c.getOr(n);return fc(d,u)(e)};var kc=[os("hotspot"),us("bubble"),ys("overrides",{}),wc(),Ri("placement",((e,t,o)=>{const n=t.hotspot,s=Ul(o,n.element),r=Sc(e.element,t,fl(),bl(),pl(),hl(),A.some(t.hotspot.element));return A.some(hc({anchorBox:s,bubble:t.bubble.getOr(pc()),overrides:t.overrides,layouts:r}))}))],Cc=[os("x"),os("y"),ys("height",0),ys("width",0),ys("bubble",pc()),ys("overrides",{}),wc(),Ri("placement",((e,t,o)=>{const n=jl(o,t.x,t.y),s=Ko(n.left,n.top,t.width,t.height),r=Sc(e.element,t,ml(),gl(),ml(),gl(),A.none());return A.some(hc({anchorBox:s,bubble:t.bubble,overrides:t.overrides,layouts:r}))}))];const Oc=As([{screen:["point"]},{absolute:["point","scrollLeft","scrollTop"]}]),_c=e=>e.fold(w,((e,t,o)=>e.translate(-t,-o))),Tc=e=>e.fold(w,w),Ec=e=>j(e,((e,t)=>e.translate(t.left,t.top)),$t(0,0)),Ac=e=>{const t=H(e,Tc);return Ec(t)},Mc=Oc.screen,Dc=Oc.absolute,Bc=(e,t,o)=>{const n=et(e.element),s=Uo(n),r=((e,t,o)=>{const n=nt(o.root).dom;return A.from(n.frameElement).map(Ve).filter((t=>{const o=et(t),n=et(e.element);return Ze(o,n)})).map(Xt)})(e,0,o).getOr(s);return Dc(r,s.left,s.top)},Fc=(e,t,o,n)=>{const s=Mc($t(e,t));return A.some(((e,t,o)=>({point:e,width:t,height:o}))(s,o,n))},Ic=(e,t,o,n,s)=>e.map((e=>{const r=[t,e.point],a=(i=()=>Ac(r),l=()=>Ac(r),c=()=>(e=>{const t=H(e,_c);return Ec(t)})(r),n.fold(i,l,c));var i,l,c;const d=(p=a.left,h=a.top,f=e.width,b=e.height,{x:p,y:h,width:f,height:b}),u=o.showAbove?pl():fl(),m=o.showAbove?hl():bl(),g=Sc(s,o,u,m,u,m,A.none());var p,h,f,b;return hc({anchorBox:d,bubble:o.bubble.getOr(pc()),overrides:o.overrides,layouts:g})}));var Rc=[os("node"),os("root"),us("bubble"),wc(),ys("overrides",{}),ys("showAbove",!1),Ri("placement",((e,t,o)=>{const n=Bc(e,0,t);return t.node.filter(yt).bind((s=>{const r=s.dom.getBoundingClientRect(),a=Fc(r.left,r.top,r.width,r.height),i=t.node.getOr(e.element);return Ic(a,n,t,o,i)}))}))];const Nc=(e,t,o,n)=>({start:e,soffset:t,finish:o,foffset:n}),Vc=As([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),zc=(Vc.before,Vc.on,Vc.after,e=>e.fold(w,w,w)),Hc=As([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Lc={domRange:Hc.domRange,relative:Hc.relative,exact:Hc.exact,exactFromRange:e=>Hc.exact(e.start,e.soffset,e.finish,e.foffset),getWin:e=>{const t=(e=>e.match({domRange:e=>Ve(e.startContainer),relative:(e,t)=>zc(e),exact:(e,t,o,n)=>e}))(e);return nt(t)},range:Nc},Pc=(e,t,o)=>{const n=e.document.createRange();var s;return s=n,t.fold((e=>{s.setStartBefore(e.dom)}),((e,t)=>{s.setStart(e.dom,t)}),(e=>{s.setStartAfter(e.dom)})),((e,t)=>{t.fold((t=>{e.setEndBefore(t.dom)}),((t,o)=>{e.setEnd(t.dom,o)}),(t=>{e.setEndAfter(t.dom)}))})(n,o),n},Uc=(e,t,o,n,s)=>{const r=e.document.createRange();return r.setStart(t.dom,o),r.setEnd(n.dom,s),r},Wc=e=>({left:e.left,top:e.top,right:e.right,bottom:e.bottom,width:e.width,height:e.height}),jc=As([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Gc=(e,t,o)=>t(Ve(o.startContainer),o.startOffset,Ve(o.endContainer),o.endOffset),$c=(e,t)=>((e,t)=>{const o=((e,t)=>t.match({domRange:e=>({ltr:x(e),rtl:A.none}),relative:(t,o)=>({ltr:Qt((()=>Pc(e,t,o))),rtl:Qt((()=>A.some(Pc(e,o,t))))}),exact:(t,o,n,s)=>({ltr:Qt((()=>Uc(e,t,o,n,s))),rtl:Qt((()=>A.some(Uc(e,n,s,t,o))))})}))(e,t);return((e,t)=>{const o=t.ltr();return o.collapsed?t.rtl().filter((e=>!1===e.collapsed)).map((e=>jc.rtl(Ve(e.endContainer),e.endOffset,Ve(e.startContainer),e.startOffset))).getOrThunk((()=>Gc(0,jc.ltr,o))):Gc(0,jc.ltr,o)})(0,o)})(e,t).match({ltr:(t,o,n,s)=>{const r=e.document.createRange();return r.setStart(t.dom,o),r.setEnd(n.dom,s),r},rtl:(t,o,n,s)=>{const r=e.document.createRange();return r.setStart(n.dom,s),r.setEnd(t.dom,o),r}});jc.ltr,jc.rtl;const qc=(e,t,o)=>U(((e,t)=>{const o=p(t)?t:T;let n=e.dom;const s=[];for(;null!==n.parentNode&&void 0!==n.parentNode;){const e=n.parentNode,t=Ve(e);if(s.push(t),!0===o(t))break;n=e}return s})(e,o),t),Xc=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return Je(o)?[]:H(o.querySelectorAll(e),Ve)})(t,e),Yc=e=>{if(e.rangeCount>0){const t=e.getRangeAt(0),o=e.getRangeAt(e.rangeCount-1);return A.some(Nc(Ve(t.startContainer),t.startOffset,Ve(o.endContainer),o.endOffset))}return A.none()},Kc=e=>{if(null===e.anchorNode||null===e.focusNode)return Yc(e);{const t=Ve(e.anchorNode),o=Ve(e.focusNode);return((e,t,o,n)=>{const s=((e,t,o,n)=>{const s=et(e).dom.createRange();return s.setStart(e.dom,t),s.setEnd(o.dom,n),s})(e,t,o,n),r=Ze(e,o)&&t===n;return s.collapsed&&!r})(t,e.anchorOffset,o,e.focusOffset)?A.some(Nc(t,e.anchorOffset,o,e.focusOffset)):Yc(e)}},Jc=(e,t)=>(e=>{const t=e.getClientRects(),o=t.length>0?t[0]:e.getBoundingClientRect();return o.width>0||o.height>0?A.some(o).map(Wc):A.none()})($c(e,t)),Zc=((e,t)=>{const o=t=>e(t)?A.from(t.dom.nodeValue):A.none();return{get:t=>{if(!e(t))throw new Error("Can only get text value of a text node");return o(t).getOr("")},getOption:o,set:(t,o)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=o}}})($e),Qc=(e,t)=>({element:e,offset:t}),ed=(e,t)=>$e(e)?Qc(e,t):((e,t)=>{const o=it(e);if(0===o.length)return Qc(e,t);if(t<o.length)return Qc(o[t],0);{const e=o[o.length-1],t=$e(e)?(e=>Zc.get(e))(e).length:it(e).length;return Qc(e,t)}})(e,t),td=e=>void 0!==e.foffset,od=(e,t)=>t.getSelection.getOrThunk((()=>()=>(e=>(e=>A.from(e.getSelection()))(e).filter((e=>e.rangeCount>0)).bind(Kc))(e)))().map((e=>{if(td(e)){const t=ed(e.start,e.soffset),o=ed(e.finish,e.foffset);return Lc.range(t.element,t.offset,o.element,o.offset)}return e}));var nd=[us("getSelection"),os("root"),us("bubble"),wc(),ys("overrides",{}),ys("showAbove",!1),Ri("placement",((e,t,o)=>{const n=nt(t.root).dom,s=Bc(e,0,t),r=od(n,t).bind((e=>{if(td(e)){const t=((e,t)=>(e=>{const t=e.getBoundingClientRect();return t.width>0||t.height>0?A.some(t).map(Wc):A.none()})($c(e,t)))(n,Lc.exactFromRange(e)).orThunk((()=>{const t=Ne("\ufeff");Ro(e.start,t);const o=Jc(n,Lc.exact(t,0,t,1));return Po(t),o}));return t.bind((e=>Fc(e.left,e.top,e.width,e.height)))}{const t=ce(e,(e=>e.dom.getBoundingClientRect())),o={left:Math.min(t.firstCell.left,t.lastCell.left),right:Math.max(t.firstCell.right,t.lastCell.right),top:Math.min(t.firstCell.top,t.lastCell.top),bottom:Math.max(t.firstCell.bottom,t.lastCell.bottom)};return Fc(o.left,o.top,o.right-o.left,o.bottom-o.top)}})),a=od(n,t).bind((e=>td(e)?Ge(e.start)?A.some(e.start):rt(e.start):A.some(e.firstCell))).getOr(e.element);return Ic(r,s,t,o,a)}))];const sd="link-layout",rd=e=>e.x+e.width,ad=(e,t)=>e.x-t.width,id=(e,t)=>e.y-t.height+e.height,ld=e=>e.y,cd=(e,t,o)=>zi(rd(e),ld(e),o.southeast(),Li(),"southeast",Ki(e,{left:0,top:2}),sd),dd=(e,t,o)=>zi(ad(e,t),ld(e),o.southwest(),Pi(),"southwest",Ki(e,{right:1,top:2}),sd),ud=(e,t,o)=>zi(rd(e),id(e,t),o.northeast(),Ui(),"northeast",Ki(e,{left:0,bottom:3}),sd),md=(e,t,o)=>zi(ad(e,t),id(e,t),o.northwest(),Wi(),"northwest",Ki(e,{right:1,bottom:3}),sd),gd=()=>[cd,dd,ud,md],pd=()=>[dd,cd,md,ud];var hd=[os("item"),wc(),ys("overrides",{}),Ri("placement",((e,t,o)=>{const n=Ul(o,t.item.element),s=Sc(e.element,t,gd(),pd(),gd(),pd(),A.none());return A.some(hc({anchorBox:n,bubble:pc(),overrides:t.overrides,layouts:s}))}))],fd=Jn("type",{selection:nd,node:Rc,hotspot:kc,submenu:hd,makeshift:Cc});const bd=[ds("classes",Hn),ks("mode","all",["all","layout","placement"])],vd=[ys("useFixed",T),us("getBounds")],yd=[ns("anchor",fd),vs("transition",bd)],xd=(e,t,o,n,s,r)=>{const a=Yn("placement.info",Dn(yd),s),i=a.anchor,l=n.element,c=o.get(n.uid);Nl((()=>{Dt(l,"position","fixed");const s=Nt(l,"visibility");Dt(l,"visibility","hidden");const d=t.useFixed()?(()=>{const e=document.documentElement;return $l(0,0,e.clientWidth,e.clientHeight)})():(e=>{const t=Xt(e.element),o=e.element.dom.getBoundingClientRect();return Gl(t.left,t.top,o.width,o.height)})(e);i.placement(e,i,d).each((e=>{const s=r.orThunk((()=>t.getBounds.map(_))),i=((e,t,o,n,s,r)=>((e,t,o,n,s,r,a,i)=>{const l=dc(a,"maxHeightFunction",lc()),c=dc(a,"maxWidthFunction",b),d=e.anchorBox,u=e.origin,m={bounds:Wl(u,r),origin:u,preference:n,maxHeightFunction:l,maxWidthFunction:c,lastPlacement:s,transition:i};return uc(d,t,o,m)})(((e,t)=>((e,t)=>({anchorBox:e,origin:t}))(e,t))(t.anchorBox,e),n.element,t.bubble,t.layouts,s,o,t.overrides,r))(d,e,s,n,c,a.transition);o.set(n.uid,i)})),s.fold((()=>{Ht(l,"visibility")}),(e=>{Dt(l,"visibility",e)})),Nt(l,"left").isNone()&&Nt(l,"top").isNone()&&Nt(l,"right").isNone()&&Nt(l,"bottom").isNone()&&xe(Nt(l,"position"),"fixed")&&Ht(l,"position")}),l)};var wd=Object.freeze({__proto__:null,position:(e,t,o,n,s)=>{const r=A.none();xd(e,t,o,n,s,r)},positionWithinBounds:xd,getMode:(e,t,o)=>t.useFixed()?"fixed":"absolute",reset:(e,t,o,n)=>{const s=n.element;L(["position","left","right","top","bottom"],(e=>Ht(s,e))),(e=>{Et(e,ql)})(s),o.clear(n.uid)}});const Sd=Ol({fields:vd,name:"positioning",active:Ml,apis:wd,state:Object.freeze({__proto__:null,init:()=>{let e={};return _a({readState:()=>e,clear:t=>{g(t)?delete e[t]:e={}},set:(t,o)=>{e[t]=o},get:t=>be(e,t)})}})}),kd=e=>e.getSystem().isConnected(),Cd=e=>{Fr(e,kr());const t=e.components();L(t,Cd)},Od=e=>{const t=e.components();L(t,Od),Fr(e,Sr())},_d=(e,t)=>{e.getSystem().addToWorld(t),yt(e.element)&&Od(t)},Td=e=>{Cd(e),e.getSystem().removeFromWorld(e)},Ed=(e,t)=>{zo(e.element,t.element)},Ad=(e,t)=>{Md(e,t,zo)},Md=(e,t,o)=>{e.getSystem().addToWorld(t),o(e.element,t.element),yt(e.element)&&Od(t),e.syncComponents()},Dd=e=>{Cd(e),Po(e.element),e.getSystem().removeFromWorld(e)},Bd=e=>{const t=st(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()));Dd(e),t.each((e=>{e.syncComponents()}))},Fd=e=>{const t=e.components();L(t,Dd),Lo(e.element),e.syncComponents()},Id=(e,t)=>{Nd(e,t,zo)},Rd=(e,t)=>{Nd(e,t,No)},Nd=(e,t,o)=>{o(e,t.element);const n=it(t.element);L(n,(e=>{t.getByDom(e).each(Od)}))},Vd=e=>{const t=it(e.element);L(t,(t=>{e.getByDom(t).each(Cd)})),Po(e.element)},zd=(e,t,o,n)=>{o.get().each((t=>{Fd(e)}));const s=t.getAttachPoint(e);Ad(s,e);const r=e.getSystem().build(n);return Ad(e,r),o.set(r),r},Hd=(e,t,o,n)=>{const s=zd(e,t,o,n);return t.onOpen(e,s),s},Ld=(e,t,o)=>{o.get().each((n=>{Fd(e),Bd(e),t.onClose(e,n),o.clear()}))},Pd=(e,t,o)=>o.isOpen(),Ud=(e,t,o)=>{const n=t.getAttachPoint(e);Dt(e.element,"position",Sd.getMode(n)),((e,t,o,n)=>{Nt(e.element,t).fold((()=>{Et(e.element,o)}),(t=>{kt(e.element,o,t)})),Dt(e.element,t,"hidden")})(e,"visibility",t.cloakVisibilityAttr)},Wd=(e,t,o)=>{(e=>N(["top","left","right","bottom"],(t=>Nt(e,t).isSome())))(e.element)||Ht(e.element,"position"),((e,t,o)=>{_t(e.element,o).fold((()=>Ht(e.element,t)),(o=>Dt(e.element,t,o)))})(e,"visibility",t.cloakVisibilityAttr)};var jd=Object.freeze({__proto__:null,cloak:Ud,decloak:Wd,open:Hd,openWhileCloaked:(e,t,o,n,s)=>{Ud(e,t),Hd(e,t,o,n),s(),Wd(e,t)},close:Ld,isOpen:Pd,isPartOf:(e,t,o,n)=>Pd(0,0,o)&&o.get().exists((o=>t.isPartOf(e,o,n))),getState:(e,t,o)=>o.get(),setContent:(e,t,o,n)=>o.get().map((()=>zd(e,t,o,n)))}),Gd=Object.freeze({__proto__:null,events:(e,t)=>Hr([Ur(hr(),((o,n)=>{Ld(o,e,t)}))])}),$d=[Di("onOpen"),Di("onClose"),os("isPartOf"),os("getAttachPoint"),ys("cloakVisibilityAttr","data-precloak-visibility")],qd=Object.freeze({__proto__:null,init:()=>{const e=Ql(),t=x("not-implemented");return _a({readState:t,isOpen:e.isSet,clear:e.clear,set:e.set,get:e.get})}});const Xd=Ol({fields:$d,name:"sandboxing",active:Gd,apis:jd,state:qd}),Yd=x("dismiss.popups"),Kd=x("reposition.popups"),Jd=x("mouse.released"),Zd=Mn([ys("isExtraPart",T),vs("fireEventInstead",[ys("event",Cr())])]),Qd=e=>{const t=Yn("Dismissal",Zd,e);return{[Yd()]:{schema:Mn([os("target")]),onReceive:(e,o)=>{Xd.isOpen(e)&&(Xd.isPartOf(e,o.target)||t.isExtraPart(e,o.target)||t.fireEventInstead.fold((()=>Xd.close(e)),(t=>Fr(e,t.event))))}}}},eu=Mn([vs("fireEventInstead",[ys("event",Or())]),is("doReposition")]),tu=e=>{const t=Yn("Reposition",eu,e);return{[Kd()]:{onReceive:e=>{Xd.isOpen(e)&&t.fireEventInstead.fold((()=>t.doReposition(e)),(t=>Fr(e,t.event)))}}}},ou=(e,t,o)=>{t.store.manager.onLoad(e,t,o)},nu=(e,t,o)=>{t.store.manager.onUnload(e,t,o)};var su=Object.freeze({__proto__:null,onLoad:ou,onUnload:nu,setValue:(e,t,o,n)=>{t.store.manager.setValue(e,t,o,n)},getValue:(e,t,o)=>t.store.manager.getValue(e,t,o),getState:(e,t,o)=>o}),ru=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.resetOnDom?[Kr(((o,n)=>{ou(o,e,t)})),Jr(((o,n)=>{nu(o,e,t)}))]:[xl(e,t,ou)];return Hr(o)}});const au=()=>{const e=Es(null);return _a({set:e.set,get:e.get,isNotSet:()=>null===e.get(),clear:()=>{e.set(null)},readState:()=>({mode:"memory",value:e.get()})})},iu=()=>{const e=Es({}),t=Es({});return _a({readState:()=>({mode:"dataset",dataByValue:e.get(),dataByText:t.get()}),lookup:o=>be(e.get(),o).orThunk((()=>be(t.get(),o))),update:o=>{const n=e.get(),s=t.get(),r={},a={};L(o,(e=>{r[e.value]=e,be(e,"meta").each((t=>{be(t,"text").each((t=>{a[t]=e}))}))})),e.set({...n,...r}),t.set({...s,...a})},clear:()=>{e.set({}),t.set({})}})};var lu=Object.freeze({__proto__:null,memory:au,dataset:iu,manual:()=>_a({readState:b}),init:e=>e.store.manager.state(e)});const cu=(e,t,o,n)=>{const s=t.store;o.update([n]),s.setValue(e,n),t.onSetValue(e,n)};var du=[us("initialValue"),os("getFallbackEntry"),os("getDataKey"),os("setValue"),Ri("manager",{setValue:cu,getValue:(e,t,o)=>{const n=t.store,s=n.getDataKey(e);return o.lookup(s).getOrThunk((()=>n.getFallbackEntry(s)))},onLoad:(e,t,o)=>{t.store.initialValue.each((n=>{cu(e,t,o,n)}))},onUnload:(e,t,o)=>{o.clear()},state:iu})],uu=[os("getValue"),ys("setValue",b),us("initialValue"),Ri("manager",{setValue:(e,t,o,n)=>{t.store.setValue(e,n),t.onSetValue(e,n)},getValue:(e,t,o)=>t.store.getValue(e),onLoad:(e,t,o)=>{t.store.initialValue.each((o=>{t.store.setValue(e,o)}))},onUnload:b,state:Oa.init})],mu=[us("initialValue"),Ri("manager",{setValue:(e,t,o,n)=>{o.set(n),t.onSetValue(e,n)},getValue:(e,t,o)=>o.get(),onLoad:(e,t,o)=>{t.store.initialValue.each((e=>{o.isNotSet()&&o.set(e)}))},onUnload:(e,t,o)=>{o.clear()},state:au})],gu=[xs("store",{mode:"memory"},Jn("mode",{memory:mu,manual:uu,dataset:du})),Di("onSetValue"),ys("resetOnDom",!1)];const pu=Ol({fields:gu,name:"representing",active:ru,apis:su,extra:{setValueFrom:(e,t)=>{const o=pu.getValue(t);pu.setValue(e,o)}},state:lu}),hu=(e,t)=>Ts(e,{},H(t,(t=>{return o=t.name(),n="Cannot configure "+t.name()+" for "+e,Qn(o,o,{tag:"option",process:{}},Cn((e=>un("The field: "+o+" is forbidden. "+n))));var o,n})).concat([es("dump",w)])),fu=e=>e.dump,bu=(e,t)=>({...kl(t),...e.dump}),vu=hu,yu=bu,xu="placeholder",wu=As([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),Su=e=>ve(e,"uiType"),ku=(e,t,o,n)=>((e,t,o,n)=>Su(o)&&o.uiType===xu?((e,t,o,n)=>e.exists((e=>e!==o.owner))?wu.single(!0,x(o)):be(n,o.name).fold((()=>{throw new Error("Unknown placeholder component: "+o.name+"\nKnown: ["+ae(n)+"]\nNamespace: "+e.getOr("none")+"\nSpec: "+JSON.stringify(o,null,2))}),(e=>e.replace())))(e,0,o,n):wu.single(!1,x(o)))(e,0,o,n).fold(((s,r)=>{const a=Su(o)?r(t,o.config,o.validated):r(t),i=be(a,"components").getOr([]),l=X(i,(o=>ku(e,t,o,n)));return[{...a,components:l}]}),((e,n)=>{if(Su(o)){const e=n(t,o.config,o.validated);return o.validated.preprocess.getOr(w)(e)}return n(t)})),Cu=wu.single,Ou=wu.multiple,_u=x(xu),Tu=As([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),Eu=ys("factory",{sketch:w}),Au=ys("schema",[]),Mu=os("name"),Du=Qn("pname","pname",vn((e=>"<alloy."+la(e.name)+">")),Nn()),Bu=es("schema",(()=>[us("preprocess")])),Fu=ys("defaults",x({})),Iu=ys("overrides",x({})),Ru=Dn([Eu,Au,Mu,Du,Fu,Iu]),Nu=Dn([Eu,Au,Mu,Fu,Iu]),Vu=Dn([Eu,Au,Mu,Du,Fu,Iu]),zu=Dn([Eu,Bu,Mu,os("unit"),Du,Fu,Iu]),Hu=e=>e.fold(A.some,A.none,A.some,A.some),Lu=e=>{const t=e=>e.name;return e.fold(t,t,t,t)},Pu=(e,t)=>o=>{const n=Yn("Converting part type",t,o);return e(n)},Uu=Pu(Tu.required,Ru),Wu=Pu(Tu.external,Nu),ju=Pu(Tu.optional,Vu),Gu=Pu(Tu.group,zu),$u=x("entirety");var qu=Object.freeze({__proto__:null,required:Uu,external:Wu,optional:ju,group:Gu,asNamedPart:Hu,name:Lu,asCommon:e=>e.fold(w,w,w,w),original:$u});const Xu=(e,t,o,n)=>fn(t.defaults(e,o,n),o,{uid:e.partUids[t.name]},t.overrides(e,o,n)),Yu=(e,t)=>{const o={};return L(t,(t=>{Hu(t).each((t=>{const n=Ku(e,t.pname);o[t.name]=o=>{const s=Yn("Part: "+t.name+" in "+e,Dn(t.schema),o);return{...n,config:o,validated:s}}}))})),o},Ku=(e,t)=>({uiType:_u(),owner:e,name:t}),Ju=(e,t,o)=>({uiType:_u(),owner:e,name:t,config:o,validated:{}}),Zu=e=>X(e,(e=>e.fold(A.none,A.some,A.none,A.none).map((e=>ls(e.name,e.schema.concat([Ni($u())])))).toArray())),Qu=e=>H(e,Lu),em=(e,t,o)=>((e,t,o)=>{const n={},s={};return L(o,(e=>{e.fold((e=>{n[e.pname]=Cu(!0,((t,o,n)=>e.factory.sketch(Xu(t,e,o,n))))}),(e=>{const o=t.parts[e.name];s[e.name]=x(e.factory.sketch(Xu(t,e,o[$u()]),o))}),(e=>{n[e.pname]=Cu(!1,((t,o,n)=>e.factory.sketch(Xu(t,e,o,n))))}),(e=>{n[e.pname]=Ou(!0,((t,o,n)=>{const s=t[e.name];return H(s,(o=>e.factory.sketch(fn(e.defaults(t,o,n),o,e.overrides(t,o)))))}))}))})),{internals:x(n),externals:x(s)}})(0,t,o),tm=(e,t,o)=>((e,t,o,n)=>{const s=ce(n,((e,t)=>((e,t)=>{let o=!1;return{name:x(e),required:()=>t.fold(((e,t)=>e),((e,t)=>e)),used:()=>o,replace:()=>{if(o)throw new Error("Trying to use the same placeholder more than once: "+e);return o=!0,t}}})(t,e))),r=((e,t,o,n)=>X(o,(o=>ku(e,t,o,n))))(e,t,o,s);return le(s,(o=>{if(!1===o.used()&&o.required())throw new Error("Placeholder: "+o.name()+" was not found in components list\nNamespace: "+e.getOr("none")+"\nComponents: "+JSON.stringify(t.components,null,2))})),r})(A.some(e),t,t.components,o),om=(e,t,o)=>{const n=t.partUids[o];return e.getSystem().getByUid(n).toOptional()},nm=(e,t,o)=>om(e,t,o).getOrDie("Could not find part: "+o),sm=(e,t,o)=>{const n={},s=t.partUids,r=e.getSystem();return L(o,(e=>{n[e]=x(r.getByUid(s[e]))})),n},rm=(e,t)=>{const o=e.getSystem();return ce(t.partUids,((e,t)=>x(o.getByUid(e))))},am=e=>ae(e.partUids),im=(e,t,o)=>{const n={},s=t.partUids,r=e.getSystem();return L(o,(e=>{n[e]=x(r.getByUid(s[e]).getOrDie())})),n},lm=(e,t)=>{const o=Qu(t);return Ds(H(o,(t=>({key:t,value:e+"-"+t}))))},cm=e=>Qn("partUids","partUids",xn((t=>lm(t.uid,e))),Nn());var dm=Object.freeze({__proto__:null,generate:Yu,generateOne:Ju,schemas:Zu,names:Qu,substitutes:em,components:tm,defaultUids:lm,defaultUidsSchema:cm,getAllParts:rm,getAllPartNames:am,getPart:om,getPartOrDie:nm,getParts:sm,getPartsOrDie:im});const um=(e,t,o,n,s)=>{const r=((e,t)=>(e.length>0?[ls("parts",e)]:[]).concat([os("uid"),ys("dom",{}),ys("components",[]),Ni("originalSpec"),ys("debug.sketcher",{})]).concat(t))(n,s);return Yn(e+" [SpecSchema]",Mn(r.concat(t)),o)},mm=(e,t,o,n,s)=>{const r=gm(s),a=Zu(o),i=cm(o),l=um(e,t,r,a,[i]),c=em(0,l,o);return n(l,tm(e,l,c.internals()),r,c.externals())},gm=e=>(e=>ve(e,"uid"))(e)?e:{...e,uid:ha("uid")},pm=Mn([os("name"),os("factory"),os("configFields"),ys("apis",{}),ys("extraApis",{})]),hm=Mn([os("name"),os("factory"),os("configFields"),os("partFields"),ys("apis",{}),ys("extraApis",{})]),fm=e=>{const t=Yn("Sketcher for "+e.name,pm,e),o=ce(t.apis,Ca),n=ce(t.extraApis,((e,t)=>xa(e,t)));return{name:t.name,configFields:t.configFields,sketch:e=>((e,t,o,n)=>{const s=gm(n);return o(um(e,t,s,[],[]),s)})(t.name,t.configFields,t.factory,e),...o,...n}},bm=e=>{const t=Yn("Sketcher for "+e.name,hm,e),o=Yu(t.name,t.partFields),n=ce(t.apis,Ca),s=ce(t.extraApis,((e,t)=>xa(e,t)));return{name:t.name,partFields:t.partFields,configFields:t.configFields,sketch:e=>mm(t.name,t.configFields,t.partFields,t.factory,e),parts:o,...n,...s}},vm=e=>Ye("input")(e)&&"radio"!==Ot(e,"type")||Ye("textarea")(e);var ym=Object.freeze({__proto__:null,getCurrent:(e,t,o)=>t.find(e)});const xm=[os("find")],wm=Ol({fields:xm,name:"composing",apis:ym}),Sm=["input","button","textarea","select"],km=(e,t,o)=>{(t.disabled()?Am:Mm)(e,t)},Cm=(e,t)=>!0===t.useNative&&R(Sm,Ue(e.element)),Om=e=>{kt(e.element,"disabled","disabled")},_m=e=>{Et(e.element,"disabled")},Tm=e=>{kt(e.element,"aria-disabled","true")},Em=e=>{kt(e.element,"aria-disabled","false")},Am=(e,t,o)=>{t.disableClass.each((t=>{La(e.element,t)})),(Cm(e,t)?Om:Tm)(e),t.onDisabled(e)},Mm=(e,t,o)=>{t.disableClass.each((t=>{Pa(e.element,t)})),(Cm(e,t)?_m:Em)(e),t.onEnabled(e)},Dm=(e,t)=>Cm(e,t)?(e=>Tt(e.element,"disabled"))(e):(e=>"true"===Ot(e.element,"aria-disabled"))(e);var Bm=Object.freeze({__proto__:null,enable:Mm,disable:Am,isDisabled:Dm,onLoad:km,set:(e,t,o,n)=>{(n?Am:Mm)(e,t)}}),Fm=Object.freeze({__proto__:null,exhibit:(e,t)=>Ea({classes:t.disabled()?t.disableClass.toArray():[]}),events:(e,t)=>Hr([Lr(ur(),((t,o)=>Dm(t,e))),xl(e,t,km)])}),Im=[Os("disabled",T),ys("useNative",!0),us("disableClass"),Di("onDisabled"),Di("onEnabled")];const Rm=Ol({fields:Im,name:"disabling",active:Fm,apis:Bm}),Nm=(e,t,o,n)=>{const s=Xc(e.element,"."+t.highlightClass);L(s,(o=>{N(n,(e=>Ze(e.element,o)))||(Pa(o,t.highlightClass),e.getSystem().getByDom(o).each((o=>{t.onDehighlight(e,o),Fr(o,Br())})))}))},Vm=(e,t,o,n)=>{Nm(e,t,0,[n]),zm(e,t,o,n)||(La(n.element,t.highlightClass),t.onHighlight(e,n),Fr(n,Dr()))},zm=(e,t,o,n)=>Ua(n.element,t.highlightClass),Hm=(e,t,o)=>pi(e.element,"."+t.itemClass).bind((t=>e.getSystem().getByDom(t).toOptional())),Lm=(e,t,o)=>{const n=Xc(e.element,"."+t.itemClass);return(n.length>0?A.some(n[n.length-1]):A.none()).bind((t=>e.getSystem().getByDom(t).toOptional()))},Pm=(e,t,o,n)=>{const s=Xc(e.element,"."+t.itemClass);return $(s,(e=>Ua(e,t.highlightClass))).bind((t=>{const o=Xi(t,n,0,s.length-1);return e.getSystem().getByDom(s[o]).toOptional()}))},Um=(e,t,o)=>{const n=Xc(e.element,"."+t.itemClass);return we(H(n,(t=>e.getSystem().getByDom(t).toOptional())))};var Wm=Object.freeze({__proto__:null,dehighlightAll:(e,t,o)=>Nm(e,t,0,[]),dehighlight:(e,t,o,n)=>{zm(e,t,o,n)&&(Pa(n.element,t.highlightClass),t.onDehighlight(e,n),Fr(n,Br()))},highlight:Vm,highlightFirst:(e,t,o)=>{Hm(e,t).each((n=>{Vm(e,t,o,n)}))},highlightLast:(e,t,o)=>{Lm(e,t).each((n=>{Vm(e,t,o,n)}))},highlightAt:(e,t,o,n)=>{((e,t,o,n)=>{const s=Xc(e.element,"."+t.itemClass);return A.from(s[n]).fold((()=>sn.error(new Error("No element found with index "+n))),e.getSystem().getByDom)})(e,t,0,n).fold((e=>{throw e}),(n=>{Vm(e,t,o,n)}))},highlightBy:(e,t,o,n)=>{const s=Um(e,t);G(s,n).each((n=>{Vm(e,t,o,n)}))},isHighlighted:zm,getHighlighted:(e,t,o)=>pi(e.element,"."+t.highlightClass).bind((t=>e.getSystem().getByDom(t).toOptional())),getFirst:Hm,getLast:Lm,getPrevious:(e,t,o)=>Pm(e,t,0,-1),getNext:(e,t,o)=>Pm(e,t,0,1),getCandidates:Um}),jm=[os("highlightClass"),os("itemClass"),Di("onHighlight"),Di("onDehighlight")];const Gm=Ol({fields:jm,name:"highlighting",apis:Wm}),$m=[8],qm=[9],Xm=[13],Ym=[27],Km=[32],Jm=[37],Zm=[38],Qm=[39],eg=[40],tg=(e,t,o)=>{const n=K(e.slice(0,t)),s=K(e.slice(t+1));return G(n.concat(s),o)},og=(e,t,o)=>{const n=K(e.slice(0,t));return G(n,o)},ng=(e,t,o)=>{const n=e.slice(0,t),s=e.slice(t+1);return G(s.concat(n),o)},sg=(e,t,o)=>{const n=e.slice(t+1);return G(n,o)},rg=e=>t=>{const o=t.raw;return R(e,o.which)},ag=e=>t=>Y(e,(e=>e(t))),ig=e=>!0===e.raw.shiftKey,lg=e=>!0===e.raw.ctrlKey,cg=C(ig),dg=(e,t)=>({matches:e,classification:t}),ug=(e,t,o)=>{t.exists((e=>o.exists((t=>Ze(t,e)))))||Ir(e,_r(),{prevFocus:t,newFocus:o})},mg=()=>{const e=e=>Rl(e.element);return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().triggerFocus(o,t.element);const s=e(t);ug(t,n,s)}}},gg=()=>{const e=e=>Gm.getHighlighted(e).map((e=>e.element));return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().getByDom(o).fold(b,(e=>{Gm.highlight(t,e)}));const s=e(t);ug(t,n,s)}}};var pg;!function(e){e.OnFocusMode="onFocus",e.OnEnterOrSpaceMode="onEnterOrSpace",e.OnApiMode="onApi"}(pg||(pg={}));const hg=(e,t,o,n,s)=>{const r=(e,t,o,n,s)=>{return(r=o(e,t,n,s),a=t.event,G(r,(e=>e.matches(a))).map((e=>e.classification))).bind((o=>o(e,t,n,s)));var r,a},a={schema:()=>e.concat([ys("focusManager",mg()),xs("focusInside","onFocus",Gn((e=>R(["onFocus","onEnterOrSpace","onApi"],e)?sn.value(e):sn.error("Invalid value for focusInside")))),Ri("handler",a),Ri("state",t),Ri("sendFocusIn",s)]),processKey:r,toEvents:(e,t)=>{const a=e.focusInside!==pg.OnFocusMode?A.none():s(e).map((o=>Ur(ir(),((n,s)=>{o(n,e,t),s.stop()})))),i=[Ur(Ks(),((n,a)=>{r(n,a,o,e,t).fold((()=>{((o,n)=>{const r=rg(Km.concat(Xm))(n.event);e.focusInside===pg.OnEnterOrSpaceMode&&r&&Rs(o,n)&&s(e).each((s=>{s(o,e,t),n.stop()}))})(n,a)}),(e=>{a.stop()}))})),Ur(Js(),((o,s)=>{r(o,s,n,e,t).each((e=>{s.stop()}))}))];return Hr(a.toArray().concat(i))}};return a},fg=e=>{const t=[us("onEscape"),us("onEnter"),ys("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),ys("firstTabstop",0),ys("useTabstopAt",E),us("visibilitySelector")].concat([e]),o=(e,t)=>{const o=e.visibilitySelector.bind((e=>hi(t,e))).getOr(t);return Wt(o)>0},n=(e,t)=>t.focusManager.get(e).bind((e=>hi(e,t.selector))),s=(e,t,n)=>{((e,t)=>{const n=Xc(e.element,t.selector),s=U(n,(e=>o(t,e)));return A.from(s[t.firstTabstop])})(e,t).each((o=>{t.focusManager.set(e,o)}))},r=(e,t,s,r)=>{const a=Xc(e.element,s.selector);return n(e,s).bind((t=>$(a,k(Ze,t)).bind((t=>((e,t,n,s,r)=>r(t,n,(e=>((e,t)=>o(e,t)&&e.useTabstopAt(t))(s,e))).fold((()=>s.cyclic?A.some(!0):A.none()),(t=>(s.focusManager.set(e,t),A.some(!0)))))(e,a,t,s,r)))))},a=(e,t,o)=>{const n=o.cyclic?tg:og;return r(e,0,o,n)},i=(e,t,o)=>{const n=o.cyclic?ng:sg;return r(e,0,o,n)},l=x([dg(ag([ig,rg(qm)]),a),dg(rg(qm),i),dg(ag([cg,rg(Xm)]),((e,t,o)=>o.onEnter.bind((o=>o(e,t)))))]),c=x([dg(rg(Ym),((e,t,o)=>o.onEscape.bind((o=>o(e,t))))),dg(rg(qm),((e,t,o)=>n(e,o).filter((e=>!o.useTabstopAt(e))).bind((n=>((e=>(e=>st(e))(e).bind(ct).exists((t=>Ze(t,e))))(n)?a:i)(e,t,o)))))]);return hg(t,Oa.init,l,c,(()=>A.some(s)))};var bg=fg(es("cyclic",T)),vg=fg(es("cyclic",E));const yg=(e,t,o)=>vm(o)&&rg(Km)(t.event)?A.none():((e,t,o)=>(Nr(e,o,ur()),A.some(!0)))(e,0,o),xg=(e,t)=>A.some(!0),wg=[ys("execute",yg),ys("useSpace",!1),ys("useEnter",!0),ys("useControlEnter",!1),ys("useDown",!1)],Sg=(e,t,o)=>o.execute(e,t,e.element);var kg=hg(wg,Oa.init,((e,t,o,n)=>{const s=o.useSpace&&!vm(e.element)?Km:[],r=o.useEnter?Xm:[],a=o.useDown?eg:[],i=s.concat(r).concat(a);return[dg(rg(i),Sg)].concat(o.useControlEnter?[dg(ag([lg,rg(Xm)]),Sg)]:[])}),((e,t,o,n)=>o.useSpace&&!vm(e.element)?[dg(rg(Km),xg)]:[]),(()=>A.none()));const Cg=()=>{const e=Ql();return _a({readState:()=>e.get().map((e=>({numRows:String(e.numRows),numColumns:String(e.numColumns)}))).getOr({numRows:"?",numColumns:"?"}),setGridSize:(t,o)=>{e.set({numRows:t,numColumns:o})},getNumRows:()=>e.get().map((e=>e.numRows)),getNumColumns:()=>e.get().map((e=>e.numColumns))})};var Og=Object.freeze({__proto__:null,flatgrid:Cg,init:e=>e.state(e)});const _g=e=>(t,o,n,s)=>{const r=e(t.element);return Mg(r,t,o,n,s)},Tg=(e,t)=>{const o=fc(e,t);return _g(o)},Eg=(e,t)=>{const o=fc(t,e);return _g(o)},Ag=e=>(t,o,n,s)=>Mg(e,t,o,n,s),Mg=(e,t,o,n,s)=>n.focusManager.get(t).bind((o=>e(t.element,o,n,s))).map((e=>(n.focusManager.set(t,e),!0))),Dg=Ag,Bg=Ag,Fg=Ag,Ig=e=>!(e=>e.offsetWidth<=0&&e.offsetHeight<=0)(e.dom),Rg=(e,t,o)=>{const n=Xc(e,o);return((e,o)=>$(e,(e=>Ze(e,t))).map((t=>({index:t,candidates:e}))))(U(n,Ig))},Ng=(e,t)=>$(e,(e=>Ze(t,e))),Vg=(e,t,o,n)=>n(Math.floor(t/o),t%o).bind((t=>{const n=t.row*o+t.column;return n>=0&&n<e.length?A.some(e[n]):A.none()})),zg=(e,t,o,n,s)=>Vg(e,t,n,((t,r)=>{const a=t===o-1?e.length-t*n:n,i=Xi(r,s,0,a-1);return A.some({row:t,column:i})})),Hg=(e,t,o,n,s)=>Vg(e,t,n,((t,r)=>{const a=Xi(t,s,0,o-1),i=a===o-1?e.length-a*n:n,l=Yi(r,0,i-1);return A.some({row:a,column:l})})),Lg=[os("selector"),ys("execute",yg),Bi("onEscape"),ys("captureTab",!1),Vi()],Pg=(e,t,o)=>{pi(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},Ug=e=>(t,o,n,s)=>Rg(t,o,n.selector).bind((t=>e(t.candidates,t.index,s.getNumRows().getOr(n.initSize.numRows),s.getNumColumns().getOr(n.initSize.numColumns)))),Wg=(e,t,o)=>o.captureTab?A.some(!0):A.none(),jg=Ug(((e,t,o,n)=>zg(e,t,o,n,-1))),Gg=Ug(((e,t,o,n)=>zg(e,t,o,n,1))),$g=Ug(((e,t,o,n)=>Hg(e,t,o,n,-1))),qg=Ug(((e,t,o,n)=>Hg(e,t,o,n,1))),Xg=x([dg(rg(Jm),Tg(jg,Gg)),dg(rg(Qm),Eg(jg,Gg)),dg(rg(Zm),Dg($g)),dg(rg(eg),Bg(qg)),dg(ag([ig,rg(qm)]),Wg),dg(ag([cg,rg(qm)]),Wg),dg(rg(Km.concat(Xm)),((e,t,o,n)=>((e,t)=>t.focusManager.get(e).bind((e=>hi(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n)))))]),Yg=x([dg(rg(Ym),((e,t,o)=>o.onEscape(e,t))),dg(rg(Km),xg)]);var Kg=hg(Lg,Cg,Xg,Yg,(()=>A.some(Pg)));const Jg=(e,t,o,n,s)=>{const r=(e,t,o)=>s(e,t,n,0,o.length-1,o[t],(t=>{return n=o[t],"button"===Ue(n)&&"disabled"===Ot(n,"disabled")?r(e,t,o):A.from(o[t]);var n}));return Rg(e,o,t).bind((e=>{const t=e.index,o=e.candidates;return r(t,t,o)}))},Zg=(e,t,o,n)=>Jg(e,t,o,n,((e,t,o,n,s,r,a)=>{const i=Yi(t+o,n,s);return i===e?A.from(r):a(i)})),Qg=(e,t,o,n)=>Jg(e,t,o,n,((e,t,o,n,s,r,a)=>{const i=Xi(t,o,n,s);return i===e?A.none():a(i)})),ep=[os("selector"),ys("getInitial",A.none),ys("execute",yg),Bi("onEscape"),ys("executeOnMove",!1),ys("allowVertical",!0),ys("allowHorizontal",!0),ys("cycles",!0)],tp=(e,t,o)=>((e,t)=>t.focusManager.get(e).bind((e=>hi(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n))),op=(e,t,o)=>{t.getInitial(e).orThunk((()=>pi(e.element,t.selector))).each((o=>{t.focusManager.set(e,o)}))},np=(e,t,o)=>(o.cycles?Qg:Zg)(e,o.selector,t,-1),sp=(e,t,o)=>(o.cycles?Qg:Zg)(e,o.selector,t,1),rp=e=>(t,o,n,s)=>e(t,o,n,s).bind((()=>n.executeOnMove?tp(t,o,n):A.some(!0))),ap=x([dg(rg(Km),xg),dg(rg(Ym),((e,t,o)=>o.onEscape(e,t)))]);var ip=hg(ep,Oa.init,((e,t,o,n)=>{const s=[...o.allowHorizontal?Jm:[]].concat(o.allowVertical?Zm:[]),r=[...o.allowHorizontal?Qm:[]].concat(o.allowVertical?eg:[]);return[dg(rg(s),rp(Tg(np,sp))),dg(rg(r),rp(Eg(np,sp))),dg(rg(Xm),tp),dg(rg(Km),tp)]}),ap,(()=>A.some(op)));const lp=(e,t,o)=>A.from(e[t]).bind((e=>A.from(e[o]).map((e=>({rowIndex:t,columnIndex:o,cell:e}))))),cp=(e,t,o,n)=>{const s=e[t].length,r=Xi(o,n,0,s-1);return lp(e,t,r)},dp=(e,t,o,n)=>{const s=Xi(o,n,0,e.length-1),r=e[s].length,a=Yi(t,0,r-1);return lp(e,s,a)},up=(e,t,o,n)=>{const s=e[t].length,r=Yi(o+n,0,s-1);return lp(e,t,r)},mp=(e,t,o,n)=>{const s=Yi(o+n,0,e.length-1),r=e[s].length,a=Yi(t,0,r-1);return lp(e,s,a)},gp=[ls("selectors",[os("row"),os("cell")]),ys("cycles",!0),ys("previousSelector",A.none),ys("execute",yg)],pp=(e,t,o)=>{t.previousSelector(e).orThunk((()=>{const o=t.selectors;return pi(e.element,o.cell)})).each((o=>{t.focusManager.set(e,o)}))},hp=(e,t)=>(o,n,s)=>{const r=s.cycles?e:t;return hi(n,s.selectors.row).bind((e=>{const t=Xc(e,s.selectors.cell);return Ng(t,n).bind((t=>{const n=Xc(o,s.selectors.row);return Ng(n,e).bind((e=>{const o=((e,t)=>H(e,(e=>Xc(e,t.selectors.cell))))(n,s);return r(o,e,t).map((e=>e.cell))}))}))}))},fp=hp(((e,t,o)=>cp(e,t,o,-1)),((e,t,o)=>up(e,t,o,-1))),bp=hp(((e,t,o)=>cp(e,t,o,1)),((e,t,o)=>up(e,t,o,1))),vp=hp(((e,t,o)=>dp(e,o,t,-1)),((e,t,o)=>mp(e,o,t,-1))),yp=hp(((e,t,o)=>dp(e,o,t,1)),((e,t,o)=>mp(e,o,t,1))),xp=x([dg(rg(Jm),Tg(fp,bp)),dg(rg(Qm),Eg(fp,bp)),dg(rg(Zm),Dg(vp)),dg(rg(eg),Bg(yp)),dg(rg(Km.concat(Xm)),((e,t,o)=>Rl(e.element).bind((n=>o.execute(e,t,n)))))]),wp=x([dg(rg(Km),xg)]);var Sp=hg(gp,Oa.init,xp,wp,(()=>A.some(pp)));const kp=[os("selector"),ys("execute",yg),ys("moveOnTab",!1)],Cp=(e,t,o)=>o.focusManager.get(e).bind((n=>o.execute(e,t,n))),Op=(e,t,o)=>{pi(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},_p=(e,t,o)=>Qg(e,o.selector,t,-1),Tp=(e,t,o)=>Qg(e,o.selector,t,1),Ep=x([dg(rg(Zm),Fg(_p)),dg(rg(eg),Fg(Tp)),dg(ag([ig,rg(qm)]),((e,t,o,n)=>o.moveOnTab?Fg(_p)(e,t,o,n):A.none())),dg(ag([cg,rg(qm)]),((e,t,o,n)=>o.moveOnTab?Fg(Tp)(e,t,o,n):A.none())),dg(rg(Xm),Cp),dg(rg(Km),Cp)]),Ap=x([dg(rg(Km),xg)]);var Mp=hg(kp,Oa.init,Ep,Ap,(()=>A.some(Op)));const Dp=[Bi("onSpace"),Bi("onEnter"),Bi("onShiftEnter"),Bi("onLeft"),Bi("onRight"),Bi("onTab"),Bi("onShiftTab"),Bi("onUp"),Bi("onDown"),Bi("onEscape"),ys("stopSpaceKeyup",!1),us("focusIn")];var Bp=hg(Dp,Oa.init,((e,t,o)=>[dg(rg(Km),o.onSpace),dg(ag([cg,rg(Xm)]),o.onEnter),dg(ag([ig,rg(Xm)]),o.onShiftEnter),dg(ag([ig,rg(qm)]),o.onShiftTab),dg(ag([cg,rg(qm)]),o.onTab),dg(rg(Zm),o.onUp),dg(rg(eg),o.onDown),dg(rg(Jm),o.onLeft),dg(rg(Qm),o.onRight),dg(rg(Km),o.onSpace)]),((e,t,o)=>[...o.stopSpaceKeyup?[dg(rg(Km),xg)]:[],dg(rg(Ym),o.onEscape)]),(e=>e.focusIn));const Fp=bg.schema(),Ip=vg.schema(),Rp=ip.schema(),Np=Kg.schema(),Vp=Sp.schema(),zp=kg.schema(),Hp=Mp.schema(),Lp=Bp.schema(),Pp=Tl({branchKey:"mode",branches:Object.freeze({__proto__:null,acyclic:Fp,cyclic:Ip,flow:Rp,flatgrid:Np,matrix:Vp,execution:zp,menu:Hp,special:Lp}),name:"keying",active:{events:(e,t)=>e.handler.toEvents(e,t)},apis:{focusIn:(e,t,o)=>{t.sendFocusIn(t).fold((()=>{e.getSystem().triggerFocus(e.element,e.element)}),(n=>{n(e,t,o)}))},setGridSize:(e,t,o,n,s)=>{(e=>ye(e,"setGridSize"))(o)?o.setGridSize(n,s):console.error("Layout does not support setGridSize")}},state:Og}),Up=(e,t)=>{Nl((()=>{((e,t,o)=>{const n=e.components();(e=>{L(e.components(),(e=>Po(e.element))),Lo(e.element),e.syncComponents()})(e);const s=o(t),r=J(n,s);L(r,(t=>{Cd(t),e.getSystem().removeFromWorld(t)})),L(s,(t=>{kd(t)?Ed(e,t):(e.getSystem().addToWorld(t),Ed(e,t),yt(e.element)&&Od(t))})),e.syncComponents()})(e,t,(()=>H(t,e.getSystem().build)))}),e.element)},Wp=(e,t)=>{Nl((()=>{((o,n,s)=>{const r=o.components(),a=X(n,(e=>ka(e).toArray()));L(r,(e=>{R(a,e)||Td(e)}));const i=((e,t,o)=>Ya(e,t,((t,n)=>Ka(e,n,t,o))))(e.element,t,e.getSystem().buildOrPatch),l=J(r,i);L(l,(e=>{kd(e)&&Td(e)})),L(i,(e=>{kd(e)||_d(o,e)})),o.syncComponents()})(e,t)}),e.element)},jp=(e,t,o,n)=>{Td(t);const s=Ka(e.element,o,n,e.getSystem().buildOrPatch);_d(e,s),e.syncComponents()},Gp=(e,t,o)=>{const n=e.getSystem().build(o);Md(e,n,t)},$p=(e,t,o,n)=>{Bd(t),Gp(e,((e,t)=>((e,t,o)=>{lt(e,o).fold((()=>{zo(e,t)}),(e=>{Ro(e,t)}))})(e,t,o)),n)},qp=(e,t)=>e.components(),Xp=(e,t,o,n,s)=>{const r=qp(e);return A.from(r[n]).map((o=>(s.fold((()=>Bd(o)),(s=>{(t.reuseDom?jp:$p)(e,o,n,s)})),o)))};var Yp=Object.freeze({__proto__:null,append:(e,t,o,n)=>{Gp(e,zo,n)},prepend:(e,t,o,n)=>{Gp(e,Vo,n)},remove:(e,t,o,n)=>{const s=qp(e),r=G(s,(e=>Ze(n.element,e.element)));r.each(Bd)},replaceAt:Xp,replaceBy:(e,t,o,n,s)=>{const r=qp(e);return $(r,n).bind((o=>Xp(e,t,0,o,s)))},set:(e,t,o,n)=>(t.reuseDom?Wp:Up)(e,n),contents:qp});const Kp=Ol({fields:[Cs("reuseDom",!0)],name:"replacing",apis:Yp}),Jp=(e,t)=>{const o=((e,t)=>{const o=Hr(t);return Ol({fields:[os("enabled")],name:e,active:{events:x(o)}})})(e,t);return{key:e,value:{config:{},me:o,configAsRaw:x({}),initialConfig:{},state:Oa}}},Zp=(e,t)=>{t.ignore||(Dl(e.element),t.onFocus(e))};var Qp=Object.freeze({__proto__:null,focus:Zp,blur:(e,t)=>{t.ignore||Bl(e.element)},isFocused:e=>Fl(e.element)}),eh=Object.freeze({__proto__:null,exhibit:(e,t)=>{const o=t.ignore?{}:{attributes:{tabindex:"-1"}};return Ea(o)},events:e=>Hr([Ur(ir(),((t,o)=>{Zp(t,e),o.stop()}))].concat(e.stopMousedown?[Ur(Ws(),((e,t)=>{t.event.prevent()}))]:[]))}),th=[Di("onFocus"),ys("stopMousedown",!1),ys("ignore",!1)];const oh=Ol({fields:th,name:"focusing",active:eh,apis:Qp}),nh=(e,t,o,n)=>{const s=o.get();o.set(n),((e,t,o)=>{t.toggleClass.each((t=>{o.get()?La(e.element,t):Pa(e.element,t)}))})(e,t,o),((e,t,o)=>{const n=t.aria;n.update(e,n,o.get())})(e,t,o),s!==n&&t.onToggled(e,n)},sh=(e,t,o)=>{nh(e,t,o,!o.get())},rh=(e,t,o)=>{nh(e,t,o,t.selected)};var ah=Object.freeze({__proto__:null,onLoad:rh,toggle:sh,isOn:(e,t,o)=>o.get(),on:(e,t,o)=>{nh(e,t,o,!0)},off:(e,t,o)=>{nh(e,t,o,!1)},set:nh}),ih=Object.freeze({__proto__:null,exhibit:()=>Ea({}),events:(e,t)=>{const o=(n=e,s=t,r=sh,Qr((e=>{r(e,n,s)})));var n,s,r;const a=xl(e,t,rh);return Hr(q([e.toggleOnExecute?[o]:[],[a]]))}});const lh=(e,t,o)=>{kt(e.element,"aria-expanded",o)};var ch=[ys("selected",!1),us("toggleClass"),ys("toggleOnExecute",!0),Di("onToggled"),xs("aria",{mode:"none"},Jn("mode",{pressed:[ys("syncWithExpanded",!1),Ri("update",((e,t,o)=>{kt(e.element,"aria-pressed",o),t.syncWithExpanded&&lh(e,0,o)}))],checked:[Ri("update",((e,t,o)=>{kt(e.element,"aria-checked",o)}))],expanded:[Ri("update",lh)],selected:[Ri("update",((e,t,o)=>{kt(e.element,"aria-selected",o)}))],none:[Ri("update",b)]}))];const dh=Ol({fields:ch,name:"toggling",active:ih,apis:ah,state:(!1,{init:()=>{const e=Es(false);return{get:()=>e.get(),set:t=>e.set(t),clear:()=>e.set(false),readState:()=>e.get()}}})});const uh=()=>{const e=(e,t)=>{t.stop(),Rr(e)};return[Ur(er(),e),Ur(gr(),e),qr(Hs()),qr(Ws())]},mh=e=>Hr(q([e.map((e=>Qr(((t,o)=>{e(t),o.stop()})))).toArray(),uh()])),gh="alloy.item-hover",ph="alloy.item-focus",hh="alloy.item-toggled",fh=e=>{(Rl(e.element).isNone()||oh.isFocused(e))&&(oh.isFocused(e)||oh.focus(e),Ir(e,gh,{item:e}))},bh=e=>{Ir(e,ph,{item:e})},vh=x(gh),yh=x(ph),xh=x(hh),wh=e=>e.toggling.map((e=>e.exclusive?"menuitemradio":"menuitemcheckbox")).getOr("menuitem"),Sh=[os("data"),os("components"),os("dom"),ys("hasSubmenu",!1),us("toggling"),vu("itemBehaviours",[dh,oh,Pp,pu]),ys("ignoreFocus",!1),ys("domModification",{}),Ri("builder",(e=>({dom:e.dom,domModification:{...e.domModification,attributes:{role:wh(e),...e.domModification.attributes,"aria-haspopup":e.hasSubmenu,...e.hasSubmenu?{"aria-expanded":!1}:{}}},behaviours:yu(e.itemBehaviours,[e.toggling.fold(dh.revoke,(e=>dh.config((e=>({aria:{mode:"checked"},...ge(e,((e,t)=>"exclusive"!==t)),onToggled:(t,o)=>{p(e.onToggled)&&e.onToggled(t,o),((e,t)=>{Ir(e,hh,{item:e,state:t})})(t,o)}}))(e)))),oh.config({ignore:e.ignoreFocus,stopMousedown:e.ignoreFocus,onFocus:e=>{bh(e)}}),Pp.config({mode:"execution"}),pu.config({store:{mode:"memory",initialValue:e.data}}),Jp("item-type-events",[...uh(),Ur(qs(),fh),Ur(mr(),oh.focus)])]),components:e.components,eventOrder:e.eventOrder}))),ys("eventOrder",{})],kh=[os("dom"),os("components"),Ri("builder",(e=>({dom:e.dom,components:e.components,events:Hr([Xr(mr())])})))],Ch=x("item-widget"),Oh=x([Uu({name:"widget",overrides:e=>({behaviours:kl([pu.config({store:{mode:"manual",getValue:t=>e.data,setValue:b}})])})})]),_h=[os("uid"),os("data"),os("components"),os("dom"),ys("autofocus",!1),ys("ignoreFocus",!1),vu("widgetBehaviours",[pu,oh,Pp]),ys("domModification",{}),cm(Oh()),Ri("builder",(e=>{const t=em(Ch(),e,Oh()),o=tm(Ch(),e,t.internals()),n=t=>om(t,e,"widget").map((e=>(Pp.focusIn(e),e))),s=(t,o)=>vm(o.event.target)?A.none():e.autofocus?(o.setSource(t.element),A.none()):A.none();return{dom:e.dom,components:o,domModification:e.domModification,events:Hr([Qr(((e,t)=>{n(e).each((e=>{t.stop()}))})),Ur(qs(),fh),Ur(mr(),((t,o)=>{e.autofocus?n(t):oh.focus(t)}))]),behaviours:yu(e.widgetBehaviours,[pu.config({store:{mode:"memory",initialValue:e.data}}),oh.config({ignore:e.ignoreFocus,onFocus:e=>{bh(e)}}),Pp.config({mode:"special",focusIn:e.autofocus?e=>{n(e)}:El(),onLeft:s,onRight:s,onEscape:(t,o)=>oh.isFocused(t)||e.autofocus?e.autofocus?(o.setSource(t.element),A.none()):A.none():(oh.focus(t),A.some(!0))})])}}))],Th=Jn("type",{widget:_h,item:Sh,separator:kh}),Eh=x([Gu({factory:{sketch:e=>{const t=Yn("menu.spec item",Th,e);return t.builder(t)}},name:"items",unit:"item",defaults:(e,t)=>ve(t,"uid")?t:{...t,uid:ha("item")},overrides:(e,t)=>({type:t.type,ignoreFocus:e.fakeFocus,domModification:{classes:[e.markers.item]}})})]),Ah=x([os("value"),os("items"),os("dom"),os("components"),ys("eventOrder",{}),hu("menuBehaviours",[Gm,pu,wm,Pp]),xs("movement",{mode:"menu",moveOnTab:!0},Jn("mode",{grid:[Vi(),Ri("config",((e,t)=>({mode:"flatgrid",selector:"."+e.markers.item,initSize:{numColumns:t.initSize.numColumns,numRows:t.initSize.numRows},focusManager:e.focusManager})))],matrix:[Ri("config",((e,t)=>({mode:"matrix",selectors:{row:t.rowSelector,cell:"."+e.markers.item},previousSelector:t.previousSelector,focusManager:e.focusManager}))),os("rowSelector"),ys("previousSelector",A.none)],menu:[ys("moveOnTab",!0),Ri("config",((e,t)=>({mode:"menu",selector:"."+e.markers.item,moveOnTab:t.moveOnTab,focusManager:e.focusManager})))]})),ns("markers",_i()),ys("fakeFocus",!1),ys("focusManager",mg()),Di("onHighlight"),Di("onDehighlight")]),Mh=x("alloy.menu-focus"),Dh=bm({name:"Menu",configFields:Ah(),partFields:Eh(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,markers:e.markers,behaviours:bu(e.menuBehaviours,[Gm.config({highlightClass:e.markers.selectedItem,itemClass:e.markers.item,onHighlight:e.onHighlight,onDehighlight:e.onDehighlight}),pu.config({store:{mode:"memory",initialValue:e.value}}),wm.config({find:A.some}),Pp.config(e.movement.config(e,e.movement))]),events:Hr([Ur(yh(),((e,t)=>{const o=t.event;e.getSystem().getByDom(o.target).each((o=>{Gm.highlight(e,o),t.stop(),Ir(e,Mh(),{menu:e,item:o})}))})),Ur(vh(),((e,t)=>{const o=t.event.item;Gm.highlight(e,o)})),Ur(xh(),((e,t)=>{const{item:o,state:n}=t.event;n&&"menuitemradio"===Ot(o.element,"role")&&((e,t)=>{const o=Xc(e.element,'[role="menuitemradio"][aria-checked="true"]');L(o,(o=>{Ze(o,t.element)||e.getSystem().getByDom(o).each((e=>{dh.off(e)}))}))})(e,o)}))]),components:t,eventOrder:e.eventOrder,domModification:{attributes:{role:"menu"}}})}),Bh=(e,t,o,n)=>be(o,n).bind((n=>be(e,n).bind((n=>{const s=Bh(e,t,o,n);return A.some([n].concat(s))})))).getOr([]),Fh=e=>"prepared"===e.type?A.some(e.menu):A.none(),Ih=()=>{const e=Es({}),t=Es({}),o=Es({}),n=Ql(),s=Es({}),r=e=>a(e).bind(Fh),a=e=>be(t.get(),e),i=t=>be(e.get(),t);return{setMenuBuilt:(e,o)=>{t.set({...t.get(),[e]:{type:"prepared",menu:o}})},setContents:(r,a,i,l)=>{n.set(r),e.set(i),t.set(a),s.set(l);const c=((e,t)=>{const o={};le(e,((e,t)=>{L(e,(e=>{o[e]=t}))}));const n=t,s=de(t,((e,t)=>({k:e,v:t}))),r=ce(s,((e,t)=>[t].concat(Bh(o,n,s,t))));return ce(o,(e=>be(r,e).getOr([e])))})(l,i);o.set(c)},expand:t=>be(e.get(),t).map((e=>{const n=be(o.get(),t).getOr([]);return[e].concat(n)})),refresh:e=>be(o.get(),e),collapse:e=>be(o.get(),e).bind((e=>e.length>1?A.some(e.slice(1)):A.none())),lookupMenu:a,lookupItem:i,otherMenus:e=>{const t=s.get();return J(ae(t),e)},getPrimary:()=>n.get().bind(r),getMenus:()=>t.get(),clear:()=>{e.set({}),t.set({}),o.set({}),n.clear()},isClear:()=>n.get().isNone(),getTriggeringPath:(t,s)=>{const a=U(i(t).toArray(),(e=>r(e).isSome()));return be(o.get(),t).bind((t=>{const o=K(a.concat(t));return(e=>{const t=[];for(let o=0;o<e.length;o++){const n=e[o];if(!n.isSome())return A.none();t.push(n.getOrDie())}return A.some(t)})(X(o,((t,a)=>((t,o,n)=>r(t).bind((s=>(t=>he(e.get(),((e,o)=>e===t)))(t).bind((e=>o(e).map((e=>({triggeredMenu:s,triggeringItem:e,triggeringPath:n}))))))))(t,s,o.slice(0,a+1)).fold((()=>xe(n.get(),t)?[]:[A.none()]),(e=>[A.some(e)])))))}))}}},Rh=Fh,Nh=la("tiered-menu-item-highlight"),Vh=la("tiered-menu-item-dehighlight");var zh;!function(e){e[e.HighlightMenuAndItem=0]="HighlightMenuAndItem",e[e.HighlightJustMenu=1]="HighlightJustMenu",e[e.HighlightNone=2]="HighlightNone"}(zh||(zh={}));const Hh=x("collapse-item"),Lh=fm({name:"TieredMenu",configFields:[Ii("onExecute"),Ii("onEscape"),Fi("onOpenMenu"),Fi("onOpenSubmenu"),Di("onRepositionMenu"),Di("onCollapseMenu"),ys("highlightOnOpen",zh.HighlightMenuAndItem),ls("data",[os("primary"),os("menus"),os("expansions")]),ys("fakeFocus",!1),Di("onHighlightItem"),Di("onDehighlightItem"),Di("onHover"),Ei(),os("dom"),ys("navigateOnHover",!0),ys("stayInDom",!1),hu("tmenuBehaviours",[Pp,Gm,wm,Kp]),ys("eventOrder",{})],apis:{collapseMenu:(e,t)=>{e.collapseMenu(t)},highlightPrimary:(e,t)=>{e.highlightPrimary(t)},repositionMenus:(e,t)=>{e.repositionMenus(t)}},factory:(e,t)=>{const o=Ql(),n=Ih(),s=e=>pu.getValue(e).value,r=t=>ce(e.data.menus,((e,t)=>X(e.items,(e=>"separator"===e.type?[]:[e.data.value])))),a=Gm.highlight,i=(t,o)=>{a(t,o),Gm.getHighlighted(o).orThunk((()=>Gm.getFirst(o))).each((n=>{e.fakeFocus?Gm.highlight(o,n):Nr(t,n.element,mr())}))},l=(e,t)=>we(H(t,(t=>e.lookupMenu(t).bind((e=>"prepared"===e.type?A.some(e.menu):A.none()))))),c=(t,o,n)=>{const s=l(o,o.otherMenus(n));L(s,(o=>{ja(o.element,[e.markers.backgroundMenu]),e.stayInDom||Kp.remove(t,o)}))},d=(t,n)=>{const r=(t=>o.get().getOrThunk((()=>{const n={},r=Xc(t.element,`.${e.markers.item}`),a=U(r,(e=>"true"===Ot(e,"aria-haspopup")));return L(a,(e=>{t.getSystem().getByDom(e).each((e=>{const t=s(e);n[t]=e}))})),o.set(n),n})))(t);le(r,((e,t)=>{const o=R(n,t);kt(e.element,"aria-expanded",o)}))},u=(t,o,n)=>A.from(n[0]).bind((s=>o.lookupMenu(s).bind((s=>{if("notbuilt"===s.type)return A.none();{const r=s.menu,a=l(o,n.slice(1));return L(a,(t=>{La(t.element,e.markers.backgroundMenu)})),yt(r.element)||Kp.append(t,ai(r)),ja(r.element,[e.markers.backgroundMenu]),i(t,r),c(t,o,n),A.some(r)}}))));let m;!function(e){e[e.HighlightSubmenu=0]="HighlightSubmenu",e[e.HighlightParent=1]="HighlightParent"}(m||(m={}));const g=(t,o,r=m.HighlightSubmenu)=>{if(o.hasConfigured(Rm)&&Rm.isDisabled(o))return A.some(o);{const a=s(o);return n.expand(a).bind((s=>(d(t,s),A.from(s[0]).bind((a=>n.lookupMenu(a).bind((i=>{const l=((e,t,o)=>{if("notbuilt"===o.type){const s=e.getSystem().build(o.nbMenu());return n.setMenuBuilt(t,s),s}return o.menu})(t,a,i);return yt(l.element)||Kp.append(t,ai(l)),e.onOpenSubmenu(t,o,l,K(s)),r===m.HighlightSubmenu?(Gm.highlightFirst(l),u(t,n,s)):(Gm.dehighlightAll(l),A.some(o))})))))))}},p=(t,o)=>{const r=s(o);return n.collapse(r).bind((s=>(d(t,s),u(t,n,s).map((n=>(e.onCollapseMenu(t,o,n),n))))))},h=t=>(o,n)=>hi(n.getSource(),`.${e.markers.item}`).bind((e=>o.getSystem().getByDom(e).toOptional().bind((e=>t(o,e).map(E))))),f=Hr([Ur(Mh(),((e,t)=>{const o=t.event.item;n.lookupItem(s(o)).each((()=>{const o=t.event.menu;Gm.highlight(e,o);const r=s(t.event.item);n.refresh(r).each((t=>c(e,n,t)))}))})),Qr(((t,o)=>{const n=o.event.target;t.getSystem().getByDom(n).each((o=>{0===s(o).indexOf("collapse-item")&&p(t,o),g(t,o,m.HighlightSubmenu).fold((()=>{e.onExecute(t,o)}),b)}))})),Kr(((t,o)=>{(t=>{const o=((t,o,n)=>ce(n,((n,s)=>{const r=()=>Dh.sketch({...n,value:s,markers:e.markers,fakeFocus:e.fakeFocus,onHighlight:(e,t)=>{Ir(e,Nh,{menuComp:e,itemComp:t})},onDehighlight:(e,t)=>{Ir(e,Vh,{menuComp:e,itemComp:t})},focusManager:e.fakeFocus?gg():mg()});return s===o?{type:"prepared",menu:t.getSystem().build(r())}:{type:"notbuilt",nbMenu:r}})))(t,e.data.primary,e.data.menus),s=r();return n.setContents(e.data.primary,o,e.data.expansions,s),n.getPrimary()})(t).each((o=>{Kp.append(t,ai(o)),e.onOpenMenu(t,o),e.highlightOnOpen===zh.HighlightMenuAndItem?i(t,o):e.highlightOnOpen===zh.HighlightJustMenu&&a(t,o)}))})),Ur(Nh,((t,o)=>{e.onHighlightItem(t,o.event.menuComp,o.event.itemComp)})),Ur(Vh,((t,o)=>{e.onDehighlightItem(t,o.event.menuComp,o.event.itemComp)})),...e.navigateOnHover?[Ur(vh(),((t,o)=>{const r=o.event.item;((e,t)=>{const o=s(t);n.refresh(o).bind((t=>(d(e,t),u(e,n,t))))})(t,r),g(t,r,m.HighlightParent),e.onHover(t,r)}))]:[]]),v=e=>Gm.getHighlighted(e).bind(Gm.getHighlighted),y={collapseMenu:e=>{v(e).each((t=>{p(e,t)}))},highlightPrimary:e=>{n.getPrimary().each((t=>{i(e,t)}))},repositionMenus:t=>{const o=n.getPrimary().bind((e=>v(t).bind((e=>{const t=s(e),o=fe(n.getMenus()),r=we(H(o,Rh));return n.getTriggeringPath(t,(e=>((e,t,o)=>re(t,(e=>{if(!e.getSystem().isConnected())return A.none();const t=Gm.getCandidates(e);return G(t,(e=>s(e)===o))})))(0,r,e)))})).map((t=>({primary:e,triggeringPath:t})))));o.fold((()=>{(e=>A.from(e.components()[0]).filter((e=>"menu"===Ot(e.element,"role"))))(t).each((o=>{e.onRepositionMenu(t,o,[])}))}),(({primary:o,triggeringPath:n})=>{e.onRepositionMenu(t,o,n)}))}};return{uid:e.uid,dom:e.dom,markers:e.markers,behaviours:bu(e.tmenuBehaviours,[Pp.config({mode:"special",onRight:h(((e,t)=>vm(t.element)?A.none():g(e,t,m.HighlightSubmenu))),onLeft:h(((e,t)=>vm(t.element)?A.none():p(e,t))),onEscape:h(((t,o)=>p(t,o).orThunk((()=>e.onEscape(t,o).map((()=>t)))))),focusIn:(e,t)=>{n.getPrimary().each((t=>{Nr(e,t.element,mr())}))}}),Gm.config({highlightClass:e.markers.selectedMenu,itemClass:e.markers.menu}),wm.config({find:e=>Gm.getHighlighted(e)}),Kp.config({})]),eventOrder:e.eventOrder,apis:y,events:f}},extraApis:{tieredData:(e,t,o)=>({primary:e,menus:t,expansions:o}),singleData:(e,t)=>({primary:e,menus:Ms(e,t),expansions:{}}),collapseItem:e=>({value:la(Hh()),meta:{text:e}})}}),Ph=fm({name:"InlineView",configFields:[os("lazySink"),Di("onShow"),Di("onHide"),fs("onEscape"),hu("inlineBehaviours",[Xd,pu,Al]),vs("fireDismissalEventInstead",[ys("event",Cr())]),vs("fireRepositionEventInstead",[ys("event",Or())]),ys("getRelated",A.none),ys("isExtraPart",T),ys("eventOrder",A.none)],factory:(e,t)=>{const o=(t,o,n,s)=>{const r=e.lazySink(t).getOrDie();Xd.openWhileCloaked(t,o,(()=>Sd.positionWithinBounds(r,t,n,s()))),pu.setValue(t,A.some({mode:"position",config:n,getBounds:s}))},n=(t,o,n,s)=>{const r=((e,t,o,n,s)=>{const r=()=>e.lazySink(t),a="horizontal"===n.type?{layouts:{onLtr:()=>fl(),onRtl:()=>bl()}}:{},i=e=>(e=>2===e.length)(e)?a:{};return Lh.sketch({dom:{tag:"div"},data:n.data,markers:n.menu.markers,highlightOnOpen:n.menu.highlightOnOpen,fakeFocus:n.menu.fakeFocus,onEscape:()=>(Xd.close(t),e.onEscape.map((e=>e(t))),A.some(!0)),onExecute:()=>A.some(!0),onOpenMenu:(e,t)=>{Sd.positionWithinBounds(r().getOrDie(),t,o,s())},onOpenSubmenu:(e,t,o,n)=>{const s=r().getOrDie();Sd.position(s,o,{anchor:{type:"submenu",item:t,...i(n)}})},onRepositionMenu:(e,t,n)=>{const a=r().getOrDie();Sd.positionWithinBounds(a,t,o,s()),L(n,(e=>{const t=i(e.triggeringPath);Sd.position(a,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem,...t}})}))}})})(e,t,o,n,s);Xd.open(t,r),pu.setValue(t,A.some({mode:"menu",menu:r}))},s=t=>{Xd.isOpen(t)&&pu.getValue(t).each((o=>{switch(o.mode){case"menu":Xd.getState(t).each(Lh.repositionMenus);break;case"position":const n=e.lazySink(t).getOrDie();Sd.positionWithinBounds(n,t,o.config,o.getBounds())}}))},r={setContent:(e,t)=>{Xd.setContent(e,t)},showAt:(e,t,n)=>{const s=A.none;o(e,t,n,s)},showWithinBounds:o,showMenuAt:(e,t,o)=>{n(e,t,o,A.none)},showMenuWithinBounds:n,hide:e=>{Xd.isOpen(e)&&(pu.setValue(e,A.none()),Xd.close(e))},getContent:e=>Xd.getState(e),reposition:s,isOpen:Xd.isOpen};return{uid:e.uid,dom:e.dom,behaviours:bu(e.inlineBehaviours,[Xd.config({isPartOf:(t,o,n)=>vi(o,n)||((t,o)=>e.getRelated(t).exists((e=>vi(e,o))))(t,n),getAttachPoint:t=>e.lazySink(t).getOrDie(),onOpen:t=>{e.onShow(t)},onClose:t=>{e.onHide(t)}}),pu.config({store:{mode:"memory",initialValue:A.none()}}),Al.config({channels:{...Qd({isExtraPart:t.isExtraPart,...e.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...tu({...e.fireRepositionEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({}),doReposition:s})}})]),eventOrder:e.eventOrder,apis:r}},apis:{showAt:(e,t,o,n)=>{e.showAt(t,o,n)},showWithinBounds:(e,t,o,n,s)=>{e.showWithinBounds(t,o,n,s)},showMenuAt:(e,t,o,n)=>{e.showMenuAt(t,o,n)},showMenuWithinBounds:(e,t,o,n,s)=>{e.showMenuWithinBounds(t,o,n,s)},hide:(e,t)=>{e.hide(t)},isOpen:(e,t)=>e.isOpen(t),getContent:(e,t)=>e.getContent(t),setContent:(e,t,o)=>{e.setContent(t,o)},reposition:(e,t)=>{e.reposition(t)}}});var Uh=tinymce.util.Tools.resolve("tinymce.util.Delay");const Wh=fm({name:"Button",factory:e=>{const t=mh(e.action),o=e.dom.tag,n=t=>be(e.dom,"attributes").bind((e=>be(e,t)));return{uid:e.uid,dom:e.dom,components:e.components,events:t,behaviours:yu(e.buttonBehaviours,[oh.config({}),Pp.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:"button"===o?{type:n("type").getOr("button"),...n("role").map((e=>({role:e}))).getOr({})}:{role:e.role.getOr(n("role").getOr("button"))}},eventOrder:e.eventOrder}},configFields:[ys("uid",void 0),os("dom"),ys("components",[]),vu("buttonBehaviours",[oh,Pp]),us("action"),us("role"),ys("eventOrder",{})]}),jh=e=>{const t=Ie(e),o=it(t),n=(e=>{const t=void 0!==e.dom.attributes?e.dom.attributes:[];return j(t,((e,t)=>"class"===t.name?e:{...e,[t.name]:t.value}),{})})(t),s=(e=>Array.prototype.slice.call(e.dom.classList,0))(t),r=0===o.length?{}:{innerHtml:ea(t)};return{tag:Ue(t),classes:s,attributes:n,...r}},Gh=e=>{const t=(e=>void 0!==e.uid)(e)&&ye(e,"uid")?e.uid:ha("memento");return{get:e=>e.getSystem().getByUid(t).getOrDie(),getOpt:e=>e.getSystem().getByUid(t).toOptional(),asSpec:()=>({...e,uid:t})}};function $h(e){return $h="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},$h(e)}function qh(e,t){return qh=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},qh(e,t)}function Xh(e,t,o){return Xh=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}()?Reflect.construct:function(e,t,o){var n=[null];n.push.apply(n,t);var s=new(Function.bind.apply(e,n));return o&&qh(s,o.prototype),s},Xh.apply(null,arguments)}function Yh(e){return function(e){if(Array.isArray(e))return Kh(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return Kh(e,t);var o=Object.prototype.toString.call(e).slice(8,-1);return"Object"===o&&e.constructor&&(o=e.constructor.name),"Map"===o||"Set"===o?Array.from(e):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?Kh(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Kh(e,t){(null==t||t>e.length)&&(t=e.length);for(var o=0,n=new Array(t);o<t;o++)n[o]=e[o];return n}var Jh=Object.hasOwnProperty,Zh=Object.setPrototypeOf,Qh=Object.isFrozen,ef=Object.getPrototypeOf,tf=Object.getOwnPropertyDescriptor,of=Object.freeze,nf=Object.seal,sf=Object.create,rf="undefined"!=typeof Reflect&&Reflect,af=rf.apply,lf=rf.construct;af||(af=function(e,t,o){return e.apply(t,o)}),of||(of=function(e){return e}),nf||(nf=function(e){return e}),lf||(lf=function(e,t){return Xh(e,Yh(t))});var cf,df=xf(Array.prototype.forEach),uf=xf(Array.prototype.pop),mf=xf(Array.prototype.push),gf=xf(String.prototype.toLowerCase),pf=xf(String.prototype.match),hf=xf(String.prototype.replace),ff=xf(String.prototype.indexOf),bf=xf(String.prototype.trim),vf=xf(RegExp.prototype.test),yf=(cf=TypeError,function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return lf(cf,t)});function xf(e){return function(t){for(var o=arguments.length,n=new Array(o>1?o-1:0),s=1;s<o;s++)n[s-1]=arguments[s];return af(e,t,n)}}function wf(e,t){Zh&&Zh(e,null);for(var o=t.length;o--;){var n=t[o];if("string"==typeof n){var s=gf(n);s!==n&&(Qh(t)||(t[o]=s),n=s)}e[n]=!0}return e}function Sf(e){var t,o=sf(null);for(t in e)af(Jh,e,[t])&&(o[t]=e[t]);return o}function kf(e,t){for(;null!==e;){var o=tf(e,t);if(o){if(o.get)return xf(o.get);if("function"==typeof o.value)return xf(o.value)}e=ef(e)}return function(e){return console.warn("fallback value for",e),null}}var Cf=of(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Of=of(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),_f=of(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Tf=of(["animate","color-profile","cursor","discard","fedropshadow","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Ef=of(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover"]),Af=of(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Mf=of(["#text"]),Df=of(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),Bf=of(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Ff=of(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),If=of(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),Rf=nf(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Nf=nf(/<%[\w\W]*|[\w\W]*%>/gm),Vf=nf(/^data-[\-\w.\u00B7-\uFFFF]/),zf=nf(/^aria-[\-\w]+$/),Hf=nf(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Lf=nf(/^(?:\w+script|data):/i),Pf=nf(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),Uf=nf(/^html$/i),Wf=function(){return"undefined"==typeof window?null:window},jf=function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Wf(),o=function(t){return e(t)};if(o.version="2.3.8",o.removed=[],!t||!t.document||9!==t.document.nodeType)return o.isSupported=!1,o;var n=t.document,s=t.document,r=t.DocumentFragment,a=t.HTMLTemplateElement,i=t.Node,l=t.Element,c=t.NodeFilter,d=t.NamedNodeMap,u=void 0===d?t.NamedNodeMap||t.MozNamedAttrMap:d,m=t.HTMLFormElement,g=t.DOMParser,p=t.trustedTypes,h=l.prototype,f=kf(h,"cloneNode"),b=kf(h,"nextSibling"),v=kf(h,"childNodes"),y=kf(h,"parentNode");if("function"==typeof a){var x=s.createElement("template");x.content&&x.content.ownerDocument&&(s=x.content.ownerDocument)}var w=function(e,t){if("object"!==$h(e)||"function"!=typeof e.createPolicy)return null;var o=null,n="data-tt-policy-suffix";t.currentScript&&t.currentScript.hasAttribute(n)&&(o=t.currentScript.getAttribute(n));var s="dompurify"+(o?"#"+o:"");try{return e.createPolicy(s,{createHTML:function(e){return e}})}catch(e){return console.warn("TrustedTypes policy "+s+" could not be created."),null}}(p,n),S=w?w.createHTML(""):"",k=s,C=k.implementation,O=k.createNodeIterator,_=k.createDocumentFragment,T=k.getElementsByTagName,E=n.importNode,A={};try{A=Sf(s).documentMode?s.documentMode:{}}catch(e){}var M={};o.isSupported="function"==typeof y&&C&&void 0!==C.createHTMLDocument&&9!==A;var D,B,F=Rf,I=Nf,R=Vf,N=zf,V=Lf,z=Pf,H=Hf,L=null,P=wf({},[].concat(Yh(Cf),Yh(Of),Yh(_f),Yh(Ef),Yh(Mf))),U=null,W=wf({},[].concat(Yh(Df),Yh(Bf),Yh(Ff),Yh(If))),j=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),G=null,$=null,q=!0,X=!0,Y=!1,K=!1,J=!1,Z=!1,Q=!1,ee=!1,te=!1,oe=!1,ne=!0,se=!0,re=!1,ae={},ie=null,le=wf({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),ce=null,de=wf({},["audio","video","img","source","image","track"]),ue=null,me=wf({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),ge="http://www.w3.org/1998/Math/MathML",pe="http://www.w3.org/2000/svg",he="http://www.w3.org/1999/xhtml",fe=he,be=!1,ve=["application/xhtml+xml","text/html"],ye=null,xe=s.createElement("form"),we=function(e){return e instanceof RegExp||e instanceof Function},Se=function(e){ye&&ye===e||(e&&"object"===$h(e)||(e={}),e=Sf(e),L="ALLOWED_TAGS"in e?wf({},e.ALLOWED_TAGS):P,U="ALLOWED_ATTR"in e?wf({},e.ALLOWED_ATTR):W,ue="ADD_URI_SAFE_ATTR"in e?wf(Sf(me),e.ADD_URI_SAFE_ATTR):me,ce="ADD_DATA_URI_TAGS"in e?wf(Sf(de),e.ADD_DATA_URI_TAGS):de,ie="FORBID_CONTENTS"in e?wf({},e.FORBID_CONTENTS):le,G="FORBID_TAGS"in e?wf({},e.FORBID_TAGS):{},$="FORBID_ATTR"in e?wf({},e.FORBID_ATTR):{},ae="USE_PROFILES"in e&&e.USE_PROFILES,q=!1!==e.ALLOW_ARIA_ATTR,X=!1!==e.ALLOW_DATA_ATTR,Y=e.ALLOW_UNKNOWN_PROTOCOLS||!1,K=e.SAFE_FOR_TEMPLATES||!1,J=e.WHOLE_DOCUMENT||!1,ee=e.RETURN_DOM||!1,te=e.RETURN_DOM_FRAGMENT||!1,oe=e.RETURN_TRUSTED_TYPE||!1,Q=e.FORCE_BODY||!1,ne=!1!==e.SANITIZE_DOM,se=!1!==e.KEEP_CONTENT,re=e.IN_PLACE||!1,H=e.ALLOWED_URI_REGEXP||H,fe=e.NAMESPACE||he,e.CUSTOM_ELEMENT_HANDLING&&we(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(j.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&we(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(j.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(j.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),D=D=-1===ve.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,B="application/xhtml+xml"===D?function(e){return e}:gf,K&&(X=!1),te&&(ee=!0),ae&&(L=wf({},Yh(Mf)),U=[],!0===ae.html&&(wf(L,Cf),wf(U,Df)),!0===ae.svg&&(wf(L,Of),wf(U,Bf),wf(U,If)),!0===ae.svgFilters&&(wf(L,_f),wf(U,Bf),wf(U,If)),!0===ae.mathMl&&(wf(L,Ef),wf(U,Ff),wf(U,If))),e.ADD_TAGS&&(L===P&&(L=Sf(L)),wf(L,e.ADD_TAGS)),e.ADD_ATTR&&(U===W&&(U=Sf(U)),wf(U,e.ADD_ATTR)),e.ADD_URI_SAFE_ATTR&&wf(ue,e.ADD_URI_SAFE_ATTR),e.FORBID_CONTENTS&&(ie===le&&(ie=Sf(ie)),wf(ie,e.FORBID_CONTENTS)),se&&(L["#text"]=!0),J&&wf(L,["html","head","body"]),L.table&&(wf(L,["tbody"]),delete G.tbody),of&&of(e),ye=e)},ke=wf({},["mi","mo","mn","ms","mtext"]),Ce=wf({},["foreignobject","desc","title","annotation-xml"]),Oe=wf({},["title","style","font","a","script"]),_e=wf({},Of);wf(_e,_f),wf(_e,Tf);var Te=wf({},Ef);wf(Te,Af);var Ee=function(e){mf(o.removed,{element:e});try{e.parentNode.removeChild(e)}catch(t){try{e.outerHTML=S}catch(t){e.remove()}}},Ae=function(e,t){try{mf(o.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){mf(o.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e&&!U[e])if(ee||te)try{Ee(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},Me=function(e){var t,o;if(Q)e="<remove></remove>"+e;else{var n=pf(e,/^[\r\n\t ]+/);o=n&&n[0]}"application/xhtml+xml"===D&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");var r=w?w.createHTML(e):e;if(fe===he)try{t=(new g).parseFromString(r,D)}catch(e){}if(!t||!t.documentElement){t=C.createDocument(fe,"template",null);try{t.documentElement.innerHTML=be?"":r}catch(e){}}var a=t.body||t.documentElement;return e&&o&&a.insertBefore(s.createTextNode(o),a.childNodes[0]||null),fe===he?T.call(t,J?"html":"body")[0]:J?t.documentElement:a},De=function(e){return O.call(e.ownerDocument||e,e,c.SHOW_ELEMENT|c.SHOW_COMMENT|c.SHOW_TEXT,null,!1)},Be=function(e){return"object"===$h(i)?e instanceof i:e&&"object"===$h(e)&&"number"==typeof e.nodeType&&"string"==typeof e.nodeName},Fe=function(e,t,n){M[e]&&df(M[e],(function(e){e.call(o,t,n,ye)}))},Ie=function(e){var t,n;if(Fe("beforeSanitizeElements",e,null),(n=e)instanceof m&&("string"!=typeof n.nodeName||"string"!=typeof n.textContent||"function"!=typeof n.removeChild||!(n.attributes instanceof u)||"function"!=typeof n.removeAttribute||"function"!=typeof n.setAttribute||"string"!=typeof n.namespaceURI||"function"!=typeof n.insertBefore))return Ee(e),!0;if(vf(/[\u0080-\uFFFF]/,e.nodeName))return Ee(e),!0;var s=B(e.nodeName);if(Fe("uponSanitizeElement",e,{tagName:s,allowedTags:L}),e.hasChildNodes()&&!Be(e.firstElementChild)&&(!Be(e.content)||!Be(e.content.firstElementChild))&&vf(/<[/\w]/g,e.innerHTML)&&vf(/<[/\w]/g,e.textContent))return Ee(e),!0;if("select"===s&&vf(/<template/i,e.innerHTML))return Ee(e),!0;if(!L[s]||G[s]){if(!G[s]&&Ne(s)){if(j.tagNameCheck instanceof RegExp&&vf(j.tagNameCheck,s))return!1;if(j.tagNameCheck instanceof Function&&j.tagNameCheck(s))return!1}if(se&&!ie[s]){var r=y(e)||e.parentNode,a=v(e)||e.childNodes;if(a&&r)for(var i=a.length-1;i>=0;--i)r.insertBefore(f(a[i],!0),b(e))}return Ee(e),!0}return e instanceof l&&!function(e){var t=y(e);t&&t.tagName||(t={namespaceURI:he,tagName:"template"});var o=gf(e.tagName),n=gf(t.tagName);return e.namespaceURI===pe?t.namespaceURI===he?"svg"===o:t.namespaceURI===ge?"svg"===o&&("annotation-xml"===n||ke[n]):Boolean(_e[o]):e.namespaceURI===ge?t.namespaceURI===he?"math"===o:t.namespaceURI===pe?"math"===o&&Ce[n]:Boolean(Te[o]):e.namespaceURI===he&&!(t.namespaceURI===pe&&!Ce[n])&&!(t.namespaceURI===ge&&!ke[n])&&!Te[o]&&(Oe[o]||!_e[o])}(e)?(Ee(e),!0):"noscript"!==s&&"noembed"!==s||!vf(/<\/no(script|embed)/i,e.innerHTML)?(K&&3===e.nodeType&&(t=e.textContent,t=hf(t,F," "),t=hf(t,I," "),e.textContent!==t&&(mf(o.removed,{element:e.cloneNode()}),e.textContent=t)),Fe("afterSanitizeElements",e,null),!1):(Ee(e),!0)},Re=function(e,t,o){if(ne&&("id"===t||"name"===t)&&(o in s||o in xe))return!1;if(X&&!$[t]&&vf(R,t));else if(q&&vf(N,t));else if(!U[t]||$[t]){if(!(Ne(e)&&(j.tagNameCheck instanceof RegExp&&vf(j.tagNameCheck,e)||j.tagNameCheck instanceof Function&&j.tagNameCheck(e))&&(j.attributeNameCheck instanceof RegExp&&vf(j.attributeNameCheck,t)||j.attributeNameCheck instanceof Function&&j.attributeNameCheck(t))||"is"===t&&j.allowCustomizedBuiltInElements&&(j.tagNameCheck instanceof RegExp&&vf(j.tagNameCheck,o)||j.tagNameCheck instanceof Function&&j.tagNameCheck(o))))return!1}else if(ue[t]);else if(vf(H,hf(o,z,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==ff(o,"data:")||!ce[e])if(Y&&!vf(V,hf(o,z,"")));else if(o)return!1;return!0},Ne=function(e){return e.indexOf("-")>0},Ve=function(e){var t,o,n,s;Fe("beforeSanitizeAttributes",e,null);var r=e.attributes;if(r){var a={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:U};for(s=r.length;s--;){var i=t=r[s],l=i.name,c=i.namespaceURI;o="value"===l?t.value:bf(t.value),n=B(l);var d=o;if(a.attrName=n,a.attrValue=o,a.keepAttr=!0,a.forceKeepAttr=void 0,Fe("uponSanitizeAttribute",e,a),o=a.attrValue,!a.forceKeepAttr)if(a.keepAttr)if(vf(/\/>/i,o))Ae(l,e);else{K&&(o=hf(o,F," "),o=hf(o,I," "));var u=B(e.nodeName);if(Re(u,n,o)){if(o!==d)try{c?e.setAttributeNS(c,l,o):e.setAttribute(l,o)}catch(t){Ae(l,e)}}else Ae(l,e)}else Ae(l,e)}Fe("afterSanitizeAttributes",e,null)}},ze=function e(t){var o,n=De(t);for(Fe("beforeSanitizeShadowDOM",t,null);o=n.nextNode();)Fe("uponSanitizeShadowNode",o,null),Ie(o)||(o.content instanceof r&&e(o.content),Ve(o));Fe("afterSanitizeShadowDOM",t,null)};return o.sanitize=function(e,s){var a,l,c,d,u;if((be=!e)&&(e="\x3c!--\x3e"),"string"!=typeof e&&!Be(e)){if("function"!=typeof e.toString)throw yf("toString is not a function");if("string"!=typeof(e=e.toString()))throw yf("dirty is not a string, aborting")}if(!o.isSupported){if("object"===$h(t.toStaticHTML)||"function"==typeof t.toStaticHTML){if("string"==typeof e)return t.toStaticHTML(e);if(Be(e))return t.toStaticHTML(e.outerHTML)}return e}if(Z||Se(s),o.removed=[],"string"==typeof e&&(re=!1),re){if(e.nodeName){var m=B(e.nodeName);if(!L[m]||G[m])throw yf("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof i)1===(l=(a=Me("\x3c!----\x3e")).ownerDocument.importNode(e,!0)).nodeType&&"BODY"===l.nodeName||"HTML"===l.nodeName?a=l:a.appendChild(l);else{if(!ee&&!K&&!J&&-1===e.indexOf("<"))return w&&oe?w.createHTML(e):e;if(!(a=Me(e)))return ee?null:oe?S:""}a&&Q&&Ee(a.firstChild);for(var g=De(re?e:a);c=g.nextNode();)3===c.nodeType&&c===d||Ie(c)||(c.content instanceof r&&ze(c.content),Ve(c),d=c);if(d=null,re)return e;if(ee){if(te)for(u=_.call(a.ownerDocument);a.firstChild;)u.appendChild(a.firstChild);else u=a;return U.shadowroot&&(u=E.call(n,u,!0)),u}var p=J?a.outerHTML:a.innerHTML;return J&&L["!doctype"]&&a.ownerDocument&&a.ownerDocument.doctype&&a.ownerDocument.doctype.name&&vf(Uf,a.ownerDocument.doctype.name)&&(p="<!DOCTYPE "+a.ownerDocument.doctype.name+">\n"+p),K&&(p=hf(p,F," "),p=hf(p,I," ")),w&&oe?w.createHTML(p):p},o.setConfig=function(e){Se(e),Z=!0},o.clearConfig=function(){ye=null,Z=!1},o.isValidAttribute=function(e,t,o){ye||Se({});var n=B(e),s=B(t);return Re(n,s,o)},o.addHook=function(e,t){"function"==typeof t&&(M[e]=M[e]||[],mf(M[e],t))},o.removeHook=function(e){if(M[e])return uf(M[e])},o.removeHooks=function(e){M[e]&&(M[e]=[])},o.removeAllHooks=function(){M={}},o}();const Gf=e=>jf().sanitize(e);var $f=tinymce.util.Tools.resolve("tinymce.util.I18n");const qf={indent:!0,outdent:!0,"table-insert-column-after":!0,"table-insert-column-before":!0,"paste-column-after":!0,"paste-column-before":!0,"unordered-list":!0,"list-bull-circle":!0,"list-bull-default":!0,"list-bull-square":!0},Xf="temporary-placeholder",Yf=e=>()=>be(e,Xf).getOr("!not found!"),Kf=(e,t)=>{const o=e.toLowerCase();if($f.isRtl()){const e=((e,t)=>Ae(e,t)?e:((e,t)=>e+t)(e,t))(o,"-rtl");return ve(t,e)?e:o}return o},Jf=(e,t)=>be(t,Kf(e,t)),Zf=(e,t)=>{const o=t();return Jf(e,o).getOrThunk(Yf(o))},Qf=()=>Jp("add-focusable",[Kr((e=>{gi(e.element,"svg").each((e=>kt(e,"focusable","false")))}))]),eb=(e,t,o,n)=>{var s,r;const a=(e=>!!$f.isRtl()&&ve(qf,e))(t)?["tox-icon--flip"]:[],i=be(o,Kf(t,o)).or(n).getOrThunk(Yf(o));return{dom:{tag:e.tag,attributes:null!==(s=e.attributes)&&void 0!==s?s:{},classes:e.classes.concat(a),innerHtml:i},behaviours:kl([...null!==(r=e.behaviours)&&void 0!==r?r:[],Qf()])}},tb=(e,t,o,n=A.none())=>eb(t,e,o(),n),ob={success:"checkmark",error:"warning",err:"error",warning:"warning",warn:"warning",info:"info"},nb=fm({name:"Notification",factory:e=>{const t=Gh({dom:jh(`<p>${Gf(e.translationProvider(e.text))}</p>`),behaviours:kl([Kp.config({})])}),o=e=>({dom:{tag:"div",classes:["tox-bar"],styles:{width:`${e}%`}}}),n=e=>({dom:{tag:"div",classes:["tox-text"],innerHtml:`${e}%`}}),s=Gh({dom:{tag:"div",classes:e.progress?["tox-progress-bar","tox-progress-indicator"]:["tox-progress-bar"]},components:[{dom:{tag:"div",classes:["tox-bar-container"]},components:[o(0)]},n(0)],behaviours:kl([Kp.config({})])}),r={updateProgress:(e,t)=>{e.getSystem().isConnected()&&s.getOpt(e).each((e=>{Kp.set(e,[{dom:{tag:"div",classes:["tox-bar-container"]},components:[o(t)]},n(t)])}))},updateText:(e,o)=>{if(e.getSystem().isConnected()){const n=t.get(e);Kp.set(n,[ti(o)])}}},a=q([e.icon.toArray(),e.level.toArray(),e.level.bind((e=>A.from(ob[e]))).toArray()]),i=Gh(Wh.sketch({dom:{tag:"button",classes:["tox-notification__dismiss","tox-button","tox-button--naked","tox-button--icon"]},components:[tb("close",{tag:"span",classes:["tox-icon"],attributes:{"aria-label":e.translationProvider("Close")}},e.iconProvider)],action:t=>{e.onAction(t)}})),l=((e,t,o)=>{const n=o(),s=G(e,(e=>ve(n,Kf(e,n))));return eb({tag:"div",classes:["tox-notification__icon"]},s.getOr(Xf),n,A.none())})(a,0,e.iconProvider),c=[l,{dom:{tag:"div",classes:["tox-notification__body"]},components:[t.asSpec()],behaviours:kl([Kp.config({})])}];return{uid:e.uid,dom:{tag:"div",attributes:{role:"alert"},classes:e.level.map((e=>["tox-notification","tox-notification--in",`tox-notification--${e}`])).getOr(["tox-notification","tox-notification--in"])},behaviours:kl([oh.config({}),Jp("notification-events",[Ur(Xs(),(e=>{i.getOpt(e).each(oh.focus)}))])]),components:c.concat(e.progress?[s.asSpec()]:[]).concat(e.closeButton?[i.asSpec()]:[]),apis:r}},configFields:[us("level"),os("progress"),us("icon"),os("onAction"),os("text"),os("iconProvider"),os("translationProvider"),Cs("closeButton",!0)],apis:{updateProgress:(e,t,o)=>{e.updateProgress(t,o)},updateText:(e,t,o)=>{e.updateText(t,o)}}});var sb,rb,ab=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),ib=tinymce.util.Tools.resolve("tinymce.EditorManager"),lb=tinymce.util.Tools.resolve("tinymce.Env");!function(e){e.default="wrap",e.floating="floating",e.sliding="sliding",e.scrolling="scrolling"}(sb||(sb={})),function(e){e.auto="auto",e.top="top",e.bottom="bottom"}(rb||(rb={}));const cb=e=>t=>t.options.get(e),db=e=>t=>A.from(e(t)),ub=e=>{const t=lb.deviceType.isPhone(),o=lb.deviceType.isTablet()||t,n=e.options.register,s=e=>r(e)||!1===e,a=e=>r(e)||h(e);n("skin",{processor:e=>r(e)||!1===e,default:"oxide"}),n("skin_url",{processor:"string"}),n("height",{processor:a,default:Math.max(e.getElement().offsetHeight,400)}),n("width",{processor:a,default:ab.DOM.getStyle(e.getElement(),"width")}),n("min_height",{processor:"number",default:100}),n("min_width",{processor:"number"}),n("max_height",{processor:"number"}),n("max_width",{processor:"number"}),n("style_formats",{processor:"object[]"}),n("style_formats_merge",{processor:"boolean",default:!1}),n("style_formats_autohide",{processor:"boolean",default:!1}),n("line_height_formats",{processor:"string",default:"1 1.1 1.2 1.3 1.4 1.5 2"}),n("font_family_formats",{processor:"string",default:"Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats"}),n("font_size_formats",{processor:"string",default:"8pt 10pt 12pt 14pt 18pt 24pt 36pt"}),n("font_size_input_default_unit",{processor:"string",default:"pt"}),n("block_formats",{processor:"string",default:"Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre"}),n("content_langs",{processor:"object[]"}),n("removed_menuitems",{processor:"string",default:""}),n("menubar",{processor:e=>r(e)||d(e),default:!t}),n("menu",{processor:"object",default:{}}),n("toolbar",{processor:e=>d(e)||r(e)||l(e)?{value:e,valid:!0}:{valid:!1,message:"Must be a boolean, string or array."},default:!0}),V(9,(e=>{n("toolbar"+(e+1),{processor:"string"})})),n("toolbar_mode",{processor:"string",default:o?"scrolling":"floating"}),n("toolbar_groups",{processor:"object",default:{}}),n("toolbar_location",{processor:"string",default:rb.auto}),n("toolbar_persist",{processor:"boolean",default:!1}),n("toolbar_sticky",{processor:"boolean",default:e.inline}),n("toolbar_sticky_offset",{processor:"number",default:0}),n("fixed_toolbar_container",{processor:"string",default:""}),n("fixed_toolbar_container_target",{processor:"object"}),n("ui_mode",{processor:"string",default:"combined"}),n("file_picker_callback",{processor:"function"}),n("file_picker_validator_handler",{processor:"function"}),n("file_picker_types",{processor:"string"}),n("typeahead_urls",{processor:"boolean",default:!0}),n("anchor_top",{processor:s,default:"#top"}),n("anchor_bottom",{processor:s,default:"#bottom"}),n("draggable_modal",{processor:"boolean",default:!1}),n("statusbar",{processor:"boolean",default:!0}),n("elementpath",{processor:"boolean",default:!0}),n("branding",{processor:"boolean",default:!0}),n("promotion",{processor:"boolean",default:!0}),n("resize",{processor:e=>"both"===e||d(e),default:!lb.deviceType.isTouch()}),n("sidebar_show",{processor:"string"}),n("help_accessibility",{processor:"boolean",default:e.hasPlugin("help")})},mb=cb("readonly"),gb=cb("height"),pb=cb("width"),hb=db(cb("min_width")),fb=db(cb("min_height")),bb=db(cb("max_width")),vb=db(cb("max_height")),yb=db(cb("style_formats")),xb=cb("style_formats_merge"),wb=cb("style_formats_autohide"),Sb=cb("content_langs"),kb=cb("removed_menuitems"),Cb=cb("toolbar_mode"),Ob=cb("toolbar_groups"),_b=cb("toolbar_location"),Tb=cb("fixed_toolbar_container"),Eb=cb("fixed_toolbar_container_target"),Ab=cb("toolbar_persist"),Mb=cb("toolbar_sticky_offset"),Db=cb("menubar"),Bb=cb("toolbar"),Fb=cb("file_picker_callback"),Ib=cb("file_picker_validator_handler"),Rb=cb("font_size_input_default_unit"),Nb=cb("file_picker_types"),Vb=cb("typeahead_urls"),zb=cb("anchor_top"),Hb=cb("anchor_bottom"),Lb=cb("draggable_modal"),Pb=cb("statusbar"),Ub=cb("elementpath"),Wb=cb("branding"),jb=cb("resize"),Gb=cb("paste_as_text"),$b=cb("sidebar_show"),qb=cb("promotion"),Xb=cb("help_accessibility"),Yb=e=>!1===e.options.get("skin"),Kb=e=>!1!==e.options.get("menubar"),Jb=e=>{const t=e.options.get("skin_url");if(Yb(e))return t;if(t)return e.documentBaseURI.toAbsolute(t);{const t=e.options.get("skin");return ib.baseURL+"/skins/ui/"+t}},Zb=e=>e.options.get("line_height_formats").split(" "),Qb=e=>{const t=Bb(e),o=r(t),n=l(t)&&t.length>0;return!tv(e)&&(n||o||!0===t)},ev=e=>{const t=V(9,(t=>e.options.get("toolbar"+(t+1)))),o=U(t,r);return Ce(o.length>0,o)},tv=e=>ev(e).fold((()=>{const t=Bb(e);return f(t,r)&&t.length>0}),E),ov=e=>_b(e)===rb.bottom,nv=e=>{var t;if(!e.inline)return A.none();const o=null!==(t=Tb(e))&&void 0!==t?t:"";if(o.length>0)return pi(xt(),o);const n=Eb(e);return g(n)?A.some(Ve(n)):A.none()},sv=e=>e.inline&&nv(e).isSome(),rv=e=>nv(e).getOrThunk((()=>ft(ht(Ve(e.getElement()))))),av=e=>e.inline&&!Kb(e)&&!Qb(e)&&!tv(e),iv=e=>(e.options.get("toolbar_sticky")||e.inline)&&!sv(e)&&!av(e),lv=e=>!sv(e)&&"split"===e.options.get("ui_mode"),cv=e=>{const t=e.options.get("menu");return ce(t,(e=>({...e,items:e.items})))};var dv=Object.freeze({__proto__:null,get ToolbarMode(){return sb},get ToolbarLocation(){return rb},register:ub,getSkinUrl:Jb,isReadOnly:mb,isSkinDisabled:Yb,getHeightOption:gb,getWidthOption:pb,getMinWidthOption:hb,getMinHeightOption:fb,getMaxWidthOption:bb,getMaxHeightOption:vb,getUserStyleFormats:yb,shouldMergeStyleFormats:xb,shouldAutoHideStyleFormats:wb,getLineHeightFormats:Zb,getContentLanguages:Sb,getRemovedMenuItems:kb,isMenubarEnabled:Kb,isMultipleToolbars:tv,isToolbarEnabled:Qb,isToolbarPersist:Ab,getMultipleToolbarsOption:ev,getUiContainer:rv,useFixedContainer:sv,isSplitUiMode:lv,getToolbarMode:Cb,isDraggableModal:Lb,isDistractionFree:av,isStickyToolbar:iv,getStickyToolbarOffset:Mb,getToolbarLocation:_b,isToolbarLocationBottom:ov,getToolbarGroups:Ob,getMenus:cv,getMenubar:Db,getToolbar:Bb,getFilePickerCallback:Fb,getFilePickerTypes:Nb,useTypeaheadUrls:Vb,getAnchorTop:zb,getAnchorBottom:Hb,getFilePickerValidatorHandler:Ib,getFontSizeInputDefaultUnit:Rb,useStatusBar:Pb,useElementPath:Ub,promotionEnabled:qb,useBranding:Wb,getResize:jb,getPasteAsText:Gb,getSidebarShow:$b,useHelpAccessibility:Xb});const uv="[data-mce-autocompleter]",mv=e=>hi(e,uv);var gv;!function(e){e[e.CLOSE_ON_EXECUTE=0]="CLOSE_ON_EXECUTE",e[e.BUBBLE_TO_SANDBOX=1]="BUBBLE_TO_SANDBOX"}(gv||(gv={}));var pv=gv;const hv="tox-menu-nav__js",fv="tox-collection__item",bv="tox-swatch",vv={normal:hv,color:bv},yv="tox-collection__item--enabled",xv="tox-collection__item-icon",wv="tox-collection__item-label",Sv="tox-collection__item-caret",kv="tox-collection__item--active",Cv="tox-collection__item-container",Ov="tox-collection__item-container--row",_v=e=>be(vv,e).getOr(hv),Tv=e=>"color"===e?"tox-swatches":"tox-menu",Ev=e=>({backgroundMenu:"tox-background-menu",selectedMenu:"tox-selected-menu",selectedItem:"tox-collection__item--active",hasIcons:"tox-menu--has-icons",menu:Tv(e),tieredMenu:"tox-tiered-menu"}),Av=e=>{const t=Ev(e);return{backgroundMenu:t.backgroundMenu,selectedMenu:t.selectedMenu,menu:t.menu,selectedItem:t.selectedItem,item:_v(e)}},Mv=(e,t,o)=>{const n=Ev(o);return{tag:"div",classes:q([[n.menu,`tox-menu-${t}-column`],e?[n.hasIcons]:[]])}},Dv=[Dh.parts.items({})],Bv=(e,t,o)=>{const n=Ev(o);return{dom:{tag:"div",classes:q([[n.tieredMenu]])},markers:Av(o)}},Fv=x([us("data"),ys("inputAttributes",{}),ys("inputStyles",{}),ys("tag","input"),ys("inputClasses",[]),Di("onSetValue"),ys("styles",{}),ys("eventOrder",{}),hu("inputBehaviours",[pu,oh]),ys("selectOnFocus",!0)]),Iv=e=>kl([oh.config({onFocus:e.selectOnFocus?e=>{const t=e.element,o=$a(t);t.dom.setSelectionRange(0,o.length)}:b})]),Rv=e=>({...Iv(e),...bu(e.inputBehaviours,[pu.config({store:{mode:"manual",...e.data.map((e=>({initialValue:e}))).getOr({}),getValue:e=>$a(e.element),setValue:(e,t)=>{$a(e.element)!==t&&qa(e.element,t)}},onSetValue:e.onSetValue})])}),Nv=e=>({tag:e.tag,attributes:{type:"text",...e.inputAttributes},styles:e.inputStyles,classes:e.inputClasses}),Vv=fm({name:"Input",configFields:Fv(),factory:(e,t)=>({uid:e.uid,dom:Nv(e),components:[],behaviours:Rv(e),eventOrder:e.eventOrder})}),zv=la("refetch-trigger-event"),Hv=la("redirect-menu-item-interaction"),Lv="tox-menu__searcher",Pv=e=>pi(e.element,`.${Lv}`).bind((t=>e.getSystem().getByDom(t).toOptional())),Uv=Pv,Wv=e=>({fetchPattern:pu.getValue(e),selectionStart:e.element.dom.selectionStart,selectionEnd:e.element.dom.selectionEnd}),jv=e=>{const t=(e,t)=>(t.cut(),A.none()),o=(e,t)=>{const o={interactionEvent:t.event,eventType:t.event.raw.type};return Ir(e,Hv,o),A.some(!0)},n="searcher-events";return{dom:{tag:"div",classes:[fv]},components:[Vv.sketch({inputClasses:[Lv,"tox-textfield"],inputAttributes:{...e.placeholder.map((t=>({placeholder:e.i18n(t)}))).getOr({}),type:"search","aria-autocomplete":"list"},inputBehaviours:kl([Jp(n,[Ur(Zs(),(e=>{Fr(e,zv)})),Ur(Ks(),((e,t)=>{"Escape"===t.event.raw.key&&t.stop()}))]),Pp.config({mode:"special",onLeft:t,onRight:t,onSpace:t,onEnter:o,onEscape:o,onUp:o,onDown:o})]),eventOrder:{keydown:[n,Pp.name()]}})]}},Gv="tox-collection--results__js",$v=e=>{var t;return e.dom?{...e,dom:{...e.dom,attributes:{...null!==(t=e.dom.attributes)&&void 0!==t?t:{},id:la("aria-item-search-result-id"),"aria-selected":"false"}}}:e},qv=(e,t)=>o=>{const n=z(o,t);return H(n,(t=>({dom:e,components:t})))},Xv=(e,t)=>{const o=[];let n=[];return L(e,((e,s)=>{t(e,s)?(n.length>0&&o.push(n),n=[],(ve(e.dom,"innerHtml")||e.components&&e.components.length>0)&&n.push(e)):n.push(e)})),n.length>0&&o.push(n),H(o,(e=>({dom:{tag:"div",classes:["tox-collection__group"]},components:e})))},Yv=(e,t,o)=>Dh.parts.items({preprocess:n=>{const s=H(n,o);return"auto"!==e&&e>1?qv({tag:"div",classes:["tox-collection__group"]},e)(s):Xv(s,((e,o)=>"separator"===t[o].type))}}),Kv=(e,t,o=!0)=>({dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===e?["tox-collection--list"]:["tox-collection--grid"])},components:[Yv(e,t,w)]}),Jv=e=>N(e,(e=>"icon"in e&&void 0!==e.icon)),Zv=e=>(console.error(Kn(e)),console.log(e),A.none()),Qv=(e,t,o,n,s)=>{const r=(a=o,{dom:{tag:"div",classes:["tox-collection","tox-collection--horizontal"]},components:[Dh.parts.items({preprocess:e=>Xv(e,((e,t)=>"separator"===a[t].type))})]});var a;return{value:e,dom:r.dom,components:r.components,items:o}},ey=(e,t,o,n,s)=>{if("color"===s.menuType){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-swatches-menu"]},components:[{dom:{tag:"div",classes:["tox-swatches"]},components:[Dh.parts.items({preprocess:"auto"!==e?qv({tag:"div",classes:["tox-swatches__row"]},e):w})]}]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s.menuType&&"auto"===n){const t=Kv(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s.menuType||"searchable"===s.menuType){const t="searchable"!==s.menuType?Kv(n,o):"search-with-field"===s.searchMode.searchMode?((e,t,o)=>{const n=la("aria-controls-search-results");return{dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===e?["tox-collection--list"]:["tox-collection--grid"])},components:[jv({i18n:$f.translate,placeholder:o.placeholder}),{dom:{tag:"div",classes:[...1===e?["tox-collection--list"]:["tox-collection--grid"],Gv],attributes:{id:n}},components:[Yv(e,t,$v)]}]}})(n,o,s.searchMode):((e,t,o=!0)=>{const n=la("aria-controls-search-results");return{dom:{tag:"div",classes:["tox-menu","tox-collection",Gv].concat(1===e?["tox-collection--list"]:["tox-collection--grid"]),attributes:{id:n}},components:[Yv(e,t,$v)]}})(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("listpreview"===s.menuType&&"auto"!==n){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-collection","tox-collection--toolbar","tox-collection--toolbar-lg"]},components:[Dh.parts.items({preprocess:qv({tag:"div",classes:["tox-collection__group"]},e)})]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}return{value:e,dom:Mv(t,n,s.menuType),components:Dv,items:o}},ty=rs("type"),oy=rs("name"),ny=rs("label"),sy=rs("text"),ry=rs("title"),ay=rs("icon"),iy=rs("value"),ly=is("fetch"),cy=is("getSubmenuItems"),dy=is("onAction"),uy=is("onItemAction"),my=Os("onSetup",(()=>b)),gy=ps("name"),py=ps("text"),hy=ps("icon"),fy=ps("tooltip"),by=ps("label"),vy=ps("shortcut"),yy=fs("select"),xy=Cs("active",!1),wy=Cs("borderless",!1),Sy=Cs("enabled",!0),ky=Cs("primary",!1),Cy=e=>ys("columns",e),Oy=ys("meta",{}),_y=Os("onAction",b),Ty=e=>Ss("type",e),Ey=e=>Qn("name","name",vn((()=>la(`${e}-name`))),Hn),Ay=Dn([ty,py]),My=Dn([Ty("autocompleteitem"),xy,Sy,Oy,iy,py,hy]),Dy=[Sy,fy,hy,py,my],By=Dn([ty,dy].concat(Dy)),Fy=e=>qn("toolbarbutton",By,e),Iy=[xy].concat(Dy),Ry=Dn(Iy.concat([ty,dy])),Ny=e=>qn("ToggleButton",Ry,e),Vy=[Os("predicate",T),ks("scope","node",["node","editor"]),ks("position","selection",["node","selection","line"])],zy=Dy.concat([Ty("contextformbutton"),ky,dy,es("original",w)]),Hy=Iy.concat([Ty("contextformbutton"),ky,dy,es("original",w)]),Ly=Dy.concat([Ty("contextformbutton")]),Py=Iy.concat([Ty("contextformtogglebutton")]),Uy=Jn("type",{contextformbutton:zy,contextformtogglebutton:Hy}),Wy=Dn([Ty("contextform"),Os("initValue",x("")),by,ds("commands",Uy),ms("launch",Jn("type",{contextformbutton:Ly,contextformtogglebutton:Py}))].concat(Vy)),jy=Dn([Ty("contexttoolbar"),rs("items")].concat(Vy)),Gy=[ty,rs("src"),ps("alt"),_s("classes",[],Hn)],$y=Dn(Gy),qy=[ty,sy,gy,_s("classes",["tox-collection__item-label"],Hn)],Xy=Dn(qy),Yy=En((()=>jn("type",{cardimage:$y,cardtext:Xy,cardcontainer:Ky}))),Ky=Dn([ty,Ss("direction","horizontal"),Ss("align","left"),Ss("valign","middle"),ds("items",Yy)]),Jy=[Sy,py,vy,("menuitem",Qn("value","value",vn((()=>la("menuitem-value"))),Nn())),Oy];const Zy=Dn([ty,by,ds("items",Yy),my,_y].concat(Jy)),Qy=Dn([ty,xy,hy].concat(Jy)),ex=[ty,rs("fancytype"),_y],tx=[ys("initData",{})].concat(ex),ox=[fs("select"),Ts("initData",{},[Cs("allowCustomColors",!0),Ss("storageKey","default"),bs("colors",Nn())])].concat(ex),nx=Jn("fancytype",{inserttable:tx,colorswatch:ox}),sx=Dn([ty,my,_y,hy].concat(Jy)),rx=Dn([ty,cy,my,hy].concat(Jy)),ax=Dn([ty,hy,xy,my,dy].concat(Jy)),ix=(e,t,o)=>{const n=Xc(e.element,"."+o);if(n.length>0){const e=$(n,(e=>{const o=e.dom.getBoundingClientRect().top,s=n[0].dom.getBoundingClientRect().top;return Math.abs(o-s)>t})).getOr(n.length);return A.some({numColumns:e,numRows:Math.ceil(n.length/e)})}return A.none()},lx=e=>((e,t)=>kl([Jp(e,t)]))(la("unnamed-events"),e),cx=la("tooltip.exclusive"),dx=la("tooltip.show"),ux=la("tooltip.hide"),mx=(e,t,o)=>{e.getSystem().broadcastOn([cx],{})};var gx=Object.freeze({__proto__:null,hideAllExclusive:mx,setComponents:(e,t,o,n)=>{o.getTooltip().each((e=>{e.getSystem().isConnected()&&Kp.set(e,n)}))}}),px=Object.freeze({__proto__:null,events:(e,t)=>{const o=o=>{t.getTooltip().each((n=>{Bd(n),e.onHide(o,n),t.clearTooltip()})),t.clearTimer()};return Hr(q([[Ur(dx,(o=>{t.resetTimer((()=>{(o=>{if(!t.isShowing()){mx(o);const n=e.lazySink(o).getOrDie(),s=o.getSystem().build({dom:e.tooltipDom,components:e.tooltipComponents,events:Hr("normal"===e.mode?[Ur(qs(),(e=>{Fr(o,dx)})),Ur(Gs(),(e=>{Fr(o,ux)}))]:[]),behaviours:kl([Kp.config({})])});t.setTooltip(s),Ad(n,s),e.onShow(o,s),Sd.position(n,s,{anchor:e.anchor(o)})}})(o)}),e.delay)})),Ur(ux,(n=>{t.resetTimer((()=>{o(n)}),e.delay)})),Ur(dr(),((e,t)=>{const n=t;n.universal||R(n.channels,cx)&&o(e)})),Jr((e=>{o(e)}))],"normal"===e.mode?[Ur(Xs(),(e=>{Fr(e,dx)})),Ur(lr(),(e=>{Fr(e,ux)})),Ur(qs(),(e=>{Fr(e,dx)})),Ur(Gs(),(e=>{Fr(e,ux)}))]:[Ur(Dr(),((e,t)=>{Fr(e,dx)})),Ur(Br(),(e=>{Fr(e,ux)}))]]))}}),hx=[os("lazySink"),os("tooltipDom"),ys("exclusive",!0),ys("tooltipComponents",[]),ys("delay",300),ks("mode","normal",["normal","follow-highlight"]),ys("anchor",(e=>({type:"hotspot",hotspot:e,layouts:{onLtr:x([cl,ll,sl,al,rl,il]),onRtl:x([cl,ll,sl,al,rl,il])}}))),Di("onHide"),Di("onShow")],fx=Object.freeze({__proto__:null,init:()=>{const e=Ql(),t=Ql(),o=()=>{e.on(clearTimeout)},n=x("not-implemented");return _a({getTooltip:t.get,isShowing:t.isSet,setTooltip:t.set,clearTooltip:t.clear,clearTimer:o,resetTimer:(t,n)=>{o(),e.set(setTimeout(t,n))},readState:n})}});const bx=Ol({fields:hx,name:"tooltipping",active:px,state:fx,apis:gx}),vx="silver.readonly",yx=Dn([("readonly",ns("readonly",Ln))]);const xx=(e,t)=>{const o=e.mainUi.outerContainer.element,n=[e.mainUi.mothership,...e.uiMotherships];t&&L(n,(e=>{e.broadcastOn([Yd()],{target:o})})),L(n,(e=>{e.broadcastOn([vx],{readonly:t})}))},wx=(e,t)=>{e.on("init",(()=>{e.mode.isReadOnly()&&xx(t,!0)})),e.on("SwitchMode",(()=>xx(t,e.mode.isReadOnly()))),mb(e)&&e.mode.set("readonly")},Sx=()=>Al.config({channels:{[vx]:{schema:yx,onReceive:(e,t)=>{Rm.set(e,t.readonly)}}}}),kx=e=>Rm.config({disabled:e}),Cx=e=>Rm.config({disabled:e,disableClass:"tox-tbtn--disabled"}),Ox=e=>Rm.config({disabled:e,disableClass:"tox-tbtn--disabled",useNative:!1}),_x=(e,t)=>{const o=e.getApi(t);return e=>{e(o)}},Tx=(e,t)=>Kr((o=>{_x(e,o)((o=>{const n=e.onSetup(o);p(n)&&t.set(n)}))})),Ex=(e,t)=>Jr((o=>_x(e,o)(t.get()))),Ax=(e,t)=>Qr(((o,n)=>{_x(e,o)(e.onAction),e.triggersSubmenu||t!==pv.CLOSE_ON_EXECUTE||(o.getSystem().isConnected()&&Fr(o,hr()),n.stop())})),Mx={[ur()]:["disabling","alloy.base.behaviour","toggling","item-events"]},Dx=we,Bx=(e,t,o,n)=>{const s=Es(b);return{type:"item",dom:t.dom,components:Dx(t.optComponents),data:e.data,eventOrder:Mx,hasSubmenu:e.triggersSubmenu,itemBehaviours:kl([Jp("item-events",[Ax(e,o),Tx(e,s),Ex(e,s)]),(r=()=>!e.enabled||n.isDisabled(),Rm.config({disabled:r,disableClass:"tox-collection__item--state-disabled"})),Sx(),Kp.config({})].concat(e.itemBehaviours))};var r},Fx=e=>({value:e.value,meta:{text:e.text.getOr(""),...e.meta}}),Ix=e=>{const t=lb.os.isMacOS()||lb.os.isiOS(),o=t?{alt:"\u2325",ctrl:"\u2303",shift:"\u21e7",meta:"\u2318",access:"\u2303\u2325"}:{meta:"Ctrl",access:"Shift+Alt"},n=e.split("+"),s=H(n,(e=>{const t=e.toLowerCase().trim();return ve(o,t)?o[t]:e}));return t?s.join(""):s.join("+")},Rx=(e,t,o=[xv])=>tb(e,{tag:"div",classes:o},t),Nx=e=>({dom:{tag:"div",classes:[wv]},components:[ti($f.translate(e))]}),Vx=(e,t)=>({dom:{tag:"div",classes:t,innerHtml:e}}),zx=(e,t)=>({dom:{tag:"div",classes:[wv]},components:[{dom:{tag:e.tag,styles:e.styles},components:[ti($f.translate(t))]}]}),Hx=e=>({dom:{tag:"div",classes:["tox-collection__item-accessory"]},components:[ti(Ix(e))]}),Lx=e=>Rx("checkmark",e,["tox-collection__item-checkmark"]),Px=e=>{const t=e.map((e=>({attributes:{title:$f.translate(e),id:la("menu-item")}}))).getOr({});return{tag:"div",classes:[hv,fv],...t}},Ux=(e,t,o,n=A.none())=>"color"===e.presets?((e,t,o)=>{const n=e.ariaLabel,s=e.value,r=e.iconContent.map((e=>((e,t,o)=>{const n=t();return Jf(e,n).or(o).getOrThunk(Yf(n))})(e,t.icons,o)));return{dom:(()=>{const e=bv,o=r.getOr(""),a=n.map((e=>({title:t.translate(e)}))).getOr({}),i={tag:"div",attributes:a,classes:[e]};return"custom"===s?{...i,tag:"button",classes:[...i.classes,"tox-swatches__picker-btn"],innerHtml:o}:"remove"===s?{...i,classes:[...i.classes,"tox-swatch--remove"],innerHtml:o}:g(s)?{...i,attributes:{...i.attributes,"data-mce-color":s},styles:{"background-color":s},innerHtml:o}:i})(),optComponents:[]}})(e,t,n):((e,t,o,n)=>{const s={tag:"div",classes:[xv]},r=o?e.iconContent.map((e=>tb(e,s,t.icons,n))).orThunk((()=>A.some({dom:s}))):A.none(),a=e.checkMark,i=A.from(e.meta).fold((()=>Nx),(e=>ve(e,"style")?k(zx,e.style):Nx)),l=e.htmlContent.fold((()=>e.textContent.map(i)),(e=>A.some(Vx(e,[wv]))));return{dom:Px(e.ariaLabel),optComponents:[r,l,e.shortcutContent.map(Hx),a,e.caret]}})(e,t,o,n),Wx=(e,t)=>be(e,"tooltipWorker").map((e=>[bx.config({lazySink:t.getSink,tooltipDom:{tag:"div",classes:["tox-tooltip-worker-container"]},tooltipComponents:[],anchor:e=>({type:"submenu",item:e,overrides:{maxHeightFunction:cc}}),mode:"follow-highlight",onShow:(t,o)=>{e((e=>{bx.setComponents(t,[oi({element:Ve(e)})])}))}})])).getOr([]),jx=(e,t)=>{const o=(e=>ab.DOM.encode(e))($f.translate(e));if(t.length>0){const e=new RegExp((e=>e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"))(t),"gi");return o.replace(e,(e=>`<span class="tox-autocompleter-highlight">${e}</span>`))}return o},Gx=(e,t)=>H(e,(e=>{switch(e.type){case"cardcontainer":return((e,t)=>{const o="vertical"===e.direction?"tox-collection__item-container--column":Ov,n="left"===e.align?"tox-collection__item-container--align-left":"tox-collection__item-container--align-right";return{dom:{tag:"div",classes:[Cv,o,n,(()=>{switch(e.valign){case"top":return"tox-collection__item-container--valign-top";case"middle":return"tox-collection__item-container--valign-middle";case"bottom":return"tox-collection__item-container--valign-bottom"}})()]},components:t}})(e,Gx(e.items,t));case"cardimage":return((e,t,o)=>({dom:{tag:"img",classes:t,attributes:{src:e,alt:o.getOr("")}}}))(e.src,e.classes,e.alt);case"cardtext":const o=e.name.exists((e=>R(t.cardText.highlightOn,e))),n=o?A.from(t.cardText.matchText).getOr(""):"";return Vx(jx(e.text,n),e.classes)}})),$x=Yu(Ch(),Oh()),qx=e=>({value:Jx(e)}),Xx=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,Yx=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,Kx=e=>Xx.test(e)||Yx.test(e),Jx=e=>_e(e,"#").toUpperCase(),Zx=e=>{const t=e.toString(16);return(1===t.length?"0"+t:t).toUpperCase()},Qx=e=>{const t=Zx(e.red)+Zx(e.green)+Zx(e.blue);return qx(t)},ew=Math.min,tw=Math.max,ow=Math.round,nw=/^\s*rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)\s*$/i,sw=/^\s*rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d?(?:\.\d+)?)\s*\)\s*$/i,rw=(e,t,o,n)=>({red:e,green:t,blue:o,alpha:n}),aw=e=>{const t=parseInt(e,10);return t.toString()===e&&t>=0&&t<=255},iw=e=>{let t,o,n;const s=(e.hue||0)%360;let r=e.saturation/100,a=e.value/100;if(r=tw(0,ew(r,1)),a=tw(0,ew(a,1)),0===r)return t=o=n=ow(255*a),rw(t,o,n,1);const i=s/60,l=a*r,c=l*(1-Math.abs(i%2-1)),d=a-l;switch(Math.floor(i)){case 0:t=l,o=c,n=0;break;case 1:t=c,o=l,n=0;break;case 2:t=0,o=l,n=c;break;case 3:t=0,o=c,n=l;break;case 4:t=c,o=0,n=l;break;case 5:t=l,o=0,n=c;break;default:t=o=n=0}return t=ow(255*(t+d)),o=ow(255*(o+d)),n=ow(255*(n+d)),rw(t,o,n,1)},lw=e=>{const t=(e=>{const t=(e=>{const t=e.value.replace(Xx,((e,t,o,n)=>t+t+o+o+n+n));return{value:t}})(e),o=Yx.exec(t.value);return null===o?["FFFFFF","FF","FF","FF"]:o})(e),o=parseInt(t[1],16),n=parseInt(t[2],16),s=parseInt(t[3],16);return rw(o,n,s,1)},cw=(e,t,o,n)=>{const s=parseInt(e,10),r=parseInt(t,10),a=parseInt(o,10),i=parseFloat(n);return rw(s,r,a,i)},dw=e=>{if("transparent"===e)return A.some(rw(0,0,0,0));const t=nw.exec(e);if(null!==t)return A.some(cw(t[1],t[2],t[3],"1"));const o=sw.exec(e);return null!==o?A.some(cw(o[1],o[2],o[3],o[4])):A.none()},uw=e=>`rgba(${e.red},${e.green},${e.blue},${e.alpha})`,mw=rw(255,0,0,1),gw=(e,t)=>{e.dispatch("ResizeContent",t)},pw=(e,t)=>{e.dispatch("TextColorChange",t)},hw=(e,t)=>e.dispatch("ResolveName",{name:t.nodeName.toLowerCase(),target:t}),fw=(e,t)=>()=>{e(),t()},bw=e=>yw(e,"NodeChange",(t=>{t.setEnabled(e.selection.isEditable())})),vw=(e,t)=>o=>{const n=bw(e)(o),s=((e,t)=>o=>{const n=Zl(),s=()=>{o.setActive(e.formatter.match(t));const s=e.formatter.formatChanged(t,o.setActive);n.set(s)};return e.initialized?s():e.once("init",s),()=>{e.off("init",s),n.clear()}})(e,t)(o);return()=>{n(),s()}},yw=(e,t,o)=>n=>{const s=()=>o(n),r=()=>{o(n),e.on(t,s)};return e.initialized?r():e.once("init",r),()=>{e.off("init",r),e.off(t,s)}},xw=e=>t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("mceToggleFormat",!1,t.format)}))},ww=(e,t)=>()=>e.execCommand(t);var Sw=tinymce.util.Tools.resolve("tinymce.util.LocalStorage");const kw={},Cw=e=>be(kw,e).getOrThunk((()=>{const t=`tinymce-custom-colors-${e}`,o=Sw.getItem(t);if(m(o)){const e=Sw.getItem("tinymce-custom-colors");Sw.setItem(t,g(e)?e:"[]")}const n=((e,t=10)=>{const o=Sw.getItem(e),n=r(o)?JSON.parse(o):[],s=t-(a=n).length<0?a.slice(0,t):a;var a;const i=e=>{s.splice(e,1)};return{add:o=>{I(s,o).each(i),s.unshift(o),s.length>t&&s.pop(),Sw.setItem(e,JSON.stringify(s))},state:()=>s.slice(0)}})(t,10);return kw[e]=n,n})),Ow=(e,t)=>{Cw(e).add(t)},_w=(e,t,o)=>({hue:e,saturation:t,value:o}),Tw=e=>{let t=0,o=0,n=0;const s=e.red/255,r=e.green/255,a=e.blue/255,i=Math.min(s,Math.min(r,a)),l=Math.max(s,Math.max(r,a));return i===l?(n=i,_w(0,0,100*n)):(t=s===i?3:a===i?1:5,t=60*(t-(s===i?r-a:a===i?s-r:a-s)/(l-i)),o=(l-i)/l,n=l,_w(Math.round(t),Math.round(100*o),Math.round(100*n)))},Ew=e=>Qx(iw(e)),Aw=e=>{return(t=e,Kx(t)?A.some({value:Jx(t)}):A.none()).orThunk((()=>dw(e).map(Qx))).getOrThunk((()=>{const t=document.createElement("canvas");t.height=1,t.width=1;const o=t.getContext("2d");o.clearRect(0,0,t.width,t.height),o.fillStyle="#FFFFFF",o.fillStyle=e,o.fillRect(0,0,1,1);const n=o.getImageData(0,0,1,1).data,s=n[0],r=n[1],a=n[2],i=n[3];return Qx(rw(s,r,a,i))}));var t},Mw="forecolor",Dw="hilitecolor",Bw=e=>{const t=[];for(let o=0;o<e.length;o+=2)t.push({text:e[o+1],value:"#"+Aw(e[o]).value,icon:"checkmark",type:"choiceitem"});return t},Fw=e=>t=>t.options.get(e),Iw="#000000",Rw=(e,t)=>t===Mw&&e.options.isSet("color_map_foreground")?Fw("color_map_foreground")(e):t===Dw&&e.options.isSet("color_map_background")?Fw("color_map_background")(e):Fw("color_map")(e),Nw=(e,t="default")=>Math.max(5,Math.ceil(Math.sqrt(Rw(e,t).length))),Vw=(e,t)=>{const o=Fw("color_cols")(e),n=Nw(e,t);return o===Nw(e)?n:o},zw=(e,t="default")=>Math.round(t===Mw?Fw("color_cols_foreground")(e):t===Dw?Fw("color_cols_background")(e):Fw("color_cols")(e)),Hw=Fw("custom_colors"),Lw=Fw("color_default_foreground"),Pw=Fw("color_default_background"),Uw=(e,t)=>{const o=Ve(e.selection.getStart()),n="hilitecolor"===t?Is(o,(e=>{if(Ge(e)){const t=It(e,"background-color");return Ce(dw(t).exists((e=>0!==e.alpha)),t)}return A.none()})).getOr("rgba(0, 0, 0, 0)"):It(o,"color");return dw(n).map((e=>"#"+Qx(e).value))},Ww=e=>{const t="choiceitem",o={type:t,text:"Remove color",icon:"color-swatch-remove-color",value:"remove"};return e?[o,{type:t,text:"Custom color",icon:"color-picker",value:"custom"}]:[o]},jw=(e,t,o,n)=>{"custom"===o?Jw(e)((o=>{o.each((o=>{Ow(t,o),e.execCommand("mceApplyTextcolor",t,o),n(o)}))}),Uw(e,t).getOr(Iw)):"remove"===o?(n(""),e.execCommand("mceRemoveTextcolor",t)):(n(o),e.execCommand("mceApplyTextcolor",t,o))},Gw=(e,t,o)=>e.concat((e=>H(Cw(e).state(),(e=>({type:"choiceitem",text:e,icon:"checkmark",value:e}))))(t).concat(Ww(o))),$w=(e,t,o)=>n=>{n(Gw(e,t,o))},qw=(e,t,o)=>{const n="forecolor"===t?"tox-icon-text-color__color":"tox-icon-highlight-bg-color__color";e.setIconFill(n,o)},Xw=(e,t)=>o=>{const n=Uw(e,t);return xe(n,o.toUpperCase())},Yw=(e,t,o,n,s)=>{e.ui.registry.addSplitButton(t,{tooltip:n,presets:"color",icon:"forecolor"===t?"text-color":"highlight-bg-color",select:Xw(e,o),columns:zw(e,o),fetch:$w(Rw(e,o),o,Hw(e)),onAction:t=>{jw(e,o,s.get(),b)},onItemAction:(n,r)=>{jw(e,o,r,(o=>{s.set(o),pw(e,{name:t,color:o})}))},onSetup:o=>{qw(o,t,s.get());const n=e=>{e.name===t&&qw(o,e.name,e.color)};return e.on("TextColorChange",n),fw(bw(e)(o),(()=>{e.off("TextColorChange",n)}))}})},Kw=(e,t,o,n,s)=>{e.ui.registry.addNestedMenuItem(t,{text:n,icon:"forecolor"===t?"text-color":"highlight-bg-color",onSetup:o=>(qw(o,t,s.get()),bw(e)(o)),getSubmenuItems:()=>[{type:"fancymenuitem",fancytype:"colorswatch",select:Xw(e,o),initData:{storageKey:o},onAction:n=>{jw(e,o,n.value,(o=>{s.set(o),pw(e,{name:t,color:o})}))}}]})},Jw=e=>(t,o)=>{let n=!1;const s={colorpicker:o};e.windowManager.open({title:"Color Picker",size:"normal",body:{type:"panel",items:[{type:"colorpicker",name:"colorpicker",label:"Color"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:s,onAction:(e,t)=>{"hex-valid"===t.name&&(n=t.value)},onSubmit:o=>{const s=o.getData().colorpicker;n?(t(A.from(s)),o.close()):e.windowManager.alert(e.translate(["Invalid hex color code: {0}",s]))},onClose:b,onCancel:()=>{t(A.none())}})},Zw=(e,t,o,n,s,r,a,i)=>{const l=Jv(t),c=Qw(t,o,n,"color"!==s?"normal":"color",r,a,i);return ey(e,l,c,n,{menuType:s})},Qw=(e,t,o,n,s,r,a)=>we(H(e,(i=>{return"choiceitem"===i.type?(l=i,qn("choicemenuitem",Qy,l)).fold(Zv,(i=>A.some(((e,t,o,n,s,r,a,i=!0)=>{const l=Ux({presets:o,textContent:t?e.text:A.none(),htmlContent:A.none(),ariaLabel:e.text,iconContent:e.icon,shortcutContent:t?e.shortcut:A.none(),checkMark:t?A.some(Lx(a.icons)):A.none(),caret:A.none(),value:e.value},a,i);return fn(Bx({data:Fx(e),enabled:e.enabled,getApi:e=>({setActive:t=>{dh.set(e,t)},isActive:()=>dh.isOn(e),isEnabled:()=>!Rm.isDisabled(e),setEnabled:t=>Rm.set(e,!t)}),onAction:t=>n(e.value),onSetup:e=>(e.setActive(s),b),triggersSubmenu:!1,itemBehaviours:[]},l,r,a),{toggling:{toggleClass:yv,toggleOnExecute:!1,selected:e.active,exclusive:!0}})})(i,1===o,n,t,r(i.value),s,a,Jv(e))))):A.none();var l}))),eS=(e,t)=>{const o=Av(t);return 1===e?{mode:"menu",moveOnTab:!0}:"auto"===e?{mode:"grid",selector:"."+o.item,initSize:{numColumns:1,numRows:1}}:{mode:"matrix",rowSelector:"."+("color"===t?"tox-swatches__row":"tox-collection__group"),previousSelector:e=>"color"===t?pi(e.element,"[aria-checked=true]"):A.none()}},tS=la("cell-over"),oS=la("cell-execute"),nS=(e,t,o)=>{const n=o=>Ir(o,oS,{row:e,col:t}),s=(e,t)=>{t.stop(),n(e)};return ri({dom:{tag:"div",attributes:{role:"button","aria-label":o}},behaviours:kl([Jp("insert-table-picker-cell",[Ur(qs(),oh.focus),Ur(ur(),n),Ur(er(),s),Ur(gr(),s)]),dh.config({toggleClass:"tox-insert-table-picker__selected",toggleOnExecute:!1}),oh.config({onFocus:o=>Ir(o,tS,{row:e,col:t})})])})},sS=e=>X(e,(e=>H(e,ai))),rS=(e,t)=>ti(`${t}x${e}`),aS={inserttable:(e,t)=>{const o=(e=>(t,o)=>e.shared.providers.translate(`${o} columns, ${t} rows`))(t),n=((e,t,o)=>{const n=[];for(let t=0;t<10;t++){const o=[];for(let n=0;n<10;n++){const s=e(t+1,n+1);o.push(nS(t,n,s))}n.push(o)}return n})(o),s=rS(0,0),r=Gh({dom:{tag:"span",classes:["tox-insert-table-picker__label"]},components:[s],behaviours:kl([Kp.config({})])});return{type:"widget",data:{value:la("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[$x.widget({dom:{tag:"div",classes:["tox-insert-table-picker"]},components:sS(n).concat(r.asSpec()),behaviours:kl([Jp("insert-table-picker",[Kr((e=>{Kp.set(r.get(e),[s])})),$r(tS,((e,t,o)=>{const{row:s,col:a}=o.event;((e,t,o,n,s)=>{for(let n=0;n<10;n++)for(let s=0;s<10;s++)dh.set(e[n][s],n<=t&&s<=o)})(n,s,a),Kp.set(r.get(e),[rS(s+1,a+1)])})),$r(oS,((t,o,n)=>{const{row:s,col:r}=n.event;e.onAction({numRows:s+1,numColumns:r+1}),Fr(t,hr())}))]),Pp.config({initSize:{numRows:10,numColumns:10},mode:"flatgrid",selector:'[role="button"]'})])})]}},colorswatch:(e,t)=>{const o=((e,t)=>{const o=e.initData.allowCustomColors&&t.colorinput.hasCustomColors();return e.initData.colors.fold((()=>Gw(t.colorinput.getColors(e.initData.storageKey),e.initData.storageKey,o)),(e=>e.concat(Ww(o))))})(e,t),n=t.colorinput.getColorCols(e.initData.storageKey),s="color",r={...Zw(la("menu-value"),o,(t=>{e.onAction({value:t})}),n,s,pv.CLOSE_ON_EXECUTE,e.select.getOr(T),t.shared.providers),markers:Av(s),movement:eS(n,s)};return{type:"widget",data:{value:la("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[$x.widget(Dh.sketch(r))]}}},iS=e=>({type:"separator",dom:{tag:"div",classes:[fv,"tox-collection__group-heading"]},components:e.text.map(ti).toArray()});var lS=Object.freeze({__proto__:null,getCoupled:(e,t,o,n)=>o.getOrCreate(e,t,n),getExistingCoupled:(e,t,o,n)=>o.getExisting(e,t,n)}),cS=[ns("others",$n(sn.value,Nn()))],dS=Object.freeze({__proto__:null,init:()=>{const e={},t=(t,o)=>{if(0===ae(t.others).length)throw new Error("Cannot find any known coupled components");return be(e,o)},o=x({});return _a({readState:o,getExisting:(e,o,n)=>t(o,n).orThunk((()=>(be(o.others,n).getOrDie("No information found for coupled component: "+n),A.none()))),getOrCreate:(o,n,s)=>t(n,s).getOrThunk((()=>{const t=be(n.others,s).getOrDie("No information found for coupled component: "+s)(o),r=o.getSystem().build(t);return e[s]=r,r}))})}});const uS=Ol({fields:cS,name:"coupling",apis:lS,state:dS}),mS=e=>{let t=A.none(),o=[];const n=e=>{s()?r(e):o.push(e)},s=()=>t.isSome(),r=e=>{t.each((t=>{setTimeout((()=>{e(t)}),0)}))};return e((e=>{s()||(t=A.some(e),L(o,r),o=[])})),{get:n,map:e=>mS((t=>{n((o=>{t(e(o))}))})),isReady:s}},gS={nu:mS,pure:e=>mS((t=>{t(e)}))},pS=e=>{setTimeout((()=>{throw e}),0)},hS=e=>{const t=t=>{e().then(t,pS)};return{map:t=>hS((()=>e().then(t))),bind:t=>hS((()=>e().then((e=>t(e).toPromise())))),anonBind:t=>hS((()=>e().then((()=>t.toPromise())))),toLazy:()=>gS.nu(t),toCached:()=>{let t=null;return hS((()=>(null===t&&(t=e()),t)))},toPromise:e,get:t}},fS=e=>hS((()=>new Promise(e))),bS=e=>hS((()=>Promise.resolve(e))),vS=x("sink"),yS=x(ju({name:vS(),overrides:x({dom:{tag:"div"},behaviours:kl([Sd.config({useFixed:E})]),events:Hr([qr(Ks()),qr(Ws()),qr(er())])})})),xS=(e,t)=>{const o=e.getHotspot(t).getOr(t),n="hotspot",s=e.getAnchorOverrides();return e.layouts.fold((()=>({type:n,hotspot:o,overrides:s})),(e=>({type:n,hotspot:o,overrides:s,layouts:e})))},wS=(e,t,o,n,s,r,a)=>{const i=((e,t,o,n,s,r,a)=>{const i=((e,t,o)=>(0,e.fetch)(o).map(t))(e,t,n),l=CS(n,e);return i.map((e=>e.bind((e=>A.from(Lh.sketch({...r.menu(),uid:ha(""),data:e,highlightOnOpen:a,onOpenMenu:(e,t)=>{const n=l().getOrDie();Sd.position(n,t,{anchor:o}),Xd.decloak(s)},onOpenSubmenu:(e,t,o)=>{const n=l().getOrDie();Sd.position(n,o,{anchor:{type:"submenu",item:t}}),Xd.decloak(s)},onRepositionMenu:(e,t,n)=>{const s=l().getOrDie();Sd.position(s,t,{anchor:o}),L(n,(e=>{Sd.position(s,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem}})}))},onEscape:()=>(oh.focus(n),Xd.close(s),A.some(!0))}))))))})(e,t,xS(e,o),o,n,s,a);return i.map((e=>(e.fold((()=>{Xd.isOpen(n)&&Xd.close(n)}),(e=>{Xd.cloak(n),Xd.open(n,e),r(n)})),n)))},SS=(e,t,o,n,s,r,a)=>(Xd.close(n),bS(n)),kS=(e,t,o,n,s,r)=>{const a=uS.getCoupled(o,"sandbox");return(Xd.isOpen(a)?SS:wS)(e,t,o,a,n,s,r)},CS=(e,t)=>e.getSystem().getByUid(t.uid+"-"+vS()).map((e=>()=>sn.value(e))).getOrThunk((()=>t.lazySink.fold((()=>()=>sn.error(new Error("No internal sink is specified, nor could an external sink be found"))),(t=>()=>t(e))))),OS=e=>{Xd.getState(e).each((e=>{Lh.repositionMenus(e)}))},_S=(e,t,o)=>{const n=bi(),s=CS(t,e);return{dom:{tag:"div",classes:e.sandboxClasses,attributes:{id:n.id,role:"listbox"}},behaviours:yu(e.sandboxBehaviours,[pu.config({store:{mode:"memory",initialValue:t}}),Xd.config({onOpen:(s,r)=>{const a=xS(e,t);n.link(t.element),e.matchWidth&&((e,t,o)=>{const n=wm.getCurrent(t).getOr(t),s=Jt(e.element);o?Dt(n.element,"min-width",s+"px"):((e,t)=>{Kt.set(e,t)})(n.element,s)})(a.hotspot,r,e.useMinWidth),e.onOpen(a,s,r),void 0!==o&&void 0!==o.onOpen&&o.onOpen(s,r)},onClose:(e,s)=>{n.unlink(t.element),void 0!==o&&void 0!==o.onClose&&o.onClose(e,s)},isPartOf:(e,o,n)=>vi(o,n)||vi(t,n),getAttachPoint:()=>s().getOrDie()}),wm.config({find:e=>Xd.getState(e).bind((e=>wm.getCurrent(e)))}),Al.config({channels:{...Qd({isExtraPart:T}),...tu({doReposition:OS})}})])}},TS=e=>{const t=uS.getCoupled(e,"sandbox");OS(t)},ES=()=>[ys("sandboxClasses",[]),vu("sandboxBehaviours",[wm,Al,Xd,pu])],AS=x([os("dom"),os("fetch"),Di("onOpen"),Bi("onExecute"),ys("getHotspot",A.some),ys("getAnchorOverrides",x({})),wc(),hu("dropdownBehaviours",[dh,uS,Pp,oh]),os("toggleClass"),ys("eventOrder",{}),us("lazySink"),ys("matchWidth",!1),ys("useMinWidth",!1),us("role")].concat(ES())),MS=x([Wu({schema:[Ei(),ys("fakeFocus",!1)],name:"menu",defaults:e=>({onExecute:e.onExecute})}),yS()]),DS=bm({name:"Dropdown",configFields:AS(),partFields:MS(),factory:(e,t,o,n)=>{const s=e=>{Xd.getState(e).each((e=>{Lh.highlightPrimary(e)}))},r=(t,o,s)=>kS(e,w,t,n,o,s),a={expand:e=>{dh.isOn(e)||r(e,b,zh.HighlightNone).get(b)},open:e=>{dh.isOn(e)||r(e,b,zh.HighlightMenuAndItem).get(b)},refetch:t=>uS.getExistingCoupled(t,"sandbox").fold((()=>r(t,b,zh.HighlightMenuAndItem).map(b)),(o=>wS(e,w,t,o,n,b,zh.HighlightMenuAndItem).map(b))),isOpen:dh.isOn,close:e=>{dh.isOn(e)&&r(e,b,zh.HighlightMenuAndItem).get(b)},repositionMenus:e=>{dh.isOn(e)&&TS(e)}},i=(e,t)=>(Rr(e),A.some(!0));return{uid:e.uid,dom:e.dom,components:t,behaviours:bu(e.dropdownBehaviours,[dh.config({toggleClass:e.toggleClass,aria:{mode:"expanded"}}),uS.config({others:{sandbox:t=>_S(e,t,{onOpen:()=>dh.on(t),onClose:()=>dh.off(t)})}}),Pp.config({mode:"special",onSpace:i,onEnter:i,onDown:(e,t)=>{if(DS.isOpen(e)){const t=uS.getCoupled(e,"sandbox");s(t)}else DS.open(e);return A.some(!0)},onEscape:(e,t)=>DS.isOpen(e)?(DS.close(e),A.some(!0)):A.none()}),oh.config({})]),events:mh(A.some((e=>{r(e,s,zh.HighlightMenuAndItem).get(b)}))),eventOrder:{...e.eventOrder,[ur()]:["disabling","toggling","alloy.base.behaviour"]},apis:a,domModification:{attributes:{"aria-haspopup":"true",...e.role.fold((()=>({})),(e=>({role:e}))),..."button"===e.dom.tag?{type:("type",be(e.dom,"attributes").bind((e=>be(e,"type")))).getOr("button")}:{}}}}},apis:{open:(e,t)=>e.open(t),refetch:(e,t)=>e.refetch(t),expand:(e,t)=>e.expand(t),close:(e,t)=>e.close(t),isOpen:(e,t)=>e.isOpen(t),repositionMenus:(e,t)=>e.repositionMenus(t)}}),BS=(e,t,o)=>{Uv(e).each((e=>{var n;((e,t)=>{_t(t.element,"id").each((t=>kt(e.element,"aria-activedescendant",t)))})(e,o),(Ua((n=t).element,Gv)?A.some(n.element):pi(n.element,"."+Gv)).each((t=>{_t(t,"id").each((t=>kt(e.element,"aria-controls",t)))}))})),kt(o.element,"aria-selected","true")},FS=(e,t,o)=>{kt(o.element,"aria-selected","false")},IS=e=>uS.getExistingCoupled(e,"sandbox").bind(Pv).map(Wv).map((e=>e.fetchPattern)).getOr("");var RS;!function(e){e[e.ContentFocus=0]="ContentFocus",e[e.UiFocus=1]="UiFocus"}(RS||(RS={}));const NS=(e,t,o,n,s)=>{const r=o.shared.providers,a=e=>s?{...e,shortcut:A.none(),icon:e.text.isSome()?A.none():e.icon}:e;switch(e.type){case"menuitem":return(i=e,qn("menuitem",sx,i)).fold(Zv,(e=>A.some(((e,t,o,n=!0)=>{const s=Ux({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:A.none(),ariaLabel:e.text,caret:A.none(),checkMark:A.none(),shortcutContent:e.shortcut},o,n);return Bx({data:Fx(e),getApi:e=>({isEnabled:()=>!Rm.isDisabled(e),setEnabled:t=>Rm.set(e,!t)}),enabled:e.enabled,onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},s,t,o)})(a(e),t,r,n))));case"nestedmenuitem":return(e=>qn("nestedmenuitem",rx,e))(e).fold(Zv,(e=>A.some(((e,t,o,n=!0,s=!1)=>{const r=s?(a=o.icons,Rx("chevron-down",a,[Sv])):(e=>Rx("chevron-right",e,[Sv]))(o.icons);var a;const i=Ux({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:A.none(),ariaLabel:e.text,caret:A.some(r),checkMark:A.none(),shortcutContent:e.shortcut},o,n);return Bx({data:Fx(e),getApi:e=>({isEnabled:()=>!Rm.isDisabled(e),setEnabled:t=>Rm.set(e,!t),setIconFill:(t,o)=>{pi(e.element,`svg path[class="${t}"], rect[class="${t}"]`).each((e=>{kt(e,"fill",o)}))}}),enabled:e.enabled,onAction:b,onSetup:e.onSetup,triggersSubmenu:!0,itemBehaviours:[]},i,t,o)})(a(e),t,r,n,s))));case"togglemenuitem":return(e=>qn("togglemenuitem",ax,e))(e).fold(Zv,(e=>A.some(((e,t,o,n=!0)=>{const s=Ux({iconContent:e.icon,textContent:e.text,htmlContent:A.none(),ariaLabel:e.text,checkMark:A.some(Lx(o.icons)),caret:A.none(),shortcutContent:e.shortcut,presets:"normal",meta:e.meta},o,n);return fn(Bx({data:Fx(e),enabled:e.enabled,getApi:e=>({setActive:t=>{dh.set(e,t)},isActive:()=>dh.isOn(e),isEnabled:()=>!Rm.isDisabled(e),setEnabled:t=>Rm.set(e,!t)}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},s,t,o),{toggling:{toggleClass:yv,toggleOnExecute:!1,selected:e.active}})})(a(e),t,r,n))));case"separator":return(e=>qn("separatormenuitem",Ay,e))(e).fold(Zv,(e=>A.some(iS(e))));case"fancymenuitem":return(e=>qn("fancymenuitem",nx,e))(e).fold(Zv,(e=>((e,t)=>be(aS,e.fancytype).map((o=>o(e,t))))(e,o)));default:return console.error("Unknown item in general menu",e),A.none()}var i},VS=(e,t,o,n,s,r,a)=>{const i=1===n,l=!i||Jv(e);return we(H(e,(e=>{switch(e.type){case"separator":return(n=e,qn("Autocompleter.Separator",Ay,n)).fold(Zv,(e=>A.some(iS(e))));case"cardmenuitem":return(e=>qn("cardmenuitem",Zy,e))(e).fold(Zv,(e=>A.some(((e,t,o,n)=>{const s={dom:Px(e.label),optComponents:[A.some({dom:{tag:"div",classes:[Cv,Ov]},components:Gx(e.items,n)})]};return Bx({data:Fx({text:A.none(),...e}),enabled:e.enabled,getApi:e=>({isEnabled:()=>!Rm.isDisabled(e),setEnabled:t=>{Rm.set(e,!t),L(Xc(e.element,"*"),(o=>{e.getSystem().getByDom(o).each((e=>{e.hasConfigured(Rm)&&Rm.set(e,!t)}))}))}}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:A.from(n.itemBehaviours).getOr([])},s,t,o.providers)})({...e,onAction:t=>{e.onAction(t),o(e.value,e.meta)}},s,r,{itemBehaviours:Wx(e.meta,r),cardText:{matchText:t,highlightOn:a}}))));default:return(e=>qn("Autocompleter.Item",My,e))(e).fold(Zv,(e=>A.some(((e,t,o,n,s,r,a,i=!0)=>{const l=Ux({presets:n,textContent:A.none(),htmlContent:o?e.text.map((e=>jx(e,t))):A.none(),ariaLabel:e.text,iconContent:e.icon,shortcutContent:A.none(),checkMark:A.none(),caret:A.none(),value:e.value},a.providers,i,e.icon);return Bx({data:Fx(e),enabled:e.enabled,getApi:x({}),onAction:t=>s(e.value,e.meta),onSetup:x(b),triggersSubmenu:!1,itemBehaviours:Wx(e.meta,a)},l,r,a.providers)})(e,t,i,"normal",o,s,r,l))))}var n})))},zS=(e,t,o,n,s,r)=>{const a=Jv(t),i=we(H(t,(e=>{const t=e=>NS(e,o,n,(e=>s?!ve(e,"text"):a)(e),s);return"nestedmenuitem"===e.type&&e.getSubmenuItems().length<=0?t({...e,enabled:!1}):t(e)}))),l=(e=>"no-search"===e.searchMode?{menuType:"normal"}:{menuType:"searchable",searchMode:e})(r);return(s?Qv:ey)(e,a,i,1,l)},HS=e=>Lh.singleData(e.value,e),LS=(e,t)=>{const o=Es(!1),n=Es(!1),s=ri(Ph.sketch({dom:{tag:"div",classes:["tox-autocompleter"]},components:[],fireDismissalEventInstead:{},inlineBehaviours:kl([Jp("dismissAutocompleter",[Ur(Cr(),(()=>c()))])]),lazySink:t.getSink})),r=()=>Ph.isOpen(s),a=n.get,i=()=>{r()&&Ph.hide(s)},l=()=>Ph.getContent(s).bind((e=>te(e.components(),0))),c=()=>e.execCommand("mceAutocompleterClose"),d=n=>{const r=(n=>{const s=re(n,(e=>A.from(e.columns))).getOr(1);return X(n,(n=>{const r=n.items;return VS(r,n.matchText,((t,s)=>{const r=e.selection.getRng();((e,t)=>mv(Ve(t.startContainer)).map((t=>{const o=e.createRng();return o.selectNode(t.dom),o})))(e.dom,r).each((r=>{const a={hide:()=>c(),reload:t=>{i(),e.execCommand("mceAutocompleterReload",!1,{fetchOptions:t})}};o.set(!0),n.onAction(a,r,t,s),o.set(!1)}))}),s,pv.BUBBLE_TO_SANDBOX,t,n.highlightOn)}))})(n);r.length>0?((t,o)=>{var n;(n=Ve(e.getBody()),pi(n,uv)).each((n=>{const r=re(t,(e=>A.from(e.columns))).getOr(1);Ph.showMenuAt(s,{anchor:{type:"node",root:Ve(e.getBody()),node:A.from(n)}},((e,t,o,n)=>{const s=eS(t,n),r=Av(n);return{data:HS({...e,movement:s,menuBehaviours:lx("auto"!==t?[]:[Kr(((e,t)=>{ix(e,4,r.item).each((({numColumns:t,numRows:o})=>{Pp.setGridSize(e,o,t)}))}))])}),menu:{markers:Av(n),fakeFocus:o===RS.ContentFocus}}})(ey("autocompleter-value",!0,o,r,{menuType:"normal"}),r,RS.ContentFocus,"normal"))})),l().each(Gm.highlightFirst)})(n,r):i()};e.on("AutocompleterStart",(({lookupData:e})=>{n.set(!0),o.set(!1),d(e)})),e.on("AutocompleterUpdate",(({lookupData:e})=>d(e))),e.on("AutocompleterEnd",(()=>{i(),n.set(!1),o.set(!1)}));((e,t)=>{const o=(e,t)=>{Ir(e,Ks(),{raw:t})},n=()=>e.getMenu().bind(Gm.getHighlighted);t.on("keydown",(t=>{const s=t.which;e.isActive()&&(e.isMenuOpen()?13===s?(n().each(Rr),t.preventDefault()):40===s?(n().fold((()=>{e.getMenu().each(Gm.highlightFirst)}),(e=>{o(e,t)})),t.preventDefault(),t.stopImmediatePropagation()):37!==s&&38!==s&&39!==s||n().each((e=>{o(e,t),t.preventDefault(),t.stopImmediatePropagation()})):13!==s&&38!==s&&40!==s||e.cancelIfNecessary())})),t.on("NodeChange",(t=>{e.isActive()&&!e.isProcessingAction()&&mv(Ve(t.element)).isNone()&&e.cancelIfNecessary()}))})({cancelIfNecessary:c,isMenuOpen:r,isActive:a,isProcessingAction:o.get,getMenu:l},e)},PS=["visible","hidden","clip"],US=e=>Me(e).length>0&&!R(PS,e),WS=e=>{if(je(e)){const t=It(e,"overflow-x"),o=It(e,"overflow-y");return US(t)||US(o)}return!1},jS=(e,t)=>lv(e)?(e=>{const t=qc(e,WS),o=0===t.length?bt(e).map(vt).map((e=>qc(e,WS))).getOr([]):t;return oe(o).map((e=>({element:e,others:o.slice(1)})))})(t):A.none(),GS=e=>{const t=[...H(e.others,Jo),en()];return((e,t)=>j(t,((e,t)=>Qo(e,t)),e))(Jo(e.element),t)},$S=(e,t,o)=>hi(e,t,o).isSome(),qS=(e,t)=>{let o=null;return{cancel:()=>{null!==o&&(clearTimeout(o),o=null)},schedule:(...n)=>{o=setTimeout((()=>{e.apply(null,n),o=null}),t)}}},XS=e=>{const t=e.raw;return void 0===t.touches||1!==t.touches.length?A.none():A.some(t.touches[0])},YS=(e,t)=>{const o={stopBackspace:!0,...t},n=(e=>{const t=Ql(),o=Es(!1),n=qS((t=>{e.triggerEvent(pr(),t),o.set(!0)}),400),s=Ds([{key:Hs(),value:e=>(XS(e).each((s=>{n.cancel();const r={x:s.clientX,y:s.clientY,target:e.target};n.schedule(e),o.set(!1),t.set(r)})),A.none())},{key:Ls(),value:e=>(n.cancel(),XS(e).each((e=>{t.on((o=>{((e,t)=>{const o=Math.abs(e.clientX-t.x),n=Math.abs(e.clientY-t.y);return o>5||n>5})(e,o)&&t.clear()}))})),A.none())},{key:Ps(),value:s=>(n.cancel(),t.get().filter((e=>Ze(e.target,s.target))).map((t=>o.get()?(s.prevent(),!1):e.triggerEvent(gr(),s))))}]);return{fireIfReady:(e,t)=>be(s,t).bind((t=>t(e)))}})(o),s=H(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","transitioncancel","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),(t=>tc(e,t,(e=>{n.fireIfReady(e,t).each((t=>{t&&e.kill()})),o.triggerEvent(t,e)&&e.kill()})))),r=Ql(),a=tc(e,"paste",(e=>{n.fireIfReady(e,"paste").each((t=>{t&&e.kill()})),o.triggerEvent("paste",e)&&e.kill(),r.set(setTimeout((()=>{o.triggerEvent(cr(),e)}),0))})),i=tc(e,"keydown",(e=>{o.triggerEvent("keydown",e)?e.kill():o.stopBackspace&&(e=>e.raw.which===$m[0]&&!R(["input","textarea"],Ue(e.target))&&!$S(e.target,'[contenteditable="true"]'))(e)&&e.prevent()})),l=tc(e,"focusin",(e=>{o.triggerEvent("focusin",e)&&e.kill()})),c=Ql(),d=tc(e,"focusout",(e=>{o.triggerEvent("focusout",e)&&e.kill(),c.set(setTimeout((()=>{o.triggerEvent(lr(),e)}),0))}));return{unbind:()=>{L(s,(e=>{e.unbind()})),i.unbind(),l.unbind(),d.unbind(),a.unbind(),r.on(clearTimeout),c.on(clearTimeout)}}},KS=(e,t)=>{const o=be(e,"target").getOr(t);return Es(o)},JS=As([{stopped:[]},{resume:["element"]},{complete:[]}]),ZS=(e,t,o,n,s,r)=>{const a=e(t,n),i=((e,t)=>{const o=Es(!1),n=Es(!1);return{stop:()=>{o.set(!0)},cut:()=>{n.set(!0)},isStopped:o.get,isCut:n.get,event:e,setSource:t.set,getSource:t.get}})(o,s);return a.fold((()=>(r.logEventNoHandlers(t,n),JS.complete())),(e=>{const o=e.descHandler;return Aa(o)(i),i.isStopped()?(r.logEventStopped(t,e.element,o.purpose),JS.stopped()):i.isCut()?(r.logEventCut(t,e.element,o.purpose),JS.complete()):st(e.element).fold((()=>(r.logNoParent(t,e.element,o.purpose),JS.complete())),(n=>(r.logEventResponse(t,e.element,o.purpose),JS.resume(n))))}))},QS=(e,t,o,n,s,r)=>ZS(e,t,o,n,s,r).fold(E,(n=>QS(e,t,o,n,s,r)),T),ek=(e,t,o,n,s)=>{const r=KS(o,n);return QS(e,t,o,n,r,s)},tk=()=>{const e=(()=>{const e={};return{registerId:(t,o,n)=>{le(n,((n,s)=>{const r=void 0!==e[s]?e[s]:{};r[o]=((e,t)=>({cHandler:k.apply(void 0,[e.handler].concat(t)),purpose:e.purpose}))(n,t),e[s]=r}))},unregisterId:t=>{le(e,((e,o)=>{ve(e,t)&&delete e[t]}))},filterByType:t=>be(e,t).map((e=>pe(e,((e,t)=>((e,t)=>({id:e,descHandler:t}))(t,e))))).getOr([]),find:(t,o,n)=>be(e,o).bind((e=>Is(n,(t=>((e,t)=>pa(t).bind((t=>be(e,t))).map((e=>((e,t)=>({element:e,descHandler:t}))(t,e))))(e,t)),t)))}})(),t={},o=o=>{pa(o.element).each((o=>{delete t[o],e.unregisterId(o)}))};return{find:(t,o,n)=>e.find(t,o,n),filter:t=>e.filterByType(t),register:n=>{const s=(e=>{const t=e.element;return pa(t).getOrThunk((()=>((e,t)=>{const o=la(ua+"uid-");return ga(t,o),o})(0,e.element)))})(n);ye(t,s)&&((e,n)=>{const s=t[n];if(s!==e)throw new Error('The tagId "'+n+'" is already used by: '+na(s.element)+"\nCannot use it for: "+na(e.element)+"\nThe conflicting element is"+(yt(s.element)?" ":" not ")+"already in the DOM");o(e)})(n,s);const r=[n];e.registerId(r,s,n.events),t[s]=n},unregister:o,getById:e=>be(t,e)}},ok=fm({name:"Container",factory:e=>{const{attributes:t,...o}=e.dom;return{uid:e.uid,dom:{tag:"div",attributes:{role:"presentation",...t},...o},components:e.components,behaviours:fu(e.containerBehaviours),events:e.events,domModification:e.domModification,eventOrder:e.eventOrder}},configFields:[ys("components",[]),hu("containerBehaviours",[]),ys("events",{}),ys("domModification",{}),ys("eventOrder",{})]}),nk=e=>{const t=t=>st(e.element).fold(E,(e=>Ze(t,e))),o=tk(),n=(e,n)=>o.find(t,e,n),s=YS(e.element,{triggerEvent:(e,t)=>Si(e,t.target,(o=>((e,t,o,n)=>ek(e,t,o,o.target,n))(n,e,t,o)))}),r={debugInfo:x("real"),triggerEvent:(e,t,o)=>{Si(e,t,(s=>ek(n,e,o,t,s)))},triggerFocus:(e,t)=>{pa(e).fold((()=>{Dl(e)}),(o=>{Si(ir(),e,(o=>(((e,t,o,n,s)=>{const r=KS(o,n);ZS(e,t,o,n,r,s)})(n,ir(),{originator:t,kill:b,prevent:b,target:e},e,o),!1)))}))},triggerEscape:(e,t)=>{r.triggerEvent("keydown",e.element,t.event)},getByUid:e=>p(e),getByDom:e=>h(e),build:ri,buildOrPatch:si,addToGui:e=>{l(e)},removeFromGui:e=>{c(e)},addToWorld:e=>{a(e)},removeFromWorld:e=>{i(e)},broadcast:e=>{u(e)},broadcastOn:(e,t)=>{m(e,t)},broadcastEvent:(e,t)=>{g(e,t)},isConnected:E},a=e=>{e.connect(r),$e(e.element)||(o.register(e),L(e.components(),a),r.triggerEvent(br(),e.element,{target:e.element}))},i=e=>{$e(e.element)||(L(e.components(),i),o.unregister(e)),e.disconnect()},l=t=>{Ad(e,t)},c=e=>{Bd(e)},d=e=>{const t=o.filter(dr());L(t,(t=>{const o=t.descHandler;Aa(o)(e)}))},u=e=>{d({universal:!0,data:e})},m=(e,t)=>{d({universal:!1,channels:e,data:t})},g=(e,t)=>((e,t,o)=>{const n=(e=>{const t=Es(!1);return{stop:()=>{t.set(!0)},cut:b,isStopped:t.get,isCut:T,event:e,setSource:O("Cannot set source of a broadcasted event"),getSource:O("Cannot get source of a broadcasted event")}})(t);return L(e,(e=>{const t=e.descHandler;Aa(t)(n)})),n.isStopped()})(o.filter(e),t),p=e=>o.getById(e).fold((()=>sn.error(new Error('Could not find component with uid: "'+e+'" in system.'))),sn.value),h=e=>{const t=pa(e).getOr("not found");return p(t)};return a(e),{root:e,element:e.element,destroy:()=>{s.unbind(),Po(e.element)},add:l,remove:c,getByUid:p,getByDom:h,addToWorld:a,removeFromWorld:i,broadcast:u,broadcastOn:m,broadcastEvent:g}},sk=x([ys("prefix","form-field"),hu("fieldBehaviours",[wm,pu])]),rk=x([ju({schema:[os("dom")],name:"label"}),ju({factory:{sketch:e=>({uid:e.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:e.text}})},schema:[os("text")],name:"aria-descriptor"}),Uu({factory:{sketch:e=>{const t=((e,t)=>{const o={};return le(e,((e,n)=>{R(t,n)||(o[n]=e)})),o})(e,["factory"]);return e.factory.sketch(t)}},schema:[os("factory")],name:"field"})]),ak=bm({name:"FormField",configFields:sk(),partFields:rk(),factory:(e,t,o,n)=>{const s=bu(e.fieldBehaviours,[wm.config({find:t=>om(t,e,"field")}),pu.config({store:{mode:"manual",getValue:e=>wm.getCurrent(e).bind(pu.getValue),setValue:(e,t)=>{wm.getCurrent(e).each((e=>{pu.setValue(e,t)}))}}})]),r=Hr([Kr(((t,o)=>{const n=sm(t,e,["label","field","aria-descriptor"]);n.field().each((t=>{const o=la(e.prefix);n.label().each((e=>{kt(e.element,"for",o),kt(t.element,"id",o)})),n["aria-descriptor"]().each((o=>{const n=la(e.prefix);kt(o.element,"id",n),kt(t.element,"aria-describedby",n)}))}))}))]),a={getField:t=>om(t,e,"field"),getLabel:t=>om(t,e,"label")};return{uid:e.uid,dom:e.dom,components:t,behaviours:s,events:r,apis:a}},apis:{getField:(e,t)=>e.getField(t),getLabel:(e,t)=>e.getLabel(t)}});var ik=Object.freeze({__proto__:null,exhibit:(e,t)=>Ea({attributes:Ds([{key:t.tabAttr,value:"true"}])})}),lk=[ys("tabAttr","data-alloy-tabstop")];const ck=Ol({fields:lk,name:"tabstopping",active:ik});var dk=tinymce.util.Tools.resolve("tinymce.html.Entities");const uk=(e,t,o,n)=>{const s=mk(e,t,o,n);return ak.sketch(s)},mk=(e,t,o,n)=>({dom:gk(o),components:e.toArray().concat([t]),fieldBehaviours:kl(n)}),gk=e=>({tag:"div",classes:["tox-form__group"].concat(e)}),pk=(e,t)=>ak.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[ti(t.translate(e))]}),hk=la("form-component-change"),fk=la("form-close"),bk=la("form-cancel"),vk=la("form-action"),yk=la("form-submit"),xk=la("form-block"),wk=la("form-unblock"),Sk=la("form-tabchange"),kk=la("form-resize"),Ck=["input","textarea"],Ok=e=>{const t=Ue(e);return R(Ck,t)},_k=(e,t)=>{const o=t.getRoot(e).getOr(e.element);Pa(o,t.invalidClass),t.notify.each((t=>{Ok(e.element)&&kt(e.element,"aria-invalid",!1),t.getContainer(e).each((e=>{ta(e,t.validHtml)})),t.onValid(e)}))},Tk=(e,t,o,n)=>{const s=t.getRoot(e).getOr(e.element);La(s,t.invalidClass),t.notify.each((t=>{Ok(e.element)&&kt(e.element,"aria-invalid",!0),t.getContainer(e).each((e=>{ta(e,n)})),t.onInvalid(e,n)}))},Ek=(e,t,o)=>t.validator.fold((()=>bS(sn.value(!0))),(t=>t.validate(e))),Ak=(e,t,o)=>(t.notify.each((t=>{t.onValidate(e)})),Ek(e,t).map((o=>e.getSystem().isConnected()?o.fold((o=>(Tk(e,t,0,o),sn.error(o))),(o=>(_k(e,t),sn.value(o)))):sn.error("No longer in system"))));var Mk=Object.freeze({__proto__:null,markValid:_k,markInvalid:Tk,query:Ek,run:Ak,isInvalid:(e,t)=>{const o=t.getRoot(e).getOr(e.element);return Ua(o,t.invalidClass)}}),Dk=Object.freeze({__proto__:null,events:(e,t)=>e.validator.map((t=>Hr([Ur(t.onEvent,(t=>{Ak(t,e).get(w)}))].concat(t.validateOnLoad?[Kr((t=>{Ak(t,e).get(b)}))]:[])))).getOr({})}),Bk=[os("invalidClass"),ys("getRoot",A.none),vs("notify",[ys("aria","alert"),ys("getContainer",A.none),ys("validHtml",""),Di("onValid"),Di("onInvalid"),Di("onValidate")]),vs("validator",[os("validate"),ys("onEvent","input"),ys("validateOnLoad",!0)])];const Fk=Ol({fields:Bk,name:"invalidating",active:Dk,apis:Mk,extra:{validation:e=>t=>{const o=pu.getValue(t);return bS(e(o))}}}),Ik=Ol({fields:[],name:"unselecting",active:Object.freeze({__proto__:null,events:()=>Hr([Lr(sr(),E)]),exhibit:()=>Ea({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})})}),Rk=la("color-input-change"),Nk=la("color-swatch-change"),Vk=la("color-picker-cancel"),zk=ju({schema:[os("dom")],name:"label"}),Hk=e=>ju({name:e+"-edge",overrides:t=>t.model.manager.edgeActions[e].fold((()=>({})),(e=>({events:Hr([Wr(Hs(),((t,o,n)=>e(t,n)),[t]),Wr(Ws(),((t,o,n)=>e(t,n)),[t]),Wr(js(),((t,o,n)=>{n.mouseIsDown.get()&&e(t,n)}),[t])])})))}),Lk=Hk("top-left"),Pk=Hk("top"),Uk=Hk("top-right"),Wk=Hk("right"),jk=Hk("bottom-right"),Gk=Hk("bottom"),$k=Hk("bottom-left");var qk=[zk,Hk("left"),Wk,Pk,Gk,Lk,Uk,$k,jk,Uu({name:"thumb",defaults:x({dom:{styles:{position:"absolute"}}}),overrides:e=>({events:Hr([Gr(Hs(),e,"spectrum"),Gr(Ls(),e,"spectrum"),Gr(Ps(),e,"spectrum"),Gr(Ws(),e,"spectrum"),Gr(js(),e,"spectrum"),Gr($s(),e,"spectrum")])})}),Uu({schema:[es("mouseIsDown",(()=>Es(!1)))],name:"spectrum",overrides:e=>{const t=e.model.manager,o=(o,n)=>t.getValueFromEvent(n).map((n=>t.setValueFrom(o,e,n)));return{behaviours:kl([Pp.config({mode:"special",onLeft:o=>t.onLeft(o,e),onRight:o=>t.onRight(o,e),onUp:o=>t.onUp(o,e),onDown:o=>t.onDown(o,e)}),oh.config({})]),events:Hr([Ur(Hs(),o),Ur(Ls(),o),Ur(Ws(),o),Ur(js(),((t,n)=>{e.mouseIsDown.get()&&o(t,n)}))])}}})];const Xk=x("slider.change.value"),Yk=e=>{const t=e.event.raw;if((e=>-1!==e.type.indexOf("touch"))(t)){const e=t;return void 0!==e.touches&&1===e.touches.length?A.some(e.touches[0]).map((e=>$t(e.clientX,e.clientY))):A.none()}{const e=t;return void 0!==e.clientX?A.some(e).map((e=>$t(e.clientX,e.clientY))):A.none()}},Kk=e=>e.model.minX,Jk=e=>e.model.minY,Zk=e=>e.model.minX-1,Qk=e=>e.model.minY-1,eC=e=>e.model.maxX,tC=e=>e.model.maxY,oC=e=>e.model.maxX+1,nC=e=>e.model.maxY+1,sC=(e,t,o)=>t(e)-o(e),rC=e=>sC(e,eC,Kk),aC=e=>sC(e,tC,Jk),iC=e=>rC(e)/2,lC=e=>aC(e)/2,cC=e=>e.stepSize,dC=e=>e.snapToGrid,uC=e=>e.snapStart,mC=e=>e.rounded,gC=(e,t)=>void 0!==e[t+"-edge"],pC=e=>gC(e,"left"),hC=e=>gC(e,"right"),fC=e=>gC(e,"top"),bC=e=>gC(e,"bottom"),vC=e=>e.model.value.get(),yC=(e,t)=>({x:e,y:t}),xC=(e,t)=>{Ir(e,Xk(),{value:t})},wC=(e,t,o,n)=>e<t?e:e>o?o:e===t?t-1:Math.max(t,e-n),SC=(e,t,o,n)=>e>o?e:e<t?t:e===o?o+1:Math.min(o,e+n),kC=(e,t,o)=>Math.max(t,Math.min(o,e)),CC=e=>{const{min:t,max:o,range:n,value:s,step:r,snap:a,snapStart:i,rounded:l,hasMinEdge:c,hasMaxEdge:d,minBound:u,maxBound:m,screenRange:g}=e,p=c?t-1:t,h=d?o+1:o;if(s<u)return p;if(s>m)return h;{const e=((e,t,o)=>Math.min(o,Math.max(e,t))-t)(s,u,m),c=kC(e/g*n+t,p,h);return a&&c>=t&&c<=o?((e,t,o,n,s)=>s.fold((()=>{const s=e-t,r=Math.round(s/n)*n;return kC(t+r,t-1,o+1)}),(t=>{const s=(e-t)%n,r=Math.round(s/n),a=Math.floor((e-t)/n),i=Math.floor((o-t)/n),l=t+Math.min(i,a+r)*n;return Math.max(t,l)})))(c,t,o,r,i):l?Math.round(c):c}},OC=e=>{const{min:t,max:o,range:n,value:s,hasMinEdge:r,hasMaxEdge:a,maxBound:i,maxOffset:l,centerMinEdge:c,centerMaxEdge:d}=e;return s<t?r?0:c:s>o?a?i:d:(s-t)/n*l},_C="top",TC="right",EC="bottom",AC="left",MC=e=>e.element.dom.getBoundingClientRect(),DC=(e,t)=>e[t],BC=e=>{const t=MC(e);return DC(t,AC)},FC=e=>{const t=MC(e);return DC(t,TC)},IC=e=>{const t=MC(e);return DC(t,_C)},RC=e=>{const t=MC(e);return DC(t,EC)},NC=e=>{const t=MC(e);return DC(t,"width")},VC=e=>{const t=MC(e);return DC(t,"height")},zC=(e,t,o)=>(e+t)/2-o,HC=(e,t)=>{const o=MC(e),n=MC(t),s=DC(o,AC),r=DC(o,TC),a=DC(n,AC);return zC(s,r,a)},LC=(e,t)=>{const o=MC(e),n=MC(t),s=DC(o,_C),r=DC(o,EC),a=DC(n,_C);return zC(s,r,a)},PC=(e,t)=>{Ir(e,Xk(),{value:t})},UC=(e,t,o)=>{const n={min:Kk(t),max:eC(t),range:rC(t),value:o,step:cC(t),snap:dC(t),snapStart:uC(t),rounded:mC(t),hasMinEdge:pC(t),hasMaxEdge:hC(t),minBound:BC(e),maxBound:FC(e),screenRange:NC(e)};return CC(n)},WC=e=>(t,o)=>((e,t,o)=>{const n=(e>0?SC:wC)(vC(o),Kk(o),eC(o),cC(o));return PC(t,n),A.some(n)})(e,t,o).map(E),jC=(e,t,o,n,s,r)=>{const a=((e,t,o,n,s)=>{const r=NC(e),a=n.bind((t=>A.some(HC(t,e)))).getOr(0),i=s.bind((t=>A.some(HC(t,e)))).getOr(r),l={min:Kk(t),max:eC(t),range:rC(t),value:o,hasMinEdge:pC(t),hasMaxEdge:hC(t),minBound:BC(e),minOffset:0,maxBound:FC(e),maxOffset:r,centerMinEdge:a,centerMaxEdge:i};return OC(l)})(t,r,o,n,s);return BC(t)-BC(e)+a},GC=WC(-1),$C=WC(1),qC=A.none,XC=A.none,YC={"top-left":A.none(),top:A.none(),"top-right":A.none(),right:A.some(((e,t)=>{xC(e,oC(t))})),"bottom-right":A.none(),bottom:A.none(),"bottom-left":A.none(),left:A.some(((e,t)=>{xC(e,Zk(t))}))};var KC=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=UC(e,t,o);return PC(e,n),n},setToMin:(e,t)=>{const o=Kk(t);PC(e,o)},setToMax:(e,t)=>{const o=eC(t);PC(e,o)},findValueOfOffset:UC,getValueFromEvent:e=>Yk(e).map((e=>e.left)),findPositionOfValue:jC,setPositionFromValue:(e,t,o,n)=>{const s=vC(o),r=jC(e,n.getSpectrum(e),s,n.getLeftEdge(e),n.getRightEdge(e),o),a=Jt(t.element)/2;Dt(t.element,"left",r-a+"px")},onLeft:GC,onRight:$C,onUp:qC,onDown:XC,edgeActions:YC});const JC=(e,t)=>{Ir(e,Xk(),{value:t})},ZC=(e,t,o)=>{const n={min:Jk(t),max:tC(t),range:aC(t),value:o,step:cC(t),snap:dC(t),snapStart:uC(t),rounded:mC(t),hasMinEdge:fC(t),hasMaxEdge:bC(t),minBound:IC(e),maxBound:RC(e),screenRange:VC(e)};return CC(n)},QC=e=>(t,o)=>((e,t,o)=>{const n=(e>0?SC:wC)(vC(o),Jk(o),tC(o),cC(o));return JC(t,n),A.some(n)})(e,t,o).map(E),eO=(e,t,o,n,s,r)=>{const a=((e,t,o,n,s)=>{const r=VC(e),a=n.bind((t=>A.some(LC(t,e)))).getOr(0),i=s.bind((t=>A.some(LC(t,e)))).getOr(r),l={min:Jk(t),max:tC(t),range:aC(t),value:o,hasMinEdge:fC(t),hasMaxEdge:bC(t),minBound:IC(e),minOffset:0,maxBound:RC(e),maxOffset:r,centerMinEdge:a,centerMaxEdge:i};return OC(l)})(t,r,o,n,s);return IC(t)-IC(e)+a},tO=A.none,oO=A.none,nO=QC(-1),sO=QC(1),rO={"top-left":A.none(),top:A.some(((e,t)=>{xC(e,Qk(t))})),"top-right":A.none(),right:A.none(),"bottom-right":A.none(),bottom:A.some(((e,t)=>{xC(e,nC(t))})),"bottom-left":A.none(),left:A.none()};var aO=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=ZC(e,t,o);return JC(e,n),n},setToMin:(e,t)=>{const o=Jk(t);JC(e,o)},setToMax:(e,t)=>{const o=tC(t);JC(e,o)},findValueOfOffset:ZC,getValueFromEvent:e=>Yk(e).map((e=>e.top)),findPositionOfValue:eO,setPositionFromValue:(e,t,o,n)=>{const s=vC(o),r=eO(e,n.getSpectrum(e),s,n.getTopEdge(e),n.getBottomEdge(e),o),a=Wt(t.element)/2;Dt(t.element,"top",r-a+"px")},onLeft:tO,onRight:oO,onUp:nO,onDown:sO,edgeActions:rO});const iO=(e,t)=>{Ir(e,Xk(),{value:t})},lO=(e,t)=>({x:e,y:t}),cO=(e,t)=>(o,n)=>((e,t,o,n)=>{const s=e>0?SC:wC,r=t?vC(n).x:s(vC(n).x,Kk(n),eC(n),cC(n)),a=t?s(vC(n).y,Jk(n),tC(n),cC(n)):vC(n).y;return iO(o,lO(r,a)),A.some(r)})(e,t,o,n).map(E),dO=cO(-1,!1),uO=cO(1,!1),mO=cO(-1,!0),gO=cO(1,!0),pO={"top-left":A.some(((e,t)=>{xC(e,yC(Zk(t),Qk(t)))})),top:A.some(((e,t)=>{xC(e,yC(iC(t),Qk(t)))})),"top-right":A.some(((e,t)=>{xC(e,yC(oC(t),Qk(t)))})),right:A.some(((e,t)=>{xC(e,yC(oC(t),lC(t)))})),"bottom-right":A.some(((e,t)=>{xC(e,yC(oC(t),nC(t)))})),bottom:A.some(((e,t)=>{xC(e,yC(iC(t),nC(t)))})),"bottom-left":A.some(((e,t)=>{xC(e,yC(Zk(t),nC(t)))})),left:A.some(((e,t)=>{xC(e,yC(Zk(t),lC(t)))}))};var hO=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=UC(e,t,o.left),s=ZC(e,t,o.top),r=lO(n,s);return iO(e,r),r},setToMin:(e,t)=>{const o=Kk(t),n=Jk(t);iO(e,lO(o,n))},setToMax:(e,t)=>{const o=eC(t),n=tC(t);iO(e,lO(o,n))},getValueFromEvent:e=>Yk(e),setPositionFromValue:(e,t,o,n)=>{const s=vC(o),r=jC(e,n.getSpectrum(e),s.x,n.getLeftEdge(e),n.getRightEdge(e),o),a=eO(e,n.getSpectrum(e),s.y,n.getTopEdge(e),n.getBottomEdge(e),o),i=Jt(t.element)/2,l=Wt(t.element)/2;Dt(t.element,"left",r-i+"px"),Dt(t.element,"top",a-l+"px")},onLeft:dO,onRight:uO,onUp:mO,onDown:gO,edgeActions:pO});const fO=bm({name:"Slider",configFields:[ys("stepSize",1),ys("onChange",b),ys("onChoose",b),ys("onInit",b),ys("onDragStart",b),ys("onDragEnd",b),ys("snapToGrid",!1),ys("rounded",!0),us("snapStart"),ns("model",Jn("mode",{x:[ys("minX",0),ys("maxX",100),es("value",(e=>Es(e.mode.minX))),os("getInitialValue"),Ri("manager",KC)],y:[ys("minY",0),ys("maxY",100),es("value",(e=>Es(e.mode.minY))),os("getInitialValue"),Ri("manager",aO)],xy:[ys("minX",0),ys("maxX",100),ys("minY",0),ys("maxY",100),es("value",(e=>Es({x:e.mode.minX,y:e.mode.minY}))),os("getInitialValue"),Ri("manager",hO)]})),hu("sliderBehaviours",[Pp,pu]),es("mouseIsDown",(()=>Es(!1)))],partFields:qk,factory:(e,t,o,n)=>{const s=t=>nm(t,e,"thumb"),r=t=>nm(t,e,"spectrum"),a=t=>om(t,e,"left-edge"),i=t=>om(t,e,"right-edge"),l=t=>om(t,e,"top-edge"),c=t=>om(t,e,"bottom-edge"),d=e.model,u=d.manager,m=(t,o)=>{u.setPositionFromValue(t,o,e,{getLeftEdge:a,getRightEdge:i,getTopEdge:l,getBottomEdge:c,getSpectrum:r})},g=(e,t)=>{d.value.set(t);const o=s(e);m(e,o)},p=t=>{const o=e.mouseIsDown.get();e.mouseIsDown.set(!1),o&&om(t,e,"thumb").each((o=>{const n=d.value.get();e.onChoose(t,o,n)}))},h=(t,o)=>{o.stop(),e.mouseIsDown.set(!0),e.onDragStart(t,s(t))},f=(t,o)=>{o.stop(),e.onDragEnd(t,s(t)),p(t)};return{uid:e.uid,dom:e.dom,components:t,behaviours:bu(e.sliderBehaviours,[Pp.config({mode:"special",focusIn:t=>om(t,e,"spectrum").map(Pp.focusIn).map(E)}),pu.config({store:{mode:"manual",getValue:e=>d.value.get(),setValue:g}}),Al.config({channels:{[Jd()]:{onReceive:p}}})]),events:Hr([Ur(Xk(),((t,o)=>{((t,o)=>{g(t,o);const n=s(t);e.onChange(t,n,o),A.some(!0)})(t,o.event.value)})),Kr(((t,o)=>{const n=d.getInitialValue();d.value.set(n);const a=s(t);m(t,a);const i=r(t);e.onInit(t,a,i,d.value.get())})),Ur(Hs(),h),Ur(Ps(),f),Ur(Ws(),h),Ur($s(),f)]),apis:{resetToMin:t=>{u.setToMin(t,e)},resetToMax:t=>{u.setToMax(t,e)},setValue:g,refresh:m},domModification:{styles:{position:"relative"}}}},apis:{setValue:(e,t,o)=>{e.setValue(t,o)},resetToMin:(e,t)=>{e.resetToMin(t)},resetToMax:(e,t)=>{e.resetToMax(t)},refresh:(e,t)=>{e.refresh(t)}}}),bO=la("rgb-hex-update"),vO=la("slider-update"),yO=la("palette-update"),xO="form",wO=[hu("formBehaviours",[pu])],SO=e=>"<alloy.field."+e+">",kO=(e,t)=>({uid:e.uid,dom:e.dom,components:t,behaviours:bu(e.formBehaviours,[pu.config({store:{mode:"manual",getValue:t=>{const o=rm(t,e);return ce(o,((e,t)=>e().bind((e=>{return o=wm.getCurrent(e),n=new Error(`Cannot find a current component to extract the value from for form part '${t}': `+na(e.element)),o.fold((()=>sn.error(n)),sn.value);var o,n})).map(pu.getValue)))},setValue:(t,o)=>{le(o,((o,n)=>{om(t,e,n).each((e=>{wm.getCurrent(e).each((e=>{pu.setValue(e,o)}))}))}))}}})]),apis:{getField:(t,o)=>om(t,e,o).bind(wm.getCurrent)}}),CO={getField:Ca(((e,t,o)=>e.getField(t,o))),sketch:e=>{const t=(()=>{const e=[];return{field:(t,o)=>(e.push(t),Ju(xO,SO(t),o)),record:x(e)}})(),o=e(t),n=t.record(),s=H(n,(e=>Uu({name:e,pname:SO(e)})));return mm(xO,wO,s,kO,o)}},OO=la("valid-input"),_O=la("invalid-input"),TO=la("validating-input"),EO="colorcustom.rgb.",AO=(e,t,o,n)=>{const s=(o,n)=>Fk.config({invalidClass:t("invalid"),notify:{onValidate:e=>{Ir(e,TO,{type:o})},onValid:e=>{Ir(e,OO,{type:o,value:pu.getValue(e)})},onInvalid:e=>{Ir(e,_O,{type:o,value:pu.getValue(e)})}},validator:{validate:t=>{const o=pu.getValue(t),s=n(o)?sn.value(!0):sn.error(e("aria.input.invalid"));return bS(s)},validateOnLoad:!1}}),r=(o,n,r,a,i)=>{const l=e(EO+"range"),c=ak.parts.label({dom:{tag:"label",attributes:{"aria-label":a}},components:[ti(r)]}),d=ak.parts.field({data:i,factory:Vv,inputAttributes:{type:"text",..."hex"===n?{"aria-live":"polite"}:{}},inputClasses:[t("textfield")],inputBehaviours:kl([s(n,o),ck.config({})]),onSetValue:e=>{Fk.isInvalid(e)&&Fk.run(e).get(b)}}),u=[c,d],m="hex"!==n?[ak.parts["aria-descriptor"]({text:l})]:[];return{dom:{tag:"div",attributes:{role:"presentation"}},components:u.concat(m)}},a=(e,t)=>{const o=t.red,n=t.green,s=t.blue;pu.setValue(e,{red:o,green:n,blue:s})},i=Gh({dom:{tag:"div",classes:[t("rgba-preview")],styles:{"background-color":"white"},attributes:{role:"presentation"}}}),l=(e,t)=>{i.getOpt(e).each((e=>{Dt(e.element,"background-color","#"+t.value)}))},c=fm({factory:()=>{const s={red:Es(A.some(255)),green:Es(A.some(255)),blue:Es(A.some(255)),hex:Es(A.some("ffffff"))},c=e=>s[e].get(),d=(e,t)=>{s[e].set(t)},u=e=>{const t=e.red,o=e.green,n=e.blue;d("red",A.some(t)),d("green",A.some(o)),d("blue",A.some(n))},m=(e,t)=>{const o=t.event;"hex"!==o.type?d(o.type,A.none()):n(e)},g=(e,t)=>{const n=t.event;(e=>"hex"===e.type)(n)?((e,t)=>{o(e);const n=qx(t);d("hex",A.some(n.value));const s=lw(n);a(e,s),u(s),Ir(e,bO,{hex:n}),l(e,n)})(e,n.value):((e,t,o)=>{const n=parseInt(o,10);d(t,A.some(n)),c("red").bind((e=>c("green").bind((t=>c("blue").map((o=>rw(e,t,o,1))))))).each((t=>{const o=((e,t)=>{const o=Qx(t);return CO.getField(e,"hex").each((t=>{oh.isFocused(t)||pu.setValue(e,{hex:o.value})})),o})(e,t);Ir(e,bO,{hex:o}),l(e,o)}))})(e,n.type,n.value)},p=t=>({label:e(EO+t+".label"),description:e(EO+t+".description")}),h=p("red"),f=p("green"),b=p("blue"),v=p("hex");return fn(CO.sketch((o=>({dom:{tag:"form",classes:[t("rgb-form")],attributes:{"aria-label":e("aria.color.picker")}},components:[o.field("red",ak.sketch(r(aw,"red",h.label,h.description,255))),o.field("green",ak.sketch(r(aw,"green",f.label,f.description,255))),o.field("blue",ak.sketch(r(aw,"blue",b.label,b.description,255))),o.field("hex",ak.sketch(r(Kx,"hex",v.label,v.description,"ffffff"))),i.asSpec()],formBehaviours:kl([Fk.config({invalidClass:t("form-invalid")}),Jp("rgb-form-events",[Ur(OO,g),Ur(_O,m),Ur(TO,m)])])}))),{apis:{updateHex:(e,t)=>{pu.setValue(e,{hex:t.value}),((e,t)=>{const o=lw(t);a(e,o),u(o)})(e,t),l(e,t)}}})},name:"RgbForm",configFields:[],apis:{updateHex:(e,t,o)=>{e.updateHex(t,o)}},extraApis:{}});return c},MO=(e,t)=>{const o=fm({name:"ColourPicker",configFields:[os("dom"),ys("onValidHex",b),ys("onInvalidHex",b)],factory:o=>{const n=AO(e,t,o.onValidHex,o.onInvalidHex),s=((e,t)=>{const o=fO.parts.spectrum({dom:{tag:"canvas",attributes:{role:"presentation"},classes:[t("sv-palette-spectrum")]}}),n=fO.parts.thumb({dom:{tag:"div",attributes:{role:"presentation"},classes:[t("sv-palette-thumb")],innerHtml:`<div class=${t("sv-palette-inner-thumb")} role="presentation"></div>`}}),s=(e,t)=>{const{width:o,height:n}=e,s=e.getContext("2d");if(null===s)return;s.fillStyle=t,s.fillRect(0,0,o,n);const r=s.createLinearGradient(0,0,o,0);r.addColorStop(0,"rgba(255,255,255,1)"),r.addColorStop(1,"rgba(255,255,255,0)"),s.fillStyle=r,s.fillRect(0,0,o,n);const a=s.createLinearGradient(0,0,0,n);a.addColorStop(0,"rgba(0,0,0,0)"),a.addColorStop(1,"rgba(0,0,0,1)"),s.fillStyle=a,s.fillRect(0,0,o,n)};return fm({factory:e=>{const r=x({x:0,y:0}),a=kl([wm.config({find:A.some}),oh.config({})]);return fO.sketch({dom:{tag:"div",attributes:{role:"presentation"},classes:[t("sv-palette")]},model:{mode:"xy",getInitialValue:r},rounded:!1,components:[o,n],onChange:(e,t,o)=>{Ir(e,yO,{value:o})},onInit:(e,t,o,n)=>{s(o.element.dom,uw(mw))},sliderBehaviours:a})},name:"SaturationBrightnessPalette",configFields:[],apis:{setHue:(e,t,o)=>{((e,t)=>{const o=e.components()[0].element.dom,n=_w(t,100,100),r=iw(n);s(o,uw(r))})(t,o)},setThumb:(e,t,o)=>{((e,t)=>{const o=Tw(lw(t));fO.setValue(e,{x:o.saturation,y:100-o.value})})(t,o)}},extraApis:{}})})(0,t),r={paletteRgba:Es(mw),paletteHue:Es(0)},a=Gh(((e,t)=>{const o=fO.parts.spectrum({dom:{tag:"div",classes:[t("hue-slider-spectrum")],attributes:{role:"presentation"}}}),n=fO.parts.thumb({dom:{tag:"div",classes:[t("hue-slider-thumb")],attributes:{role:"presentation"}}});return fO.sketch({dom:{tag:"div",classes:[t("hue-slider")],attributes:{role:"presentation"}},rounded:!1,model:{mode:"y",getInitialValue:x(0)},components:[o,n],sliderBehaviours:kl([oh.config({})]),onChange:(e,t,o)=>{Ir(e,vO,{value:o})}})})(0,t)),i=Gh(s.sketch({})),l=Gh(n.sketch({})),c=(e,t,o)=>{i.getOpt(e).each((e=>{s.setHue(e,o)}))},d=(e,t)=>{l.getOpt(e).each((e=>{n.updateHex(e,t)}))},u=(e,t,o)=>{a.getOpt(e).each((e=>{fO.setValue(e,(e=>100-e/360*100)(o))}))},m=(e,t)=>{i.getOpt(e).each((e=>{s.setThumb(e,t)}))},g=(e,t,o,n)=>{((e,t)=>{const o=lw(e);r.paletteRgba.set(o),r.paletteHue.set(t)})(t,o),L(n,(n=>{n(e,t,o)}))};return{uid:o.uid,dom:o.dom,components:[i.asSpec(),a.asSpec(),l.asSpec()],behaviours:kl([Jp("colour-picker-events",[Ur(bO,(()=>{const e=[c,u,m];return(t,o)=>{const n=o.event.hex,s=(e=>Tw(lw(e)))(n);g(t,n,s.hue,e)}})()),Ur(yO,(()=>{const e=[d];return(t,o)=>{const n=o.event.value,s=r.paletteHue.get(),a=_w(s,n.x,100-n.y),i=Ew(a);g(t,i,s,e)}})()),Ur(vO,(()=>{const e=[c,d];return(t,o)=>{const n=(e=>(100-e)/100*360)(o.event.value),s=r.paletteRgba.get(),a=Tw(s),i=_w(n,a.saturation,a.value),l=Ew(i);g(t,l,n,e)}})())]),wm.config({find:e=>l.getOpt(e)}),Pp.config({mode:"acyclic"})])}}});return o},DO=()=>wm.config({find:A.some}),BO=e=>wm.config({find:t=>lt(t.element,e).bind((e=>t.getSystem().getByDom(e).toOptional()))}),FO=Dn([ys("preprocess",w),ys("postprocess",w)]),IO=(e,t)=>{const o=Yn("RepresentingConfigs.memento processors",FO,t);return pu.config({store:{mode:"manual",getValue:t=>{const n=e.get(t),s=pu.getValue(n);return o.postprocess(s)},setValue:(t,n)=>{const s=o.preprocess(n),r=e.get(t);pu.setValue(r,s)}}})},RO=(e,t,o)=>pu.config({store:{mode:"manual",...e.map((e=>({initialValue:e}))).getOr({}),getValue:t,setValue:o}}),NO=(e,t,o)=>RO(e,(e=>t(e.element)),((e,t)=>o(e.element,t))),VO=e=>pu.config({store:{mode:"memory",initialValue:e}}),zO={"colorcustom.rgb.red.label":"R","colorcustom.rgb.red.description":"Red component","colorcustom.rgb.green.label":"G","colorcustom.rgb.green.description":"Green component","colorcustom.rgb.blue.label":"B","colorcustom.rgb.blue.description":"Blue component","colorcustom.rgb.hex.label":"#","colorcustom.rgb.hex.description":"Hex color code","colorcustom.rgb.range":"Range 0 to 255","aria.color.picker":"Color Picker","aria.input.invalid":"Invalid input"};var HO=tinymce.util.Tools.resolve("tinymce.Resource"),LO=tinymce.util.Tools.resolve("tinymce.util.Tools");const PO=(e,t)=>{let o=null;const n=()=>{c(o)||(clearTimeout(o),o=null)};return{cancel:n,throttle:(...s)=>{n(),o=setTimeout((()=>{o=null,e.apply(null,s)}),t)}}},UO=la("alloy-fake-before-tabstop"),WO=la("alloy-fake-after-tabstop"),jO=e=>({dom:{tag:"div",styles:{width:"1px",height:"1px",outline:"none"},attributes:{tabindex:"0"},classes:e},behaviours:kl([oh.config({ignore:!0}),ck.config({})])}),GO=(e,t)=>({dom:{tag:"div",classes:["tox-navobj",...e.getOr([])]},components:[jO([UO]),t,jO([WO])],behaviours:kl([BO(1)])}),$O=(e,t)=>{Ir(e,Ks(),{raw:{which:9,shiftKey:t}})},qO=(e,t)=>{const o=t.element;Ua(o,UO)?$O(e,!0):Ua(o,WO)&&$O(e,!1)},XO=e=>$S(e,["."+UO,"."+WO].join(","),T),YO=la("update-dialog"),KO=la("update-title"),JO=la("update-body"),ZO=la("update-footer"),QO=la("body-send-message"),e_=la("dialog-focus-shifted"),t_=Do().browser,o_=t_.isSafari(),n_=t_.isFirefox(),s_=o_||n_,r_=t_.isChromium(),a_=({scrollTop:e,scrollHeight:t,clientHeight:o})=>Math.ceil(e)+o>=t,i_=(e,t)=>e.scrollTo(0,"bottom"===t?99999999:t),l_=(e,t,o)=>{const n=e.dom;A.from(n.contentDocument).fold(o,(e=>{let o=0;const s=((e,t)=>{const o=e.body;return A.from(!/^<!DOCTYPE (html|HTML)/.test(t)&&(!r_&&!o_||g(o)&&(0!==o.scrollTop||Math.abs(o.scrollHeight-o.clientHeight)>1))?o:e.documentElement)})(e,t).map((e=>(o=e.scrollTop,e))).forall(a_),r=()=>{const e=n.contentWindow;g(e)&&(s?i_(e,"bottom"):!s&&s_&&0!==o&&i_(e,o))};o_&&n.addEventListener("load",r,{once:!0}),e.open(),e.write(t),e.close(),o_||r()}))},c_=Ce(s_,o_?500:200).map((e=>((e,t)=>{let o=null,n=null;return{cancel:()=>{c(o)||(clearTimeout(o),o=null,n=null)},throttle:(...s)=>{n=s,c(o)&&(o=setTimeout((()=>{const t=n;o=null,n=null,e.apply(null,t)}),t))}}})(l_,e))),d_=la("toolbar.button.execute"),u_=la("common-button-display-events"),m_={[ur()]:["disabling","alloy.base.behaviour","toggling","toolbar-button-events"],[Sr()]:["toolbar-button-events",u_],[Ws()]:["focusing","alloy.base.behaviour",u_]},g_=e=>Dt(e.element,"width",It(e.element,"width")),p_=(e,t,o)=>tb(e,{tag:"span",classes:["tox-icon","tox-tbtn__icon-wrap"],behaviours:o},t),h_=(e,t)=>p_(e,t,[]),f_=(e,t)=>p_(e,t,[Kp.config({})]),b_=(e,t,o)=>({dom:{tag:"span",classes:[`${t}__select-label`]},components:[ti(o.translate(e))],behaviours:kl([Kp.config({})])}),v_=la("update-menu-text"),y_=la("update-menu-icon"),x_=(e,t,o)=>{const n=Es(b),s=e.text.map((e=>Gh(b_(e,t,o.providers)))),r=e.icon.map((e=>Gh(f_(e,o.providers.icons)))),a=(e,t)=>{const o=pu.getValue(e);return oh.focus(o),Ir(o,"keydown",{raw:t.event.raw}),DS.close(o),A.some(!0)},i=e.role.fold((()=>({})),(e=>({role:e}))),l=e.tooltip.fold((()=>({})),(e=>{const t=o.providers.translate(e);return{title:t,"aria-label":t}})),c=tb("chevron-down",{tag:"div",classes:[`${t}__select-chevron`]},o.providers.icons),d=la("common-button-display-events"),u=Gh(DS.sketch({...e.uid?{uid:e.uid}:{},...i,dom:{tag:"button",classes:[t,`${t}--select`].concat(H(e.classes,(e=>`${t}--${e}`))),attributes:{...l}},components:Dx([r.map((e=>e.asSpec())),s.map((e=>e.asSpec())),A.some(c)]),matchWidth:!0,useMinWidth:!0,onOpen:(t,o,n)=>{e.searchable&&(e=>{Uv(e).each((e=>oh.focus(e)))})(n)},dropdownBehaviours:kl([...e.dropdownBehaviours,kx((()=>e.disabled||o.providers.isDisabled())),Sx(),Ik.config({}),Kp.config({}),Jp("dropdown-events",[Tx(e,n),Ex(e,n)]),Jp(d,[Kr(((e,t)=>g_(e)))]),Jp("menubutton-update-display-text",[Ur(v_,((e,t)=>{s.bind((t=>t.getOpt(e))).each((e=>{Kp.set(e,[ti(o.providers.translate(t.event.text))])}))})),Ur(y_,((e,t)=>{r.bind((t=>t.getOpt(e))).each((e=>{Kp.set(e,[f_(t.event.icon,o.providers.icons)])}))}))])]),eventOrder:fn(m_,{mousedown:["focusing","alloy.base.behaviour","item-type-events","normal-dropdown-events"],[Sr()]:["toolbar-button-events","dropdown-events",d]}),sandboxBehaviours:kl([Pp.config({mode:"special",onLeft:a,onRight:a}),Jp("dropdown-sandbox-events",[Ur(zv,((e,t)=>{(e=>{const t=pu.getValue(e),o=Pv(e).map(Wv);DS.refetch(t).get((()=>{const e=uS.getCoupled(t,"sandbox");o.each((t=>Pv(e).each((e=>((e,t)=>{pu.setValue(e,t.fetchPattern),e.element.dom.selectionStart=t.selectionStart,e.element.dom.selectionEnd=t.selectionEnd})(e,t)))))}))})(e),t.stop()})),Ur(Hv,((e,t)=>{((e,t)=>{(e=>Xd.getState(e).bind(Gm.getHighlighted).bind(Gm.getHighlighted))(e).each((o=>{((e,t,o,n)=>{const s={...n,target:t};e.getSystem().triggerEvent(o,t,s)})(e,o.element,t.event.eventType,t.event.interactionEvent)}))})(e,t),t.stop()}))])]),lazySink:o.getSink,toggleClass:`${t}--active`,parts:{menu:{...Bv(0,e.columns,e.presets),fakeFocus:e.searchable,onHighlightItem:BS,onCollapseMenu:(e,t,o)=>{Gm.getHighlighted(o).each((t=>{BS(e,o,t)}))},onDehighlightItem:FS}},getAnchorOverrides:()=>({maxHeightFunction:(e,t)=>{lc()(e,t-10)}}),fetch:t=>fS(k(e.fetch,t))}));return u.asSpec()},w_=e=>"separator"===e.type,S_={type:"separator"},k_=(e,t)=>{const o=((e,t)=>{const o=j(e,((e,o)=>(e=>r(e))(o)?""===o?e:"|"===o?e.length>0&&!w_(e[e.length-1])?e.concat([S_]):e:ve(t,o.toLowerCase())?e.concat([t[o.toLowerCase()]]):e:e.concat([o])),[]);return o.length>0&&w_(o[o.length-1])&&o.pop(),o})(r(e)?e.split(" "):e,t);return W(o,((e,o)=>{if((e=>ve(e,"getSubmenuItems"))(o)){const n=(e=>{const t=be(e,"value").getOrThunk((()=>la("generated-menu-item")));return fn({value:t},e)})(o),s=((e,t)=>{const o=e.getSubmenuItems(),n=k_(o,t);return{item:e,menus:fn(n.menus,{[e.value]:n.items}),expansions:fn(n.expansions,{[e.value]:e.value})}})(n,t);return{menus:fn(e.menus,s.menus),items:[s.item,...e.items],expansions:fn(e.expansions,s.expansions)}}return{...e,items:[o,...e.items]}}),{menus:{},expansions:{},items:[]})},C_=(e,t,o,n)=>{const s=la("primary-menu"),r=k_(e,o.shared.providers.menuItems());if(0===r.items.length)return A.none();const a=(e=>e.search.fold((()=>({searchMode:"no-search"})),(e=>({searchMode:"search-with-field",placeholder:e.placeholder}))))(n),i=zS(s,r.items,t,o,n.isHorizontalMenu,a),l=(e=>e.search.fold((()=>({searchMode:"no-search"})),(e=>({searchMode:"search-with-results"}))))(n),c=ce(r.menus,((e,n)=>zS(n,e,t,o,!1,l))),d=fn(c,Ms(s,i));return A.from(Lh.tieredData(s,d,r.expansions))},O_=e=>!ve(e,"items"),__="data-value",T_=(e,t,o,n)=>H(o,(o=>O_(o)?{type:"togglemenuitem",text:o.text,value:o.value,active:o.value===n,onAction:()=>{pu.setValue(e,o.value),Ir(e,hk,{name:t}),oh.focus(e)}}:{type:"nestedmenuitem",text:o.text,getSubmenuItems:()=>T_(e,t,o.items,n)})),E_=(e,t)=>re(e,(e=>O_(e)?Ce(e.value===t,e):E_(e.items,t))),A_=fm({name:"HtmlSelect",configFields:[os("options"),hu("selectBehaviours",[oh,pu]),ys("selectClasses",[]),ys("selectAttributes",{}),us("data")],factory:(e,t)=>{const o=H(e.options,(e=>({dom:{tag:"option",value:e.value,innerHtml:e.text}}))),n=e.data.map((e=>Ms("initialValue",e))).getOr({});return{uid:e.uid,dom:{tag:"select",classes:e.selectClasses,attributes:e.selectAttributes},components:o,behaviours:bu(e.selectBehaviours,[oh.config({}),pu.config({store:{mode:"manual",getValue:e=>$a(e.element),setValue:(t,o)=>{const n=oe(e.options);G(e.options,(e=>e.value===o)).isSome()?qa(t.element,o):-1===t.element.dom.selectedIndex&&""===o&&n.each((e=>qa(t.element,e.value)))},...n}})])}}}),M_=x([ys("field1Name","field1"),ys("field2Name","field2"),Fi("onLockedChange"),Ai(["lockClass"]),ys("locked",!1),vu("coupledFieldBehaviours",[wm,pu])]),D_=(e,t)=>Uu({factory:ak,name:e,overrides:e=>({fieldBehaviours:kl([Jp("coupled-input-behaviour",[Ur(Zs(),(o=>{((e,t,o)=>om(e,t,o).bind(wm.getCurrent))(o,e,t).each((t=>{om(o,e,"lock").each((n=>{dh.isOn(n)&&e.onLockedChange(o,t,n)}))}))}))])])})}),B_=x([D_("field1","field2"),D_("field2","field1"),Uu({factory:Wh,schema:[os("dom")],name:"lock",overrides:e=>({buttonBehaviours:kl([dh.config({selected:e.locked,toggleClass:e.markers.lockClass,aria:{mode:"pressed"}})])})})]),F_=bm({name:"FormCoupledInputs",configFields:M_(),partFields:B_(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:yu(e.coupledFieldBehaviours,[wm.config({find:A.some}),pu.config({store:{mode:"manual",getValue:t=>{const o=im(t,e,["field1","field2"]);return{[e.field1Name]:pu.getValue(o.field1()),[e.field2Name]:pu.getValue(o.field2())}},setValue:(t,o)=>{const n=im(t,e,["field1","field2"]);ye(o,e.field1Name)&&pu.setValue(n.field1(),o[e.field1Name]),ye(o,e.field2Name)&&pu.setValue(n.field2(),o[e.field2Name])}}})]),apis:{getField1:t=>om(t,e,"field1"),getField2:t=>om(t,e,"field2"),getLock:t=>om(t,e,"lock")}}),apis:{getField1:(e,t)=>e.getField1(t),getField2:(e,t)=>e.getField2(t),getLock:(e,t)=>e.getLock(t)}}),I_=e=>{const t=/^\s*(\d+(?:\.\d+)?)\s*(|cm|mm|in|px|pt|pc|em|ex|ch|rem|vw|vh|vmin|vmax|%)\s*$/.exec(e);if(null!==t){const e=parseFloat(t[1]),o=t[2];return sn.value({value:e,unit:o})}return sn.error(e)},R_=(e,t)=>{const o={"":96,px:96,pt:72,cm:2.54,pc:12,mm:25.4,in:1},n=e=>ve(o,e);return e.unit===t?A.some(e.value):n(e.unit)&&n(t)?o[e.unit]===o[t]?A.some(e.value):A.some(e.value/o[e.unit]*o[t]):A.none()},N_=e=>A.none(),V_=(e,t)=>{const o=e.label.map((e=>pk(e,t))),n=[Rm.config({disabled:()=>e.disabled||t.isDisabled()}),Sx(),Pp.config({mode:"execution",useEnter:!0!==e.multiline,useControlEnter:!0===e.multiline,execute:e=>(Fr(e,yk),A.some(!0))}),Jp("textfield-change",[Ur(Zs(),((t,o)=>{Ir(t,hk,{name:e.name})})),Ur(cr(),((t,o)=>{Ir(t,hk,{name:e.name})}))]),ck.config({})],s=e.validation.map((e=>Fk.config({getRoot:e=>rt(e.element),invalidClass:"tox-invalid",validator:{validate:t=>{const o=pu.getValue(t),n=e.validator(o);return bS(!0===n?sn.value(o):sn.error(n))},validateOnLoad:e.validateOnLoad}}))).toArray(),r={...e.placeholder.fold(x({}),(e=>({placeholder:t.translate(e)}))),...e.inputMode.fold(x({}),(e=>({inputmode:e})))},a=ak.parts.field({tag:!0===e.multiline?"textarea":"input",...e.data.map((e=>({data:e}))).getOr({}),inputAttributes:r,inputClasses:[e.classname],inputBehaviours:kl(q([n,s])),selectOnFocus:!1,factory:Vv}),i=e.multiline?{dom:{tag:"div",classes:["tox-textarea-wrap"]},components:[a]}:a,l=(e.flex?["tox-form__group--stretched"]:[]).concat(e.maximized?["tox-form-group--maximize"]:[]),c=[Rm.config({disabled:()=>e.disabled||t.isDisabled(),onDisabled:e=>{ak.getField(e).each(Rm.disable)},onEnabled:e=>{ak.getField(e).each(Rm.enable)}}),Sx()];return uk(o,i,l,c)},z_=(e,t)=>t.getAnimationRoot.fold((()=>e.element),(t=>t(e))),H_=e=>e.dimension.property,L_=(e,t)=>e.dimension.getDimension(t),P_=(e,t)=>{const o=z_(e,t);ja(o,[t.shrinkingClass,t.growingClass])},U_=(e,t)=>{Pa(e.element,t.openClass),La(e.element,t.closedClass),Dt(e.element,H_(t),"0px"),Lt(e.element)},W_=(e,t)=>{Pa(e.element,t.closedClass),La(e.element,t.openClass),Ht(e.element,H_(t))},j_=(e,t,o,n)=>{o.setCollapsed(),Dt(e.element,H_(t),L_(t,e.element)),P_(e,t),U_(e,t),t.onStartShrink(e),t.onShrunk(e)},G_=(e,t,o,n)=>{const s=n.getOrThunk((()=>L_(t,e.element)));o.setCollapsed(),Dt(e.element,H_(t),s),Lt(e.element);const r=z_(e,t);Pa(r,t.growingClass),La(r,t.shrinkingClass),U_(e,t),t.onStartShrink(e)},$_=(e,t,o)=>{const n=L_(t,e.element);("0px"===n?j_:G_)(e,t,o,A.some(n))},q_=(e,t,o)=>{const n=z_(e,t),s=Ua(n,t.shrinkingClass),r=L_(t,e.element);W_(e,t);const a=L_(t,e.element);(s?()=>{Dt(e.element,H_(t),r),Lt(e.element)}:()=>{U_(e,t)})(),Pa(n,t.shrinkingClass),La(n,t.growingClass),W_(e,t),Dt(e.element,H_(t),a),o.setExpanded(),t.onStartGrow(e)},X_=(e,t,o)=>{const n=z_(e,t);return!0===Ua(n,t.growingClass)},Y_=(e,t,o)=>{const n=z_(e,t);return!0===Ua(n,t.shrinkingClass)};var K_=Object.freeze({__proto__:null,refresh:(e,t,o)=>{if(o.isExpanded()){Ht(e.element,H_(t));const o=L_(t,e.element);Dt(e.element,H_(t),o)}},grow:(e,t,o)=>{o.isExpanded()||q_(e,t,o)},shrink:(e,t,o)=>{o.isExpanded()&&$_(e,t,o)},immediateShrink:(e,t,o)=>{o.isExpanded()&&j_(e,t,o)},hasGrown:(e,t,o)=>o.isExpanded(),hasShrunk:(e,t,o)=>o.isCollapsed(),isGrowing:X_,isShrinking:Y_,isTransitioning:(e,t,o)=>X_(e,t)||Y_(e,t),toggleGrow:(e,t,o)=>{(o.isExpanded()?$_:q_)(e,t,o)},disableTransitions:P_,immediateGrow:(e,t,o)=>{o.isExpanded()||(W_(e,t),Dt(e.element,H_(t),L_(t,e.element)),P_(e,t),o.setExpanded(),t.onStartGrow(e),t.onGrown(e))}}),J_=Object.freeze({__proto__:null,exhibit:(e,t,o)=>{const n=t.expanded;return Ea(n?{classes:[t.openClass],styles:{}}:{classes:[t.closedClass],styles:Ms(t.dimension.property,"0px")})},events:(e,t)=>Hr([Yr(or(),((o,n)=>{n.event.raw.propertyName===e.dimension.property&&(P_(o,e),t.isExpanded()&&Ht(o.element,e.dimension.property),(t.isExpanded()?e.onGrown:e.onShrunk)(o))}))])}),Z_=[os("closedClass"),os("openClass"),os("shrinkingClass"),os("growingClass"),us("getAnimationRoot"),Di("onShrunk"),Di("onStartShrink"),Di("onGrown"),Di("onStartGrow"),ys("expanded",!1),ns("dimension",Jn("property",{width:[Ri("property","width"),Ri("getDimension",(e=>Jt(e)+"px"))],height:[Ri("property","height"),Ri("getDimension",(e=>Wt(e)+"px"))]}))];const Q_=Ol({fields:Z_,name:"sliding",active:J_,apis:K_,state:Object.freeze({__proto__:null,init:e=>{const t=Es(e.expanded);return _a({isExpanded:()=>!0===t.get(),isCollapsed:()=>!1===t.get(),setCollapsed:k(t.set,!1),setExpanded:k(t.set,!0),readState:()=>"expanded: "+t.get()})}})}),eT=e=>({isEnabled:()=>!Rm.isDisabled(e),setEnabled:t=>Rm.set(e,!t),setActive:t=>{const o=e.element;t?(La(o,"tox-tbtn--enabled"),kt(o,"aria-pressed",!0)):(Pa(o,"tox-tbtn--enabled"),Et(o,"aria-pressed"))},isActive:()=>Ua(e.element,"tox-tbtn--enabled"),setText:t=>{Ir(e,v_,{text:t})},setIcon:t=>Ir(e,y_,{icon:t})}),tT=(e,t,o,n,s=!0)=>x_({text:e.text,icon:e.icon,tooltip:e.tooltip,searchable:e.search.isSome(),role:n,fetch:(t,n)=>{const s={pattern:e.search.isSome()?IS(t):""};e.fetch((t=>{n(C_(t,pv.CLOSE_ON_EXECUTE,o,{isHorizontalMenu:!1,search:e.search}))}),s,eT(t))},onSetup:e.onSetup,getApi:eT,columns:1,presets:"normal",classes:[],dropdownBehaviours:[...s?[ck.config({})]:[]]},t,o.shared),oT=(e,t,o)=>{const n=e=>n=>{const s=!n.isActive();n.setActive(s),e.storage.set(s),o.shared.getSink().each((o=>{t().getOpt(o).each((t=>{Dl(t.element),Ir(t,vk,{name:e.name,value:e.storage.get()})}))}))},s=e=>t=>{t.setActive(e.storage.get())};return t=>{t(H(e,(e=>{const t=e.text.fold((()=>({})),(e=>({text:e})));return{type:e.type,active:!1,...t,onAction:n(e),onSetup:s(e)}})))}},nT=e=>({dom:{tag:"span",classes:["tox-tree__label"],attributes:{title:e,"aria-label":e}},components:[ti(e)]}),sT=la("leaf-label-event-id"),rT=({leaf:e,onLeafAction:t,visible:o,treeId:n,selectedId:s,backstage:r})=>{const a=e.menu.map((e=>tT(e,"tox-mbtn",r,A.none(),o))),i=[nT(e.title)];return a.each((e=>i.push(e))),Wh.sketch({dom:{tag:"div",classes:["tox-tree--leaf__label","tox-trbtn"].concat(o?["tox-tree--leaf__label--visible"]:[])},components:i,role:"treeitem",action:o=>{t(e.id),o.getSystem().broadcastOn([`update-active-item-${n}`],{value:e.id})},eventOrder:{[Ks()]:[sT,"keying"]},buttonBehaviours:kl([...o?[ck.config({})]:[],dh.config({toggleClass:"tox-trbtn--enabled",toggleOnExecute:!1,aria:{mode:"selected"}}),Al.config({channels:{[`update-active-item-${n}`]:{onReceive:(t,o)=>{(o.value===e.id?dh.on:dh.off)(t)}}}}),Jp(sT,[Kr(((t,o)=>{s.each((o=>{(o===e.id?dh.on:dh.off)(t)}))})),Ur(Ks(),((e,t)=>{const o="ArrowLeft"===t.event.raw.code,n="ArrowRight"===t.event.raw.code;o?(mi(e.element,".tox-tree--directory").each((t=>{e.getSystem().getByDom(t).each((e=>{gi(t,".tox-tree--directory__label").each((t=>{e.getSystem().getByDom(t).each(oh.focus)}))}))})),t.stop()):n&&t.stop()}))])])})},aT=la("directory-label-event-id"),iT=({directory:e,visible:t,noChildren:o,backstage:n})=>{const s=e.menu.map((e=>tT(e,"tox-mbtn",n,A.none()))),r=[{dom:{tag:"div",classes:["tox-chevron"]},components:[(a="chevron-right",i=n.shared.providers.icons,((e,t,o)=>tb(e,{tag:"span",classes:["tox-tree__icon-wrap","tox-icon"],behaviours:[]},t))(a,i))]},nT(e.title)];var a,i;s.each((e=>{r.push(e)}));const l=t=>{mi(t.element,".tox-tree--directory").each((o=>{t.getSystem().getByDom(o).each((o=>{const n=!dh.isOn(o);dh.toggle(o),Ir(t,"expand-tree-node",{expanded:n,node:e.id})}))}))};return Wh.sketch({dom:{tag:"div",classes:["tox-tree--directory__label","tox-trbtn"].concat(t?["tox-tree--directory__label--visible"]:[])},components:r,action:l,eventOrder:{[Ks()]:[aT,"keying"]},buttonBehaviours:kl([...t?[ck.config({})]:[],Jp(aT,[Ur(Ks(),((e,t)=>{const n="ArrowRight"===t.event.raw.code,s="ArrowLeft"===t.event.raw.code;n&&o&&t.stop(),(n||s)&&mi(e.element,".tox-tree--directory").each((o=>{e.getSystem().getByDom(o).each((o=>{!dh.isOn(o)&&n||dh.isOn(o)&&s?(l(e),t.stop()):s&&!dh.isOn(o)&&(mi(o.element,".tox-tree--directory").each((e=>{gi(e,".tox-tree--directory__label").each((e=>{o.getSystem().getByDom(e).each(oh.focus)}))})),t.stop())}))}))}))])])})},lT=({children:e,onLeafAction:t,visible:o,treeId:n,expandedIds:s,selectedId:r,backstage:a})=>({dom:{tag:"div",classes:["tox-tree--directory__children"]},components:e.map((e=>"leaf"===e.type?rT({leaf:e,selectedId:r,onLeafAction:t,visible:o,treeId:n,backstage:a}):dT({directory:e,expandedIds:s,selectedId:r,onLeafAction:t,labelTabstopping:o,treeId:n,backstage:a}))),behaviours:kl([Q_.config({dimension:{property:"height"},closedClass:"tox-tree--directory__children--closed",openClass:"tox-tree--directory__children--open",growingClass:"tox-tree--directory__children--growing",shrinkingClass:"tox-tree--directory__children--shrinking",expanded:o}),Kp.config({})])}),cT=la("directory-event-id"),dT=({directory:e,onLeafAction:t,labelTabstopping:o,treeId:n,backstage:s,expandedIds:r,selectedId:a})=>{const{children:i}=e,l=Es(r),c=r.includes(e.id);return{dom:{tag:"div",classes:["tox-tree--directory"],attributes:{role:"treeitem"}},components:[iT({directory:e,visible:o,noChildren:0===e.children.length,backstage:s}),lT({children:i,expandedIds:r,selectedId:a,onLeafAction:t,visible:c,treeId:n,backstage:s})],behaviours:kl([Jp(cT,[Kr(((e,t)=>{dh.set(e,c)})),Ur("expand-tree-node",((e,t)=>{const{expanded:o,node:n}=t.event;l.set(o?[...l.get(),n]:l.get().filter((e=>e!==n)))}))]),dh.config({...e.children.length>0?{aria:{mode:"expanded"}}:{},toggleClass:"tox-tree--directory--expanded",onToggled:(e,o)=>{const r=e.components()[1],c=(d=o,i.map((e=>"leaf"===e.type?rT({leaf:e,selectedId:a,onLeafAction:t,visible:d,treeId:n,backstage:s}):dT({directory:e,expandedIds:l.get(),selectedId:a,onLeafAction:t,labelTabstopping:d,treeId:n,backstage:s}))));var d;o?Q_.grow(r):Q_.shrink(r),Kp.set(r,c)}})])}},uT=la("tree-event-id");var mT=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.stream.streams.setup(e,t);return Hr([Ur(e.event,o),Jr((()=>t.cancel()))].concat(e.cancelEvent.map((e=>[Ur(e,(()=>t.cancel()))])).getOr([])))}});const gT=e=>{const t=Es(null);return _a({readState:()=>({timer:null!==t.get()?"set":"unset"}),setTimer:e=>{t.set(e)},cancel:()=>{const e=t.get();null!==e&&e.cancel()}})};var pT=Object.freeze({__proto__:null,throttle:gT,init:e=>e.stream.streams.state(e)}),hT=[ns("stream",Jn("mode",{throttle:[os("delay"),ys("stopEvent",!0),Ri("streams",{setup:(e,t)=>{const o=e.stream,n=PO(e.onStream,o.delay);return t.setTimer(n),(e,t)=>{n.throttle(e,t),o.stopEvent&&t.stop()}},state:gT})]})),ys("event","input"),us("cancelEvent"),Fi("onStream")];const fT=Ol({fields:hT,name:"streaming",active:mT,state:pT}),bT=(e,t,o)=>{const n=pu.getValue(o);pu.setValue(t,n),yT(t)},vT=(e,t)=>{const o=e.element,n=$a(o),s=o.dom;"number"!==Ot(o,"type")&&t(s,n)},yT=e=>{vT(e,((e,t)=>e.setSelectionRange(t.length,t.length)))},xT=x("alloy.typeahead.itemexecute"),wT=x([us("lazySink"),os("fetch"),ys("minChars",5),ys("responseTime",1e3),Di("onOpen"),ys("getHotspot",A.some),ys("getAnchorOverrides",x({})),ys("layouts",A.none()),ys("eventOrder",{}),Ts("model",{},[ys("getDisplayText",(e=>void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.value)),ys("selectsOver",!0),ys("populateFromBrowse",!0)]),Di("onSetValue"),Bi("onExecute"),Di("onItemExecute"),ys("inputClasses",[]),ys("inputAttributes",{}),ys("inputStyles",{}),ys("matchWidth",!0),ys("useMinWidth",!1),ys("dismissOnBlur",!0),Ai(["openClass"]),us("initialData"),hu("typeaheadBehaviours",[oh,pu,fT,Pp,dh,uS]),es("lazyTypeaheadComp",(()=>Es(A.none))),es("previewing",(()=>Es(!0)))].concat(Fv()).concat(ES())),ST=x([Wu({schema:[Ei()],name:"menu",overrides:e=>({fakeFocus:!0,onHighlightItem:(t,o,n)=>{e.previewing.get()?e.lazyTypeaheadComp.get().each((t=>{((e,t,o)=>{if(e.selectsOver){const n=pu.getValue(t),s=e.getDisplayText(n),r=pu.getValue(o);return 0===e.getDisplayText(r).indexOf(s)?A.some((()=>{bT(0,t,o),((e,t)=>{vT(e,((e,o)=>e.setSelectionRange(t,o.length)))})(t,s.length)})):A.none()}return A.none()})(e.model,t,n).fold((()=>{e.model.selectsOver?(Gm.dehighlight(o,n),e.previewing.set(!0)):e.previewing.set(!1)}),(t=>{t(),e.previewing.set(!1)}))})):e.lazyTypeaheadComp.get().each((t=>{e.model.populateFromBrowse&&bT(e.model,t,n),_t(n.element,"id").each((e=>kt(t.element,"aria-activedescendant",e)))}))},onExecute:(t,o)=>e.lazyTypeaheadComp.get().map((e=>(Ir(e,xT(),{item:o}),!0))),onHover:(t,o)=>{e.previewing.set(!1),e.lazyTypeaheadComp.get().each((t=>{e.model.populateFromBrowse&&bT(e.model,t,o)}))}})})]),kT=bm({name:"Typeahead",configFields:wT(),partFields:ST(),factory:(e,t,o,n)=>{const s=(t,o,s)=>{e.previewing.set(!1);const r=uS.getCoupled(t,"sandbox");if(Xd.isOpen(r))wm.getCurrent(r).each((e=>{Gm.getHighlighted(e).fold((()=>{s(e)}),(()=>{zr(r,e.element,"keydown",o)}))}));else{const o=e=>{wm.getCurrent(e).each(s)};wS(e,a(t),t,r,n,o,zh.HighlightMenuAndItem).get(b)}},r=Iv(e),a=e=>t=>t.map((t=>{const o=fe(t.menus),n=X(o,(e=>U(e.items,(e=>"item"===e.type))));return pu.getState(e).update(H(n,(e=>e.data))),t})),i=e=>wm.getCurrent(e),l="typeaheadevents",c=[oh.config({}),pu.config({onSetValue:e.onSetValue,store:{mode:"dataset",getDataKey:e=>$a(e.element),getFallbackEntry:e=>({value:e,meta:{}}),setValue:(t,o)=>{qa(t.element,e.model.getDisplayText(o))},...e.initialData.map((e=>Ms("initialValue",e))).getOr({})}}),fT.config({stream:{mode:"throttle",delay:e.responseTime,stopEvent:!1},onStream:(t,o)=>{const s=uS.getCoupled(t,"sandbox");if(oh.isFocused(t)&&$a(t.element).length>=e.minChars){const o=i(s).bind((e=>Gm.getHighlighted(e).map(pu.getValue)));e.previewing.set(!0);const r=t=>{i(s).each((t=>{o.fold((()=>{e.model.selectsOver&&Gm.highlightFirst(t)}),(e=>{Gm.highlightBy(t,(t=>pu.getValue(t).value===e.value)),Gm.getHighlighted(t).orThunk((()=>(Gm.highlightFirst(t),A.none())))}))}))};wS(e,a(t),t,s,n,r,zh.HighlightJustMenu).get(b)}},cancelEvent:fr()}),Pp.config({mode:"special",onDown:(e,t)=>(s(e,t,Gm.highlightFirst),A.some(!0)),onEscape:e=>{const t=uS.getCoupled(e,"sandbox");return Xd.isOpen(t)?(Xd.close(t),A.some(!0)):A.none()},onUp:(e,t)=>(s(e,t,Gm.highlightLast),A.some(!0)),onEnter:t=>{const o=uS.getCoupled(t,"sandbox"),n=Xd.isOpen(o);if(n&&!e.previewing.get())return i(o).bind((e=>Gm.getHighlighted(e))).map((e=>(Ir(t,xT(),{item:e}),!0)));{const s=pu.getValue(t);return Fr(t,fr()),e.onExecute(o,t,s),n&&Xd.close(o),A.some(!0)}}}),dh.config({toggleClass:e.markers.openClass,aria:{mode:"expanded"}}),uS.config({others:{sandbox:t=>_S(e,t,{onOpen:()=>dh.on(t),onClose:()=>{e.lazyTypeaheadComp.get().each((e=>Et(e.element,"aria-activedescendant"))),dh.off(t)}})}}),Jp(l,[Kr((t=>{e.lazyTypeaheadComp.set(A.some(t))})),Jr((t=>{e.lazyTypeaheadComp.set(A.none())})),Qr((t=>{const o=b;kS(e,a(t),t,n,o,zh.HighlightMenuAndItem).get(b)})),Ur(xT(),((t,o)=>{const n=uS.getCoupled(t,"sandbox");bT(e.model,t,o.event.item),Fr(t,fr()),e.onItemExecute(t,n,o.event.item,pu.getValue(t)),Xd.close(n),yT(t)}))].concat(e.dismissOnBlur?[Ur(lr(),(e=>{const t=uS.getCoupled(e,"sandbox");Rl(t.element).isNone()&&Xd.close(t)}))]:[]))],d={[kr()]:[pu.name(),fT.name(),l],...e.eventOrder};return{uid:e.uid,dom:Nv(fn(e,{inputAttributes:{role:"combobox","aria-autocomplete":"list","aria-haspopup":"true"}})),behaviours:{...r,...bu(e.typeaheadBehaviours,c)},eventOrder:d}}}),CT=e=>({...e,toCached:()=>CT(e.toCached()),bindFuture:t=>CT(e.bind((e=>e.fold((e=>bS(sn.error(e))),(e=>t(e)))))),bindResult:t=>CT(e.map((e=>e.bind(t)))),mapResult:t=>CT(e.map((e=>e.map(t)))),mapError:t=>CT(e.map((e=>e.mapError(t)))),foldResult:(t,o)=>e.map((e=>e.fold(t,o))),withTimeout:(t,o)=>CT(fS((n=>{let s=!1;const r=setTimeout((()=>{s=!0,n(sn.error(o()))}),t);e.get((e=>{s||(clearTimeout(r),n(e))}))})))}),OT=e=>CT(fS(e)),_T=(e,t,o=[],n,s,r)=>{const a=t.fold((()=>({})),(e=>({action:e}))),i={buttonBehaviours:kl([kx((()=>!e.enabled||r.isDisabled())),Sx(),ck.config({}),Jp("button press",[Pr("click"),Pr("mousedown")])].concat(o)),eventOrder:{click:["button press","alloy.base.behaviour"],mousedown:["button press","alloy.base.behaviour"]},...a},l=fn(i,{dom:n});return fn(l,{components:s})},TT=(e,t,o,n=[])=>{const s={tag:"button",classes:["tox-tbtn"],attributes:e.tooltip.map((e=>({"aria-label":o.translate(e),title:o.translate(e)}))).getOr({})},r=e.icon.map((e=>h_(e,o.icons))),a=Dx([r]);return _T(e,t,n,s,a,o)},ET=e=>{switch(e){case"primary":return["tox-button"];case"toolbar":return["tox-tbtn"];default:return["tox-button","tox-button--secondary"]}},AT=(e,t,o,n=[],s=[])=>{const r=o.translate(e.text),a=e.icon.map((e=>h_(e,o.icons))),i=[a.getOrThunk((()=>ti(r)))],l=e.buttonType.getOr(e.primary||e.borderless?"primary":"secondary"),c=[...ET(l),...a.isSome()?["tox-button--icon"]:[],...e.borderless?["tox-button--naked"]:[],...s];return _T(e,t,n,{tag:"button",classes:c,attributes:{title:r}},i,o)},MT=(e,t,o,n=[],s=[])=>{const r=AT(e,A.some(t),o,n,s);return Wh.sketch(r)},DT=(e,t)=>o=>{"custom"===t?Ir(o,vk,{name:e,value:{}}):"submit"===t?Fr(o,yk):"cancel"===t?Fr(o,bk):console.error("Unknown button type: ",t)},BT=(e,t,o)=>{if(((e,t)=>"menu"===t)(0,t)){const t=()=>r,n=e,s={...e,type:"menubutton",search:A.none(),onSetup:t=>(t.setEnabled(e.enabled),b),fetch:oT(n.items,t,o)},r=Gh(tT(s,"tox-tbtn",o,A.none()));return r.asSpec()}if(((e,t)=>"custom"===t||"cancel"===t||"submit"===t)(0,t)){const n=DT(e.name,t),s={...e,borderless:!1};return MT(s,n,o.shared.providers,[])}if(((e,t)=>"togglebutton"===t)(0,t))return((e,t)=>{var o,n;const s=e.icon.map((e=>f_(e,t.icons))).map(Gh),r=e.buttonType.getOr(e.primary?"primary":"secondary"),a={...e,name:null!==(o=e.name)&&void 0!==o?o:"",primary:"primary"===r,tooltip:A.from(e.tooltip),enabled:null!==(n=e.enabled)&&void 0!==n&&n,borderless:!1},i=a.tooltip.map((e=>({"aria-label":t.translate(e),title:t.translate(e)}))).getOr({}),l=ET(null!=r?r:"secondary"),c=e.icon.isSome()&&e.text.isSome(),d={tag:"button",classes:[...l.concat(e.icon.isSome()?["tox-button--icon"]:[]),...e.active?["tox-button--enabled"]:[],...c?["tox-button--icon-and-text"]:[]],attributes:i},u=t.translate(e.text.getOr("")),m=ti(u),g=[...Dx([s.map((e=>e.asSpec()))]),...e.text.isSome()?[m]:[]],p=_T(a,A.some((o=>{Ir(o,vk,{name:e.name,value:{setIcon:e=>{s.map((n=>n.getOpt(o).each((o=>{Kp.set(o,[f_(e,t.icons)])}))))}}})})),[],d,g,t);return Wh.sketch(p)})(e,o.shared.providers);throw console.error("Unknown footer button type: ",t),new Error("Unknown footer button type")},FT={type:"separator"},IT=e=>({type:"menuitem",value:e.url,text:e.title,meta:{attach:e.attach},onAction:b}),RT=(e,t)=>({type:"menuitem",value:t,text:e,meta:{attach:void 0},onAction:b}),NT=(e,t)=>(e=>H(e,IT))(((e,t)=>U(t,(t=>t.type===e)))(e,t)),VT=e=>NT("header",e.targets),zT=e=>NT("anchor",e.targets),HT=e=>A.from(e.anchorTop).map((e=>RT("<top>",e))).toArray(),LT=e=>A.from(e.anchorBottom).map((e=>RT("<bottom>",e))).toArray(),PT=(e,t)=>{const o=e.toLowerCase();return U(t,(e=>{var t;const n=void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.text,s=null!==(t=e.value)&&void 0!==t?t:"";return Te(n.toLowerCase(),o)||Te(s.toLowerCase(),o)}))},UT=la("aria-invalid"),WT=(e,t)=>{e.dom.checked=t},jT=e=>e.dom.checked,GT=e=>(t,o,n,s)=>be(o,"name").fold((()=>e(o,s,A.none())),(r=>t.field(r,e(o,s,be(n,r))))),$T={bar:GT(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-bar","tox-form__controls-h-stack"]},components:H(e.items,t.interpreter)}))(e,t.shared))),collection:GT(((e,t,o)=>((e,t,o)=>{const n=e.label.map((e=>pk(e,t))),s=e=>(t,o)=>{hi(o.event.target,"[data-collection-item-value]").each((n=>{e(t,o,n,Ot(n,"data-collection-item-value"))}))},r=s(((o,n,s,r)=>{n.stop(),t.isDisabled()||Ir(o,vk,{name:e.name,value:r})})),a=[Ur(qs(),s(((e,t,o)=>{Dl(o)}))),Ur(er(),r),Ur(gr(),r),Ur(Xs(),s(((e,t,o)=>{pi(e.element,"."+kv).each((e=>{Pa(e,kv)})),La(o,kv)}))),Ur(Ys(),s((e=>{pi(e.element,"."+kv).each((e=>{Pa(e,kv)}))}))),Qr(s(((t,o,n,s)=>{Ir(t,vk,{name:e.name,value:s})})))],i=(e,t)=>H(Xc(e.element,".tox-collection__item"),t),l=ak.parts.field({dom:{tag:"div",classes:["tox-collection"].concat(1!==e.columns?["tox-collection--grid"]:["tox-collection--list"])},components:[],factory:{sketch:w},behaviours:kl([Rm.config({disabled:t.isDisabled,onDisabled:e=>{i(e,(e=>{La(e,"tox-collection__item--state-disabled"),kt(e,"aria-disabled",!0)}))},onEnabled:e=>{i(e,(e=>{Pa(e,"tox-collection__item--state-disabled"),Et(e,"aria-disabled")}))}}),Sx(),Kp.config({}),pu.config({store:{mode:"memory",initialValue:o.getOr([])},onSetValue:(o,n)=>{((o,n)=>{const s=H(n,(o=>{const n=$f.translate(o.text),s=1===e.columns?`<div class="tox-collection__item-label">${n}</div>`:"",r=`<div class="tox-collection__item-icon">${o.icon}</div>`,a={_:" "," - ":" ","-":" "},i=n.replace(/\_| \- |\-/g,(e=>a[e]));return`<div class="tox-collection__item${t.isDisabled()?" tox-collection__item--state-disabled":""}" tabindex="-1" data-collection-item-value="${dk.encodeAllRaw(o.value)}" title="${i}" aria-label="${i}">${r}${s}</div>`})),r="auto"!==e.columns&&e.columns>1?z(s,e.columns):[s],a=H(r,(e=>`<div class="tox-collection__group">${e.join("")}</div>`));ta(o.element,a.join(""))})(o,n),"auto"===e.columns&&ix(o,5,"tox-collection__item").each((({numRows:e,numColumns:t})=>{Pp.setGridSize(o,e,t)})),Fr(o,kk)}}),ck.config({}),Pp.config((c=e.columns,1===c?{mode:"menu",moveOnTab:!1,selector:".tox-collection__item"}:"auto"===c?{mode:"flatgrid",selector:".tox-collection__item",initSize:{numColumns:1,numRows:1}}:{mode:"matrix",selectors:{row:".tox-collection__group",cell:`.${fv}`}})),Jp("collection-events",a)]),eventOrder:{[ur()]:["disabling","alloy.base.behaviour","collection-events"]}});var c;return uk(n,l,["tox-form__group--collection"],[])})(e,t.shared.providers,o))),alertbanner:GT(((e,t)=>((e,t)=>{const o=Zf(e.icon,t.icons);return ok.sketch({dom:{tag:"div",attributes:{role:"alert"},classes:["tox-notification","tox-notification--in",`tox-notification--${e.level}`]},components:[{dom:{tag:"div",classes:["tox-notification__icon"],innerHtml:e.url?void 0:o},components:e.url?[Wh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--naked","tox-button--icon"],innerHtml:o,attributes:{title:t.translate(e.iconTooltip)}},action:t=>Ir(t,vk,{name:"alert-banner",value:e.url}),buttonBehaviours:kl([Qf()])})]:void 0},{dom:{tag:"div",classes:["tox-notification__body"],innerHtml:t.translate(e.text)}}]})})(e,t.shared.providers))),input:GT(((e,t,o)=>((e,t,o)=>V_({name:e.name,multiline:!1,label:e.label,inputMode:e.inputMode,placeholder:e.placeholder,flex:!1,disabled:!e.enabled,classname:"tox-textfield",validation:A.none(),maximized:e.maximized,data:o},t))(e,t.shared.providers,o))),textarea:GT(((e,t,o)=>((e,t,o)=>V_({name:e.name,multiline:!0,label:e.label,inputMode:A.none(),placeholder:e.placeholder,flex:!0,disabled:!e.enabled,classname:"tox-textarea",validation:A.none(),maximized:e.maximized,data:o},t))(e,t.shared.providers,o))),label:GT(((e,t)=>((e,t)=>{const o="tox-label";return{dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"label",classes:[o,..."center"===e.align?[`${o}--center`]:[],..."end"===e.align?[`${o}--end`]:[]]},components:[ti(t.providers.translate(e.label))]},...H(e.items,t.interpreter)],behaviours:kl([DO(),Kp.config({}),(n=A.none(),NO(n,ea,ta)),Pp.config({mode:"acyclic"})])};var n})(e,t.shared))),iframe:(vA=(e,t,o)=>((e,t,o)=>{const n="tox-dialog__iframe",s=e.transparent?[]:[`${n}--opaque`],r=e.border?["tox-navobj-bordered"]:[],a={...e.label.map((e=>({title:e}))).getOr({}),...o.map((e=>({srcdoc:e}))).getOr({}),...e.sandboxed?{sandbox:"allow-scripts allow-same-origin"}:{}},i=((e,t)=>{const o=Es(e.getOr(""));return{getValue:e=>o.get(),setValue:(e,n)=>{if(o.get()!==n){const o=e.element,s=()=>kt(o,"srcdoc",n);t?c_.fold(x(l_),(e=>e.throttle))(o,n,s):s()}o.set(n)}}})(o,e.streamContent),l=e.label.map((e=>pk(e,t))),c=ak.parts.field({factory:{sketch:e=>GO(A.from(r),{uid:e.uid,dom:{tag:"iframe",attributes:a,classes:[n,...s]},behaviours:kl([ck.config({}),oh.config({}),RO(o,i.getValue,i.setValue),Al.config({channels:{[e_]:{onReceive:(e,t)=>{t.newFocus.each((t=>{rt(e.element).each((o=>{(Ze(e.element,t)?La:Pa)(o,"tox-navobj-bordered-focus")}))}))}}}})])})}});return uk(l,c,["tox-form__group--stretched"],[])})(e,t.shared.providers,o),(e,t,o,n)=>{const s=fn(t,{source:"dynamic"});return GT(vA)(e,s,o,n)}),button:GT(((e,t)=>((e,t)=>{const o=DT(e.name,"custom");return n=A.none(),s=ak.parts.field({factory:Wh,...AT(e,A.some(o),t,[VO(""),DO()])}),uk(n,s,[],[]);var n,s})(e,t.shared.providers))),checkbox:GT(((e,t,o)=>((e,t,o)=>{const n=e=>(e.element.dom.click(),A.some(!0)),s=ak.parts.field({factory:{sketch:w},dom:{tag:"input",classes:["tox-checkbox__input"],attributes:{type:"checkbox"}},behaviours:kl([DO(),Rm.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{rt(e.element).each((e=>La(e,"tox-checkbox--disabled")))},onEnabled:e=>{rt(e.element).each((e=>Pa(e,"tox-checkbox--disabled")))}}),ck.config({}),oh.config({}),NO(o,jT,WT),Pp.config({mode:"special",onEnter:n,onSpace:n,stopSpaceKeyup:!0}),Jp("checkbox-events",[Ur(Qs(),((t,o)=>{Ir(t,hk,{name:e.name})}))])])}),r=ak.parts.label({dom:{tag:"span",classes:["tox-checkbox__label"]},components:[ti(t.translate(e.label))],behaviours:kl([Ik.config({})])}),a=e=>tb("checked"===e?"selected":"unselected",{tag:"span",classes:["tox-icon","tox-checkbox-icon__"+e]},t.icons),i=Gh({dom:{tag:"div",classes:["tox-checkbox__icons"]},components:[a("checked"),a("unchecked")]});return ak.sketch({dom:{tag:"label",classes:["tox-checkbox"]},components:[s,i.asSpec(),r],fieldBehaviours:kl([Rm.config({disabled:()=>!e.enabled||t.isDisabled()}),Sx()])})})(e,t.shared.providers,o))),colorinput:GT(((e,t,o)=>((e,t,o,n)=>{const s=ak.parts.field({factory:Vv,inputClasses:["tox-textfield"],data:n,onSetValue:e=>Fk.run(e).get(b),inputBehaviours:kl([Rm.config({disabled:t.providers.isDisabled}),Sx(),ck.config({}),Fk.config({invalidClass:"tox-textbox-field-invalid",getRoot:e=>rt(e.element),notify:{onValid:e=>{const t=pu.getValue(e);Ir(e,Rk,{color:t})}},validator:{validateOnLoad:!1,validate:e=>{const t=pu.getValue(e);if(0===t.length)return bS(sn.value(!0));{const e=Re("span");Dt(e,"background-color",t);const o=Nt(e,"background-color").fold((()=>sn.error("blah")),(e=>sn.value(t)));return bS(o)}}}})]),selectOnFocus:!1}),r=e.label.map((e=>pk(e,t.providers))),a=(e,t)=>{Ir(e,Nk,{value:t})},i=Gh(((e,t)=>DS.sketch({dom:e.dom,components:e.components,toggleClass:"mce-active",dropdownBehaviours:kl([kx(t.providers.isDisabled),Sx(),Ik.config({}),ck.config({})]),layouts:e.layouts,sandboxClasses:["tox-dialog__popups"],lazySink:t.getSink,fetch:o=>fS((t=>e.fetch(t))).map((n=>A.from(HS(fn(Zw(la("menu-value"),n,(t=>{e.onItemAction(o,t)}),e.columns,e.presets,pv.CLOSE_ON_EXECUTE,T,t.providers),{movement:eS(e.columns,e.presets)}))))),parts:{menu:Bv(0,0,e.presets)}}))({dom:{tag:"span",attributes:{"aria-label":t.providers.translate("Color swatch")}},layouts:{onRtl:()=>[rl,sl,cl],onLtr:()=>[sl,rl,cl]},components:[],fetch:$w(o.getColors(e.storageKey),e.storageKey,o.hasCustomColors()),columns:o.getColorCols(e.storageKey),presets:"color",onItemAction:(t,n)=>{i.getOpt(t).each((t=>{"custom"===n?o.colorPicker((o=>{o.fold((()=>Fr(t,Vk)),(o=>{a(t,o),Ow(e.storageKey,o)}))}),"#ffffff"):a(t,"remove"===n?"":n)}))}},t));return ak.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:r.toArray().concat([{dom:{tag:"div",classes:["tox-color-input"]},components:[s,i.asSpec()]}]),fieldBehaviours:kl([Jp("form-field-events",[Ur(Rk,((t,o)=>{i.getOpt(t).each((e=>{Dt(e.element,"background-color",o.event.color)})),Ir(t,hk,{name:e.name})})),Ur(Nk,((e,t)=>{ak.getField(e).each((o=>{pu.setValue(o,t.event.value),wm.getCurrent(e).each(oh.focus)}))})),Ur(Vk,((e,t)=>{ak.getField(e).each((t=>{wm.getCurrent(e).each(oh.focus)}))}))])])})})(e,t.shared,t.colorinput,o))),colorpicker:GT(((e,t,o)=>((e,t,o)=>{const n=e=>"tox-"+e,s=MO((e=>t=>e.translate(zO[t]))(t),n),r=Gh(s.sketch({dom:{tag:"div",classes:[n("color-picker-container")],attributes:{role:"presentation"}},onValidHex:e=>{Ir(e,vk,{name:"hex-valid",value:!0})},onInvalidHex:e=>{Ir(e,vk,{name:"hex-valid",value:!1})}}));return{dom:{tag:"div"},components:[r.asSpec()],behaviours:kl([RO(o,(e=>{const t=r.get(e);return wm.getCurrent(t).bind((e=>pu.getValue(e).hex)).map((e=>"#"+_e(e,"#"))).getOr("")}),((e,t)=>{const o=A.from(/^#([a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)/.exec(t)).bind((e=>te(e,1))),n=r.get(e);wm.getCurrent(n).fold((()=>{console.log("Can not find form")}),(e=>{pu.setValue(e,{hex:o.getOr("")}),CO.getField(e,"hex").each((e=>{Fr(e,Zs())}))}))})),DO()])}})(0,t.shared.providers,o))),dropzone:GT(((e,t,o)=>((e,t,o)=>{const n=(e,t)=>{t.stop()},s=e=>(t,o)=>{L(e,(e=>{e(t,o)}))},r=(e,t)=>{var o;if(!Rm.isDisabled(e)){const n=t.event.raw;i(e,null===(o=n.dataTransfer)||void 0===o?void 0:o.files)}},a=(e,t)=>{const o=t.event.raw.target;i(e,o.files)},i=(o,n)=>{n&&(pu.setValue(o,((e,t)=>{const o=LO.explode(t.getOption("images_file_types"));return U(se(e),(e=>N(o,(t=>Ae(e.name.toLowerCase(),`.${t.toLowerCase()}`)))))})(n,t)),Ir(o,hk,{name:e.name}))},l=Gh({dom:{tag:"input",attributes:{type:"file",accept:"image/*"},styles:{display:"none"}},behaviours:kl([Jp("input-file-events",[qr(er()),qr(gr())])])}),c=e.label.map((e=>pk(e,t))),d=ak.parts.field({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-dropzone-container"]},behaviours:kl([VO(o.getOr([])),DO(),Rm.config({}),dh.config({toggleClass:"dragenter",toggleOnExecute:!1}),Jp("dropzone-events",[Ur("dragenter",s([n,dh.toggle])),Ur("dragleave",s([n,dh.toggle])),Ur("dragover",n),Ur("drop",s([n,r])),Ur(Qs(),a)])]),components:[{dom:{tag:"div",classes:["tox-dropzone"],styles:{}},components:[{dom:{tag:"p"},components:[ti(t.translate("Drop an image here"))]},Wh.sketch({dom:{tag:"button",styles:{position:"relative"},classes:["tox-button","tox-button--secondary"]},components:[ti(t.translate("Browse for an image")),l.asSpec()],action:e=>{l.get(e).element.dom.click()},buttonBehaviours:kl([ck.config({}),kx(t.isDisabled),Sx()])})]}]})}});return uk(c,d,["tox-form__group--stretched"],[])})(e,t.shared.providers,o))),grid:GT(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-form__grid",`tox-form__grid--${e.columns}col`]},components:H(e.items,t.interpreter)}))(e,t.shared))),listbox:GT(((e,t,o)=>((e,t,o)=>{const n=t.shared.providers,s=o.bind((t=>E_(e.items,t))).orThunk((()=>oe(e.items).filter(O_))),r=e.label.map((e=>pk(e,n))),a=ak.parts.field({dom:{},factory:{sketch:o=>x_({uid:o.uid,text:s.map((e=>e.text)),icon:A.none(),tooltip:e.label,role:A.none(),fetch:(o,n)=>{const s=T_(o,e.name,e.items,pu.getValue(o));n(C_(s,pv.CLOSE_ON_EXECUTE,t,{isHorizontalMenu:!1,search:A.none()}))},onSetup:x(b),getApi:x({}),columns:1,presets:"normal",classes:[],dropdownBehaviours:[ck.config({}),RO(s.map((e=>e.value)),(e=>Ot(e.element,__)),((t,o)=>{E_(e.items,o).each((e=>{kt(t.element,__,e.value),Ir(t,v_,{text:e.text})}))}))]},"tox-listbox",t.shared)}}),i={dom:{tag:"div",classes:["tox-listboxfield"]},components:[a]};return ak.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:q([r.toArray(),[i]]),fieldBehaviours:kl([Rm.config({disabled:x(!e.enabled),onDisabled:e=>{ak.getField(e).each(Rm.disable)},onEnabled:e=>{ak.getField(e).each(Rm.enable)}})])})})(e,t,o))),selectbox:GT(((e,t,o)=>((e,t,o)=>{const n=H(e.items,(e=>({text:t.translate(e.text),value:e.value}))),s=e.label.map((e=>pk(e,t))),r=ak.parts.field({dom:{},...o.map((e=>({data:e}))).getOr({}),selectAttributes:{size:e.size},options:n,factory:A_,selectBehaviours:kl([Rm.config({disabled:()=>!e.enabled||t.isDisabled()}),ck.config({}),Jp("selectbox-change",[Ur(Qs(),((t,o)=>{Ir(t,hk,{name:e.name})}))])])}),a=e.size>1?A.none():A.some(tb("chevron-down",{tag:"div",classes:["tox-selectfield__icon-js"]},t.icons)),i={dom:{tag:"div",classes:["tox-selectfield"]},components:q([[r],a.toArray()])};return ak.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:q([s.toArray(),[i]]),fieldBehaviours:kl([Rm.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{ak.getField(e).each(Rm.disable)},onEnabled:e=>{ak.getField(e).each(Rm.enable)}}),Sx()])})})(e,t.shared.providers,o))),sizeinput:GT(((e,t)=>((e,t)=>{let o=N_;const n=la("ratio-event"),s=e=>tb(e,{tag:"span",classes:["tox-icon","tox-lock-icon__"+e]},t.icons),r=F_.parts.lock({dom:{tag:"button",classes:["tox-lock","tox-button","tox-button--naked","tox-button--icon"],attributes:{title:t.translate(e.label.getOr("Constrain proportions"))}},components:[s("lock"),s("unlock")],buttonBehaviours:kl([Rm.config({disabled:()=>!e.enabled||t.isDisabled()}),Sx(),ck.config({})])}),a=e=>({dom:{tag:"div",classes:["tox-form__group"]},components:e}),i=o=>ak.parts.field({factory:Vv,inputClasses:["tox-textfield"],inputBehaviours:kl([Rm.config({disabled:()=>!e.enabled||t.isDisabled()}),Sx(),ck.config({}),Jp("size-input-events",[Ur(Xs(),((e,t)=>{Ir(e,n,{isField1:o})})),Ur(Qs(),((t,o)=>{Ir(t,hk,{name:e.name})}))])]),selectOnFocus:!1}),l=e=>({dom:{tag:"label",classes:["tox-label"]},components:[ti(t.translate(e))]}),c=F_.parts.field1(a([ak.parts.label(l("Width")),i(!0)])),d=F_.parts.field2(a([ak.parts.label(l("Height")),i(!1)]));return F_.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:[c,d,a([l("\xa0"),r])]}],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:(e,t,n)=>{I_(pu.getValue(e)).each((e=>{o(e).each((e=>{pu.setValue(t,(e=>{const t={"":0,px:0,pt:1,mm:1,pc:2,ex:2,em:2,ch:2,rem:2,cm:3,in:4,"%":4};let o=e.value.toFixed((n=e.unit)in t?t[n]:1);var n;return-1!==o.indexOf(".")&&(o=o.replace(/\.?0*$/,"")),o+e.unit})(e))}))}))},coupledFieldBehaviours:kl([Rm.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{F_.getField1(e).bind(ak.getField).each(Rm.disable),F_.getField2(e).bind(ak.getField).each(Rm.disable),F_.getLock(e).each(Rm.disable)},onEnabled:e=>{F_.getField1(e).bind(ak.getField).each(Rm.enable),F_.getField2(e).bind(ak.getField).each(Rm.enable),F_.getLock(e).each(Rm.enable)}}),Sx(),Jp("size-input-events2",[Ur(n,((e,t)=>{const n=t.event.isField1,s=n?F_.getField1(e):F_.getField2(e),r=n?F_.getField2(e):F_.getField1(e),a=s.map(pu.getValue).getOr(""),i=r.map(pu.getValue).getOr("");o=((e,t)=>{const o=I_(e).toOptional(),n=I_(t).toOptional();return Se(o,n,((e,t)=>R_(e,t.unit).map((e=>t.value/e)).map((e=>{return o=e,n=t.unit,e=>R_(e,n).map((e=>({value:e*o,unit:n})));var o,n})).getOr(N_))).getOr(N_)})(a,i)}))])])})})(e,t.shared.providers))),slider:GT(((e,t,o)=>((e,t,o)=>{const n=fO.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[ti(t.translate(e.label))]}),s=fO.parts.spectrum({dom:{tag:"div",classes:["tox-slider__rail"],attributes:{role:"presentation"}}}),r=fO.parts.thumb({dom:{tag:"div",classes:["tox-slider__handle"],attributes:{role:"presentation"}}});return fO.sketch({dom:{tag:"div",classes:["tox-slider"],attributes:{role:"presentation"}},model:{mode:"x",minX:e.min,maxX:e.max,getInitialValue:x(o.getOrThunk((()=>(Math.abs(e.max)-Math.abs(e.min))/2)))},components:[n,s,r],sliderBehaviours:kl([DO(),oh.config({})]),onChoose:(t,o,n)=>{Ir(t,hk,{name:e.name,value:n})}})})(e,t.shared.providers,o))),urlinput:GT(((e,t,o)=>((e,t,o,n)=>{const s=t.shared.providers,r=t=>{const n=pu.getValue(t);o.addToHistory(n.value,e.filetype)},a={...n.map((e=>({initialData:e}))).getOr({}),dismissOnBlur:!0,inputClasses:["tox-textfield"],sandboxClasses:["tox-dialog__popups"],inputAttributes:{"aria-errormessage":UT,type:"url"},minChars:0,responseTime:0,fetch:n=>{const s=((e,t,o)=>{var n,s;const r=pu.getValue(t),a=null!==(s=null===(n=null==r?void 0:r.meta)||void 0===n?void 0:n.text)&&void 0!==s?s:r.value;return o.getLinkInformation().fold((()=>[]),(t=>{const n=PT(a,(e=>H(e,(e=>RT(e,e))))(o.getHistory(e)));return"file"===e?(s=[n,PT(a,VT(t)),PT(a,q([HT(t),zT(t),LT(t)]))],j(s,((e,t)=>0===e.length||0===t.length?e.concat(t):e.concat(FT,t)),[])):n;var s}))})(e.filetype,n,o),r=C_(s,pv.BUBBLE_TO_SANDBOX,t,{isHorizontalMenu:!1,search:A.none()});return bS(r)},getHotspot:e=>g.getOpt(e),onSetValue:(e,t)=>{e.hasConfigured(Fk)&&Fk.run(e).get(b)},typeaheadBehaviours:kl([...o.getValidationHandler().map((t=>Fk.config({getRoot:e=>rt(e.element),invalidClass:"tox-control-wrap--status-invalid",notify:{onInvalid:(e,t)=>{c.getOpt(e).each((e=>{kt(e.element,"title",s.translate(t))}))}},validator:{validate:o=>{const n=pu.getValue(o);return OT((o=>{t({type:e.filetype,url:n.value},(e=>{if("invalid"===e.status){const t=sn.error(e.message);o(t)}else{const t=sn.value(e.message);o(t)}}))}))},validateOnLoad:!1}}))).toArray(),Rm.config({disabled:()=>!e.enabled||s.isDisabled()}),ck.config({}),Jp("urlinput-events",[Ur(Zs(),(t=>{const o=$a(t.element),n=o.trim();n!==o&&qa(t.element,n),"file"===e.filetype&&Ir(t,hk,{name:e.name})})),Ur(Qs(),(t=>{Ir(t,hk,{name:e.name}),r(t)})),Ur(cr(),(t=>{Ir(t,hk,{name:e.name}),r(t)}))])]),eventOrder:{[Zs()]:["streaming","urlinput-events","invalidating"]},model:{getDisplayText:e=>e.value,selectsOver:!1,populateFromBrowse:!1},markers:{openClass:"tox-textfield--popup-open"},lazySink:t.shared.getSink,parts:{menu:Bv(0,0,"normal")},onExecute:(e,t,o)=>{Ir(t,yk,{})},onItemExecute:(t,o,n,s)=>{r(t),Ir(t,hk,{name:e.name})}},i=ak.parts.field({...a,factory:kT}),l=e.label.map((e=>pk(e,s))),c=Gh(((e,t,o=e,n=e)=>tb(o,{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+e],attributes:{title:s.translate(n),"aria-live":"polite",...t.fold((()=>({})),(e=>({id:e})))}},s.icons))("invalid",A.some(UT),"warning")),d=Gh({dom:{tag:"div",classes:["tox-control-wrap__status-icon-wrap"]},components:[c.asSpec()]}),u=o.getUrlPicker(e.filetype),m=la("browser.url.event"),g=Gh({dom:{tag:"div",classes:["tox-control-wrap"]},components:[i,d.asSpec()],behaviours:kl([Rm.config({disabled:()=>!e.enabled||s.isDisabled()})])}),p=Gh(MT({name:e.name,icon:A.some("browse"),text:e.label.getOr(""),enabled:e.enabled,primary:!1,buttonType:A.none(),borderless:!0},(e=>Fr(e,m)),s,[],["tox-browse-url"]));return ak.sketch({dom:gk([]),components:l.toArray().concat([{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:q([[g.asSpec()],u.map((()=>p.asSpec())).toArray()])}]),fieldBehaviours:kl([Rm.config({disabled:()=>!e.enabled||s.isDisabled(),onDisabled:e=>{ak.getField(e).each(Rm.disable),p.getOpt(e).each(Rm.disable)},onEnabled:e=>{ak.getField(e).each(Rm.enable),p.getOpt(e).each(Rm.enable)}}),Sx(),Jp("url-input-events",[Ur(m,(t=>{wm.getCurrent(t).each((o=>{const n=pu.getValue(o),s={fieldname:e.name,...n};u.each((n=>{n(s).get((n=>{pu.setValue(o,n),Ir(t,hk,{name:e.name})}))}))}))}))])])})})(e,t,t.urlinput,o))),customeditor:GT((e=>{const t=Ql(),o=Gh({dom:{tag:e.tag}}),n=Ql();return{dom:{tag:"div",classes:["tox-custom-editor"]},behaviours:kl([Jp("custom-editor-events",[Kr((s=>{o.getOpt(s).each((o=>{((e=>ve(e,"init"))(e)?e.init(o.element.dom):HO.load(e.scriptId,e.scriptUrl).then((t=>t(o.element.dom,e.settings)))).then((e=>{n.on((t=>{e.setValue(t)})),n.clear(),t.set(e)}))}))}))]),RO(A.none(),(()=>t.get().fold((()=>n.get().getOr("")),(e=>e.getValue()))),((e,o)=>{t.get().fold((()=>n.set(o)),(e=>e.setValue(o)))})),DO()]),components:[o.asSpec()]}})),htmlpanel:GT((e=>"presentation"===e.presets?ok.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:e.html}}):ok.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:e.html,attributes:{role:"document"}},containerBehaviours:kl([ck.config({}),oh.config({})])}))),imagepreview:GT(((e,t,o)=>((e,t)=>{const o=Es(t.getOr({url:""})),n=Gh({dom:{tag:"img",classes:["tox-imagepreview__image"],attributes:t.map((e=>({src:e.url}))).getOr({})}}),s=Gh({dom:{tag:"div",classes:["tox-imagepreview__container"],attributes:{role:"presentation"}},components:[n.asSpec()]}),r={};e.height.each((e=>r.height=e));const a=t.map((e=>({url:e.url,zoom:A.from(e.zoom),cachedWidth:A.from(e.cachedWidth),cachedHeight:A.from(e.cachedHeight)})));return{dom:{tag:"div",classes:["tox-imagepreview"],styles:r,attributes:{role:"presentation"}},components:[s.asSpec()],behaviours:kl([DO(),RO(a,(()=>o.get()),((e,t)=>{const r={url:t.url};t.zoom.each((e=>r.zoom=e)),t.cachedWidth.each((e=>r.cachedWidth=e)),t.cachedHeight.each((e=>r.cachedHeight=e)),o.set(r);const a=()=>{const{cachedWidth:t,cachedHeight:o,zoom:n}=r;if(!u(t)&&!u(o)){if(u(n)){const n=((e,t,o)=>{const n=Jt(e),s=Wt(e);return Math.min(n/t,s/o,1)})(e.element,t,o);r.zoom=n}const a=((e,t,o,n,s)=>{const r=o*s,a=n*s,i=Math.max(0,e/2-r/2),l=Math.max(0,t/2-a/2);return{left:i.toString()+"px",top:l.toString()+"px",width:r.toString()+"px",height:a.toString()+"px"}})(Jt(e.element),Wt(e.element),t,o,r.zoom);s.getOpt(e).each((e=>{Bt(e.element,a)}))}};n.getOpt(e).each((o=>{const n=o.element;var s;t.url!==Ot(n,"src")&&(kt(n,"src",t.url),Pa(e.element,"tox-imagepreview__loaded")),a(),(s=n,new Promise(((e,t)=>{const o=()=>{r(),e(s)},n=[tc(s,"load",o),tc(s,"error",(()=>{r(),t("Unable to load data from image: "+s.dom.src)}))],r=()=>L(n,(e=>e.unbind()));s.dom.complete&&o()}))).then((t=>{e.getSystem().isConnected()&&(La(e.element,"tox-imagepreview__loaded"),r.cachedWidth=t.dom.naturalWidth,r.cachedHeight=t.dom.naturalHeight,a())}))}))}))])}})(e,o))),table:GT(((e,t)=>((e,t)=>{const o=e=>({dom:{tag:"td",innerHtml:t.translate(e)}});return{dom:{tag:"table",classes:["tox-dialog__table"]},components:[(s=e.header,{dom:{tag:"thead"},components:[{dom:{tag:"tr"},components:H(s,(e=>({dom:{tag:"th",innerHtml:t.translate(e)}})))}]}),(n=e.cells,{dom:{tag:"tbody"},components:H(n,(e=>({dom:{tag:"tr"},components:H(e,o)})))})],behaviours:kl([ck.config({}),oh.config({})])};var n,s})(e,t.shared.providers))),tree:GT(((e,t)=>((e,t)=>{const o=e.onLeafAction.getOr(b),n=e.onToggleExpand.getOr(b),s=e.defaultExpandedIds,r=Es(s),a=Es(e.defaultSelectedId),i=la("tree-id"),l=(n,s)=>e.items.map((e=>"leaf"===e.type?rT({leaf:e,selectedId:n,onLeafAction:o,visible:!0,treeId:i,backstage:t}):dT({directory:e,selectedId:n,onLeafAction:o,expandedIds:s,labelTabstopping:!0,treeId:i,backstage:t})));return{dom:{tag:"div",classes:["tox-tree"],attributes:{role:"tree"}},components:l(a.get(),r.get()),behaviours:kl([Pp.config({mode:"flow",selector:".tox-tree--leaf__label--visible, .tox-tree--directory__label--visible",cycles:!1}),Jp(uT,[Ur("expand-tree-node",((e,t)=>{const{expanded:o,node:s}=t.event;r.set(o?[...r.get(),s]:r.get().filter((e=>e!==s))),n(r.get(),{expanded:o,node:s})}))]),Al.config({channels:{[`update-active-item-${i}`]:{onReceive:(e,t)=>{a.set(A.some(t.value)),Kp.set(e,l(A.some(t.value),r.get()))}}}}),Kp.config({})])}})(e,t))),panel:GT(((e,t)=>((e,t)=>({dom:{tag:"div",classes:e.classes},components:H(e.items,t.shared.interpreter)}))(e,t)))},qT={field:(e,t)=>t,record:x([])},XT=(e,t,o,n)=>{const s=fn(n,{shared:{interpreter:t=>YT(e,t,o,s)}});return YT(e,t,o,s)},YT=(e,t,o,n)=>be($T,t.type).fold((()=>(console.error(`Unknown factory type "${t.type}", defaulting to container: `,t),t)),(s=>s(e,t,o,n))),KT=(e,t,o)=>YT(qT,e,t,o),JT="layout-inset",ZT=e=>e.x,QT=(e,t)=>e.x+e.width/2-t.width/2,eE=(e,t)=>e.x+e.width-t.width,tE=e=>e.y,oE=(e,t)=>e.y+e.height-t.height,nE=(e,t)=>e.y+e.height/2-t.height/2,sE=(e,t,o)=>zi(eE(e,t),oE(e,t),o.insetSouthwest(),Wi(),"southwest",Ki(e,{right:0,bottom:3}),JT),rE=(e,t,o)=>zi(ZT(e),oE(e,t),o.insetSoutheast(),Ui(),"southeast",Ki(e,{left:1,bottom:3}),JT),aE=(e,t,o)=>zi(eE(e,t),tE(e),o.insetNorthwest(),Pi(),"northwest",Ki(e,{right:0,top:2}),JT),iE=(e,t,o)=>zi(ZT(e),tE(e),o.insetNortheast(),Li(),"northeast",Ki(e,{left:1,top:2}),JT),lE=(e,t,o)=>zi(QT(e,t),tE(e),o.insetNorth(),ji(),"north",Ki(e,{top:2}),JT),cE=(e,t,o)=>zi(QT(e,t),oE(e,t),o.insetSouth(),Gi(),"south",Ki(e,{bottom:3}),JT),dE=(e,t,o)=>zi(eE(e,t),nE(e,t),o.insetEast(),qi(),"east",Ki(e,{right:0}),JT),uE=(e,t,o)=>zi(ZT(e),nE(e,t),o.insetWest(),$i(),"west",Ki(e,{left:1}),JT),mE=e=>{switch(e){case"north":return lE;case"northeast":return iE;case"northwest":return aE;case"south":return cE;case"southeast":return rE;case"southwest":return sE;case"east":return dE;case"west":return uE}},gE=(e,t,o,n,s)=>Xl(n).map(mE).getOr(lE)(e,t,o,n,s),pE=e=>{switch(e){case"north":return cE;case"northeast":return rE;case"northwest":return sE;case"south":return lE;case"southeast":return iE;case"southwest":return aE;case"east":return uE;case"west":return dE}},hE=(e,t,o,n,s)=>Xl(n).map(pE).getOr(lE)(e,t,o,n,s),fE={valignCentre:[],alignCentre:[],alignLeft:[],alignRight:[],right:[],left:[],bottom:[],top:[]},bE=(e,t,o)=>{const n={maxHeightFunction:cc()};return()=>o()?{type:"node",root:ft(ht(e())),node:A.from(e()),bubble:gc(12,12,fE),layouts:{onRtl:()=>[iE],onLtr:()=>[aE]},overrides:n}:{type:"hotspot",hotspot:t(),bubble:gc(-12,12,fE),layouts:{onRtl:()=>[sl,rl,cl],onLtr:()=>[rl,sl,cl]},overrides:n}},vE=(e,t,o,n)=>{const s={maxHeightFunction:cc()};return()=>n()?{type:"node",root:ft(ht(t())),node:A.from(t()),bubble:gc(12,12,fE),layouts:{onRtl:()=>[lE],onLtr:()=>[lE]},overrides:s}:e?{type:"node",root:ft(ht(t())),node:A.from(t()),bubble:gc(0,-jt(t()),fE),layouts:{onRtl:()=>[ll],onLtr:()=>[ll]},overrides:s}:{type:"hotspot",hotspot:o(),bubble:gc(0,0,fE),layouts:{onRtl:()=>[ll],onLtr:()=>[ll]},overrides:s}},yE=(e,t,o)=>()=>o()?{type:"node",root:ft(ht(e())),node:A.from(e()),layouts:{onRtl:()=>[lE],onLtr:()=>[lE]}}:{type:"hotspot",hotspot:t(),layouts:{onRtl:()=>[cl],onLtr:()=>[cl]}},xE=(e,t)=>()=>({type:"selection",root:t(),getSelection:()=>{const t=e.selection.getRng(),o=e.model.table.getSelectedCells();if(o.length>1){const e=o[0],t=o[o.length-1],n={firstCell:Ve(e),lastCell:Ve(t)};return A.some(n)}return A.some(Lc.range(Ve(t.startContainer),t.startOffset,Ve(t.endContainer),t.endOffset))}}),wE=e=>t=>({type:"node",root:e(),node:t}),SE=(e,t,o,n)=>{const s=sv(e),r=()=>Ve(e.getBody()),a=()=>Ve(e.getContentAreaContainer()),i=()=>s||!n();return{inlineDialog:bE(a,t,i),inlineBottomDialog:vE(e.inline,a,o,i),banner:yE(a,t,i),cursor:xE(e,r),node:wE(r)}},kE=e=>(t,o)=>{Jw(e)(t,o)},CE=e=>()=>Hw(e),OE=e=>t=>Rw(e,t),_E=e=>t=>zw(e,t),TE=e=>()=>Lb(e),EE=e=>ye(e,"items"),AE=e=>ye(e,"format"),ME=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",format:"bold"},{title:"Italic",format:"italic"},{title:"Underline",format:"underline"},{title:"Strikethrough",format:"strikethrough"},{title:"Superscript",format:"superscript"},{title:"Subscript",format:"subscript"},{title:"Code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Align",items:[{title:"Left",format:"alignleft"},{title:"Center",format:"aligncenter"},{title:"Right",format:"alignright"},{title:"Justify",format:"alignjustify"}]}],DE=e=>j(e,((e,t)=>{if(ve(t,"items")){const o=DE(t.items);return{customFormats:e.customFormats.concat(o.customFormats),formats:e.formats.concat([{title:t.title,items:o.formats}])}}if(ve(t,"inline")||(e=>ve(e,"block"))(t)||(e=>ve(e,"selector"))(t)){const o=`custom-${r(t.name)?t.name:t.title.toLowerCase()}`;return{customFormats:e.customFormats.concat([{name:o,format:t}]),formats:e.formats.concat([{title:t.title,format:o,icon:t.icon}])}}return{...e,formats:e.formats.concat(t)}}),{customFormats:[],formats:[]}),BE=e=>yb(e).map((t=>{const o=((e,t)=>{const o=DE(t),n=t=>{L(t,(t=>{e.formatter.has(t.name)||e.formatter.register(t.name,t.format)}))};return e.formatter?n(o.customFormats):e.on("init",(()=>{n(o.customFormats)})),o.formats})(e,t);return xb(e)?ME.concat(o):o})).getOr(ME),FE=(e,t,o)=>({...e,type:"formatter",isSelected:t(e.format),getStylePreview:o(e.format)}),IE=(e,t,o,n)=>{const s=t=>H(t,(t=>EE(t)?(e=>{const t=s(e.items);return{...e,type:"submenu",getStyleItems:x(t)}})(t):AE(t)?(e=>FE(e,o,n))(t):(e=>{const t=ae(e);return 1===t.length&&R(t,"title")})(t)?{...t,type:"separator"}:(t=>{const s=r(t.name)?t.name:la(t.title),a=`custom-${s}`,i={...t,type:"formatter",format:a,isSelected:o(a),getStylePreview:n(a)};return e.formatter.register(s,i),i})(t)));return s(t)},RE=LO.trim,NE=e=>t=>{if((e=>g(e)&&1===e.nodeType)(t)){if(t.contentEditable===e)return!0;if(t.getAttribute("data-mce-contenteditable")===e)return!0}return!1},VE=NE("true"),zE=NE("false"),HE=(e,t,o,n,s)=>({type:e,title:t,url:o,level:n,attach:s}),LE=e=>e.innerText||e.textContent,PE=e=>(e=>e&&"A"===e.nodeName&&void 0!==(e.id||e.name))(e)&&WE(e),UE=e=>e&&/^(H[1-6])$/.test(e.nodeName),WE=e=>(e=>{let t=e;for(;t=t.parentNode;){const e=t.contentEditable;if(e&&"inherit"!==e)return VE(t)}return!1})(e)&&!zE(e),jE=e=>UE(e)&&WE(e),GE=e=>{var t;const o=(e=>e.id?e.id:la("h"))(e);return HE("header",null!==(t=LE(e))&&void 0!==t?t:"","#"+o,(e=>UE(e)?parseInt(e.nodeName.substr(1),10):0)(e),(()=>{e.id=o}))},$E=e=>{const t=e.id||e.name,o=LE(e);return HE("anchor",o||"#"+t,"#"+t,0,b)},qE=e=>RE(e.title).length>0,XE=e=>{const t=(e=>{const t=H(Xc(Ve(e),"h1,h2,h3,h4,h5,h6,a:not([href])"),(e=>e.dom));return t})(e);return U((e=>H(U(e,jE),GE))(t).concat((e=>H(U(e,PE),$E))(t)),qE)},YE="tinymce-url-history",KE=e=>r(e)&&/^https?/.test(e),JE=e=>a(e)&&he(e,(e=>{return!(l(t=e)&&t.length<=5&&Y(t,KE));var t})).isNone(),ZE=()=>{const e=Sw.getItem(YE);if(null===e)return{};let t;try{t=JSON.parse(e)}catch(e){if(e instanceof SyntaxError)return console.log("Local storage "+YE+" was not valid JSON",e),{};throw e}return JE(t)?t:(console.log("Local storage "+YE+" was not valid format",t),{})},QE=e=>{const t=ZE();return be(t,e).getOr([])},eA=(e,t)=>{if(!KE(e))return;const o=ZE(),n=be(o,t).getOr([]),s=U(n,(t=>t!==e));o[t]=[e].concat(s).slice(0,5),(e=>{if(!JE(e))throw new Error("Bad format for history:\n"+JSON.stringify(e));Sw.setItem(YE,JSON.stringify(e))})(o)},tA=e=>!!e,oA=e=>ce(LO.makeMap(e,/[, ]/),tA),nA=e=>A.from(Fb(e)),sA=e=>A.from(e).filter(r).getOrUndefined(),rA=e=>({getHistory:QE,addToHistory:eA,getLinkInformation:()=>(e=>Vb(e)?A.some({targets:XE(e.getBody()),anchorTop:sA(zb(e)),anchorBottom:sA(Hb(e))}):A.none())(e),getValidationHandler:()=>(e=>A.from(Ib(e)))(e),getUrlPicker:t=>((e,t)=>((e,t)=>{const o=(e=>{const t=A.from(Nb(e)).filter(tA).map(oA);return nA(e).fold(T,(e=>t.fold(E,(e=>ae(e).length>0&&e))))})(e);return d(o)?o?nA(e):A.none():o[t]?nA(e):A.none()})(e,t).map((o=>n=>fS((s=>{const i={filetype:t,fieldname:n.fieldname,...A.from(n.meta).getOr({})};o.call(e,((e,t)=>{if(!r(e))throw new Error("Expected value to be string");if(void 0!==t&&!a(t))throw new Error("Expected meta to be a object");s({value:e,meta:t})}),n.value,i)})))))(e,t)}),aA=dm,iA=qu,lA=x([ys("shell",!1),os("makeItem"),ys("setupItem",b),vu("listBehaviours",[Kp])]),cA=ju({name:"items",overrides:()=>({behaviours:kl([Kp.config({})])})}),dA=x([cA]),uA=bm({name:x("CustomList")(),configFields:lA(),partFields:dA(),factory:(e,t,o,n)=>{const s=e.shell?{behaviours:[Kp.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:s.components,behaviours:bu(e.listBehaviours,s.behaviours),apis:{setItems:(t,o)=>{var n;(n=t,e.shell?A.some(n):om(n,e,"items")).fold((()=>{throw console.error("Custom List was defined to not be a shell, but no item container was specified in components"),new Error("Custom List was defined to not be a shell, but no item container was specified in components")}),(n=>{const s=Kp.contents(n),r=o.length,a=r-s.length,i=a>0?V(a,(()=>e.makeItem())):[],l=s.slice(r);L(l,(e=>Kp.remove(n,e))),L(i,(e=>Kp.append(n,e)));const c=Kp.contents(n);L(c,((n,s)=>{e.setupItem(t,n,o[s],s)}))}))}}}},apis:{setItems:(e,t,o)=>{e.setItems(t,o)}}}),mA=x([os("dom"),ys("shell",!0),hu("toolbarBehaviours",[Kp])]),gA=x([ju({name:"groups",overrides:()=>({behaviours:kl([Kp.config({})])})})]),pA=bm({name:"Toolbar",configFields:mA(),partFields:gA(),factory:(e,t,o,n)=>{const s=e.shell?{behaviours:[Kp.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:s.components,behaviours:bu(e.toolbarBehaviours,s.behaviours),apis:{setGroups:(t,o)=>{var n;(n=t,e.shell?A.some(n):om(n,e,"groups")).fold((()=>{throw console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")}),(e=>{Kp.set(e,o)}))},refresh:b},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)}}}),hA=b,fA=T,bA=x([]);var vA,yA=Object.freeze({__proto__:null,setup:hA,isDocked:fA,getBehaviours:bA});const xA=e=>(xe(Nt(e,"position"),"fixed")?A.none():at(e)).orThunk((()=>{const t=Re("span");return st(e).bind((e=>{zo(e,t);const o=at(t);return Po(t),o}))})),wA=e=>xA(e).map(Xt).getOrThunk((()=>$t(0,0))),SA=(e,t)=>{const o=e.element;La(o,t.transitionClass),Pa(o,t.fadeOutClass),La(o,t.fadeInClass),t.onShow(e)},kA=(e,t)=>{const o=e.element;La(o,t.transitionClass),Pa(o,t.fadeInClass),La(o,t.fadeOutClass),t.onHide(e)},CA=(e,t)=>e.y>=t.y,OA=(e,t)=>e.bottom<=t.bottom,_A=(e,t,o)=>({location:"top",leftX:t,topY:o.bounds.y-e.y}),TA=(e,t,o)=>({location:"bottom",leftX:t,bottomY:e.bottom-o.bounds.bottom}),EA=e=>e.box.x-e.win.x,AA=(e,t,o)=>o.getInitialPos().map((o=>{const n=((e,t)=>{const o=t.optScrollEnv.fold(x(e.bounds.y),(t=>t.scrollElmTop+(e.bounds.y-t.currentScrollTop)));return $t(e.bounds.x,o)})(o,t);return{box:Ko(n.left,n.top,Jt(e),Wt(e)),location:o.location}})),MA=(e,t,o,n,s)=>{const r=((e,t)=>{const o=t.optScrollEnv.fold(x(e.y),(t=>e.y+t.currentScrollTop-t.scrollElmTop));return $t(e.x,o)})(t,o),a=Ko(r.left,r.top,t.width,t.height);n.setInitialPos({style:Vt(e),position:It(e,"position")||"static",bounds:a,location:s.location})},DA=(e,t,o)=>o.getInitialPos().bind((n=>{var s;switch(o.clearInitialPos(),n.position){case"static":return A.some({morph:"static"});case"absolute":const o=xA(e).getOr(xt()),r=Jo(o),a=null!==(s=o.dom.scrollTop)&&void 0!==s?s:0;return A.some({morph:"absolute",positionCss:Vl("absolute",be(n.style,"left").map((e=>t.x-r.x)),be(n.style,"top").map((e=>t.y-r.y+a)),be(n.style,"right").map((e=>r.right-t.right)),be(n.style,"bottom").map((e=>r.bottom-t.bottom)))});default:return A.none()}})),BA=e=>{switch(e.location){case"top":return A.some({morph:"fixed",positionCss:Vl("fixed",A.some(e.leftX),A.some(e.topY),A.none(),A.none())});case"bottom":return A.some({morph:"fixed",positionCss:Vl("fixed",A.some(e.leftX),A.none(),A.none(),A.some(e.bottomY))});default:return A.none()}},FA=(e,t,o)=>{const n=e.element;return xe(Nt(n,"position"),"fixed")?((e,t,o)=>((e,t,o)=>AA(e,t,o).filter((({box:e})=>((e,t,o)=>Y(e,(e=>{switch(e){case"bottom":return OA(t,o.bounds);case"top":return CA(t,o.bounds)}})))(o.getModes(),e,t))).bind((({box:t})=>DA(e,t,o))))(e,t,o).orThunk((()=>t.optScrollEnv.bind((n=>AA(e,t,o))).bind((({box:e,location:o})=>{const n=en(),s=EA({win:n,box:e}),r="top"===o?_A(n,s,t):TA(n,s,t);return BA(r)})))))(n,t,o):((e,t,o)=>{const n=Jo(e),s=en(),r=((e,t,o)=>{const n=t.win,s=t.box,r=EA(t);return re(e,(e=>{switch(e){case"bottom":return OA(s,o.bounds)?A.none():A.some(TA(n,r,o));case"top":return CA(s,o.bounds)?A.none():A.some(_A(n,r,o));default:return A.none()}})).getOr({location:"no-dock"})})(o.getModes(),{win:s,box:n},t);return"top"===r.location||"bottom"===r.location?(MA(e,n,t,o,r),BA(r)):A.none()})(n,t,o)},IA=(e,t,o)=>{o.setDocked(!1),L(["left","right","top","bottom","position"],(t=>Ht(e.element,t))),t.onUndocked(e)},RA=(e,t,o,n)=>{const s="fixed"===n.position;o.setDocked(s),zl(e.element,n),(s?t.onDocked:t.onUndocked)(e)},NA=(e,t,o,n,s=!1)=>{t.contextual.each((t=>{t.lazyContext(e).each((r=>{const a=((e,t)=>e.y<t.bottom&&e.bottom>t.y)(r,n.bounds);a!==o.isVisible()&&(o.setVisible(a),s&&!a?(Wa(e.element,[t.fadeOutClass]),t.onHide(e)):(a?SA:kA)(e,t))}))}))},VA=(e,t,o,n,s)=>{NA(e,t,o,n,!0),RA(e,t,o,s.positionCss)},zA=(e,t,o)=>{e.getSystem().isConnected()&&((e,t,o)=>{const n=t.lazyViewport(e);NA(e,t,o,n),FA(e,n,o).each((s=>{((e,t,o,n,s)=>{switch(s.morph){case"static":return IA(e,t,o);case"absolute":return RA(e,t,o,s.positionCss);case"fixed":VA(e,t,o,n,s)}})(e,t,o,n,s)}))})(e,t,o)},HA=(e,t,o)=>{o.isDocked()&&((e,t,o)=>{const n=e.element;o.setDocked(!1);const s=t.lazyViewport(e);((e,t,o)=>{const n=e.element;return AA(n,t,o).bind((({box:e})=>DA(n,e,o)))})(e,s,o).each((n=>{switch(n.morph){case"static":IA(e,t,o);break;case"absolute":RA(e,t,o,n.positionCss)}})),o.setVisible(!0),t.contextual.each((t=>{ja(n,[t.fadeInClass,t.fadeOutClass,t.transitionClass]),t.onShow(e)})),zA(e,t,o)})(e,t,o)},LA=e=>(t,o,n)=>{const s=o.lazyViewport(t);((e,t,o,n)=>{const s=Jo(e),r=en(),a=n(r,EA({win:r,box:s}),t);return"bottom"===a.location||"top"===a.location?(((e,t,o,n,s)=>{n.getInitialPos().fold((()=>MA(e,t,o,n,s)),(()=>b))})(e,s,t,o,a),BA(a)):A.none()})(t.element,s,n,e).each((e=>{VA(t,o,n,s,e)}))},PA=LA(_A),UA=LA(TA);var WA=Object.freeze({__proto__:null,refresh:zA,reset:HA,isDocked:(e,t,o)=>o.isDocked(),getModes:(e,t,o)=>o.getModes(),setModes:(e,t,o,n)=>o.setModes(n),forceDockToTop:PA,forceDockToBottom:UA}),jA=Object.freeze({__proto__:null,events:(e,t)=>Hr([Yr(or(),((o,n)=>{e.contextual.each((e=>{Ua(o.element,e.transitionClass)&&(ja(o.element,[e.transitionClass,e.fadeInClass]),(t.isVisible()?e.onShown:e.onHidden)(o)),n.stop()}))})),Ur(xr(),((o,n)=>{zA(o,e,t)})),Ur(Er(),((o,n)=>{zA(o,e,t)})),Ur(wr(),((o,n)=>{HA(o,e,t)}))])}),GA=[vs("contextual",[rs("fadeInClass"),rs("fadeOutClass"),rs("transitionClass"),is("lazyContext"),Di("onShow"),Di("onShown"),Di("onHide"),Di("onHidden")]),Os("lazyViewport",(()=>({bounds:en(),optScrollEnv:A.none()}))),_s("modes",["top","bottom"],Hn),Di("onDocked"),Di("onUndocked")];const $A=Ol({fields:GA,name:"docking",active:jA,apis:WA,state:Object.freeze({__proto__:null,init:e=>{const t=Es(!1),o=Es(!0),n=Ql(),s=Es(e.modes);return _a({isDocked:t.get,setDocked:t.set,getInitialPos:n.get,setInitialPos:n.set,clearInitialPos:n.clear,isVisible:o.get,setVisible:o.set,getModes:s.get,setModes:s.set,readState:()=>`docked:  ${t.get()}, visible: ${o.get()}, modes: ${s.get().join(",")}`})}})}),qA=x(la("toolbar-height-change")),XA={fadeInClass:"tox-editor-dock-fadein",fadeOutClass:"tox-editor-dock-fadeout",transitionClass:"tox-editor-dock-transition"},YA="tox-tinymce--toolbar-sticky-on",KA="tox-tinymce--toolbar-sticky-off",JA=(e,t)=>R($A.getModes(e),t),ZA=e=>{const t=e.element;rt(t).each((o=>{const n="padding-"+$A.getModes(e)[0];if($A.isDocked(e)){const e=Jt(o);Dt(t,"width",e+"px"),Dt(o,n,(e=>jt(e)+(parseInt(It(e,"margin-top"),10)||0)+(parseInt(It(e,"margin-bottom"),10)||0))(t)+"px")}else Ht(t,"width"),Ht(o,n)}))},QA=(e,t)=>{t?(Pa(e,XA.fadeOutClass),Wa(e,[XA.transitionClass,XA.fadeInClass])):(Pa(e,XA.fadeInClass),Wa(e,[XA.fadeOutClass,XA.transitionClass]))},eM=(e,t)=>{const o=Ve(e.getContainer());t?(La(o,YA),Pa(o,KA)):(La(o,KA),Pa(o,YA))},tM=(e,t)=>{const o=Ql(),n=t.getSink,s=e=>{n().each((t=>e(t.element)))},r=t=>{e.inline||ZA(t),eM(e,$A.isDocked(t)),t.getSystem().broadcastOn([Kd()],{}),n().each((e=>e.getSystem().broadcastOn([Kd()],{})))},a=e.inline?[]:[Al.config({channels:{[qA()]:{onReceive:ZA}}})];return[oh.config({}),$A.config({contextual:{lazyContext:t=>{const o=jt(t.element),n=e.inline?e.getContentAreaContainer():e.getContainer();return A.from(n).map((n=>{const s=Jo(Ve(n));return jS(e,t.element).fold((()=>{const e=s.height-o,n=s.y+(JA(t,"top")?0:o);return Ko(s.x,n,s.width,e)}),(e=>{const n=Qo(s,GS(e)),r=JA(t,"top")?n.y:n.y+o;return Ko(n.x,r,n.width,n.height-o)}))}))},onShow:()=>{s((e=>QA(e,!0)))},onShown:e=>{s((e=>ja(e,[XA.transitionClass,XA.fadeInClass]))),o.get().each((t=>{((e,t)=>{const o=et(t);Il(o).filter((e=>!Ze(t,e))).filter((t=>Ze(t,Ve(o.dom.body))||Qe(e,t))).each((()=>Dl(t)))})(e.element,t),o.clear()}))},onHide:e=>{((e,t)=>Rl(e).orThunk((()=>t().toOptional().bind((e=>Rl(e.element))))))(e.element,n).fold(o.clear,o.set),s((e=>QA(e,!1)))},onHidden:()=>{s((e=>ja(e,[XA.transitionClass])))},...XA},lazyViewport:t=>jS(e,t.element).fold((()=>{const o=en(),n=Mb(e),s=o.y+(JA(t,"top")?n:0),r=o.height-(JA(t,"bottom")?n:0);return{bounds:Ko(o.x,s,o.width,r),optScrollEnv:A.none()}}),(e=>({bounds:GS(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:Xt(e.element).top})}))),modes:[t.header.getDockingMode()],onDocked:r,onUndocked:r}),...a]};var oM=Object.freeze({__proto__:null,setup:(e,t,o)=>{e.inline||(t.header.isPositionedAtTop()||e.on("ResizeEditor",(()=>{o().each($A.reset)})),e.on("ResizeWindow ResizeEditor",(()=>{o().each(ZA)})),e.on("SkinLoaded",(()=>{o().each((e=>{$A.isDocked(e)?$A.reset(e):$A.refresh(e)}))})),e.on("FullscreenStateChanged",(()=>{o().each($A.reset)}))),e.on("AfterScrollIntoView",(e=>{o().each((t=>{$A.refresh(t);const o=t.element;Ig(o)&&((e,t)=>{const o=et(t),n=nt(t).dom.innerHeight,s=Uo(o),r=Ve(e.elm),a=Zo(r),i=Wt(r),l=a.y,c=l+i,d=Xt(t),u=Wt(t),m=d.top,g=m+u,p=Math.abs(m-s.top)<2,h=Math.abs(g-(s.top+n))<2;if(p&&l<g)Wo(s.left,l-u,o);else if(h&&c>m){const e=l-n+i+u;Wo(s.left,e,o)}})(e,o)}))})),e.on("PostRender",(()=>{eM(e,!1)}))},isDocked:e=>e().map($A.isDocked).getOr(!1),getBehaviours:tM});const nM=Dn([ty,ns("items",Fn([Rn([oy,ds("items",Hn)]),Hn]))].concat(Dy)),sM=[ps("text"),ps("tooltip"),ps("icon"),xs("search",!1,Fn([Ln,Dn([ps("placeholder")])],(e=>d(e)?e?A.some({placeholder:A.none()}):A.none():A.some(e)))),is("fetch"),Os("onSetup",(()=>b))],rM=Dn([ty,...sM]),aM=e=>qn("menubutton",rM,e),iM=Dn([ty,fy,hy,py,yy,ly,my,ks("presets","normal",["normal","color","listpreview"]),Cy(1),dy,uy]);var lM=fm({factory:(e,t)=>{const o={focus:Pp.focusIn,setMenus:(e,o)=>{const n=H(o,(e=>{const o={type:"menubutton",text:e.text,fetch:t=>{t(e.getItems())}},n=aM(o).mapError((e=>Kn(e))).getOrDie();return tT(n,"tox-mbtn",t.backstage,A.some("menuitem"))}));Kp.set(e,n)}};return{uid:e.uid,dom:e.dom,components:[],behaviours:kl([Kp.config({}),Jp("menubar-events",[Kr((t=>{e.onSetup(t)})),Ur(qs(),((e,t)=>{pi(e.element,".tox-mbtn--active").each((o=>{hi(t.event.target,".tox-mbtn").each((t=>{Ze(o,t)||e.getSystem().getByDom(o).each((o=>{e.getSystem().getByDom(t).each((e=>{DS.expand(e),DS.close(o),oh.focus(e)}))}))}))}))})),Ur(_r(),((e,t)=>{t.event.prevFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((o=>{t.event.newFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((e=>{DS.isOpen(o)&&(DS.expand(e),DS.close(o))}))}))}))]),Pp.config({mode:"flow",selector:".tox-mbtn",onEscape:t=>(e.onEscape(t),A.some(!0))}),ck.config({})]),apis:o,domModification:{attributes:{role:"menubar"}}}},name:"silver.Menubar",configFields:[os("dom"),os("uid"),os("onEscape"),os("backstage"),ys("onSetup",b)],apis:{focus:(e,t)=>{e.focus(t)},setMenus:(e,t,o)=>{e.setMenus(t,o)}}});const cM="container",dM=[hu("slotBehaviours",[])],uM=e=>"<alloy.field."+e+">",mM=(e,t)=>{const o=t=>am(e),n=(t,o)=>(n,s)=>om(n,e,s).map((e=>t(e,s))).getOr(o),s=(e,t)=>"true"!==Ot(e.element,"aria-hidden"),r=n(s,!1),a=n(((e,t)=>{if(s(e)){const o=e.element;Dt(o,"display","none"),kt(o,"aria-hidden","true"),Ir(e,Tr(),{name:t,visible:!1})}})),i=(e=>(t,o)=>{L(o,(o=>e(t,o)))})(a),l=n(((e,t)=>{if(!s(e)){const o=e.element;Ht(o,"display"),Et(o,"aria-hidden"),Ir(e,Tr(),{name:t,visible:!0})}})),c={getSlotNames:o,getSlot:(t,o)=>om(t,e,o),isShowing:r,hideSlot:a,hideAllSlots:e=>i(e,o()),showSlot:l};return{uid:e.uid,dom:e.dom,components:t,behaviours:fu(e.slotBehaviours),apis:c}},gM=ce({getSlotNames:(e,t)=>e.getSlotNames(t),getSlot:(e,t,o)=>e.getSlot(t,o),isShowing:(e,t,o)=>e.isShowing(t,o),hideSlot:(e,t,o)=>e.hideSlot(t,o),hideAllSlots:(e,t)=>e.hideAllSlots(t),showSlot:(e,t,o)=>e.showSlot(t,o)},(e=>Ca(e))),pM={...gM,sketch:e=>{const t=(()=>{const e=[];return{slot:(t,o)=>(e.push(t),Ju(cM,uM(t),o)),record:x(e)}})(),o=e(t),n=t.record(),s=H(n,(e=>Uu({name:e,pname:uM(e)})));return mm(cM,dM,s,mM,o)}},hM=Dn([hy,fy,Os("onShow",b),Os("onHide",b),my]),fM=e=>({element:()=>e.element.dom}),bM=(e,t)=>{const o=H(ae(t),(e=>{const o=t[e],n=Xn((e=>qn("sidebar",hM,e))(o));return{name:e,getApi:fM,onSetup:n.onSetup,onShow:n.onShow,onHide:n.onHide}}));return H(o,(t=>{const n=Es(b);return e.slot(t.name,{dom:{tag:"div",classes:["tox-sidebar__pane"]},behaviours:lx([Tx(t,n),Ex(t,n),Ur(Tr(),((e,t)=>{const n=t.event,s=G(o,(e=>e.name===n.name));s.each((t=>{(n.visible?t.onShow:t.onHide)(t.getApi(e))}))}))])})}))},vM=e=>pM.sketch((t=>({dom:{tag:"div",classes:["tox-sidebar__pane-container"]},components:bM(t,e),slotBehaviours:lx([Kr((e=>pM.hideAllSlots(e)))])}))),yM=(e,t)=>{kt(e,"role",t)},xM=e=>wm.getCurrent(e).bind((e=>Q_.isGrowing(e)||Q_.hasGrown(e)?wm.getCurrent(e).bind((e=>G(pM.getSlotNames(e),(t=>pM.isShowing(e,t))))):A.none())),wM=la("FixSizeEvent"),SM=la("AutoSizeEvent");var kM=Object.freeze({__proto__:null,block:(e,t,o,n)=>{kt(e.element,"aria-busy",!0);const s=t.getRoot(e).getOr(e),r=kl([Pp.config({mode:"special",onTab:()=>A.some(!0),onShiftTab:()=>A.some(!0)}),oh.config({})]),a=n(s,r),i=s.getSystem().build(a);Kp.append(s,ai(i)),i.hasConfigured(Pp)&&t.focus&&Pp.focusIn(i),o.isBlocked()||t.onBlock(e),o.blockWith((()=>Kp.remove(s,i)))},unblock:(e,t,o)=>{Et(e.element,"aria-busy"),o.isBlocked()&&t.onUnblock(e),o.clear()},isBlocked:(e,t,o)=>o.isBlocked()}),CM=[Os("getRoot",A.none),Cs("focus",!0),Di("onBlock"),Di("onUnblock")];const OM=Ol({fields:CM,name:"blocking",apis:kM,state:Object.freeze({__proto__:null,init:()=>{const e=Jl((e=>e.destroy()));return _a({readState:e.isSet,blockWith:t=>{e.set({destroy:t})},clear:e.clear,isBlocked:e.isSet})}})}),_M=e=>wm.getCurrent(e).each((e=>Dl(e.element))),TM=(e,t,o)=>{const n=Es(!1),s=Ql(),r=o=>{var s;n.get()&&(!(e=>"focusin"===e.type)(s=o)||!(s.composed?oe(s.composedPath()):A.from(s.target)).map(Ve).filter(Ge).exists((e=>Ua(e,"mce-pastebin"))))&&(o.preventDefault(),_M(t()),e.editorManager.setActive(e))};e.inline||e.on("PreInit",(()=>{e.dom.bind(e.getWin(),"focusin",r),e.on("BeforeExecCommand",(e=>{"mcefocus"===e.command.toLowerCase()&&!0!==e.value&&r(e)}))}));const a=s=>{s!==n.get()&&(n.set(s),((e,t,o,n)=>{const s=t.element;if(((e,t)=>{const o="tabindex",n=`data-mce-${o}`;A.from(e.iframeElement).map(Ve).each((e=>{t?(_t(e,o).each((t=>kt(e,n,t))),kt(e,o,-1)):(Et(e,o),_t(e,n).each((t=>{kt(e,o,t),Et(e,n)})))}))})(e,o),o)OM.block(t,(e=>(t,o)=>({dom:{tag:"div",attributes:{"aria-label":e.translate("Loading..."),tabindex:"0"},classes:["tox-throbber__busy-spinner"]},components:[{dom:jh('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}))(n)),Ht(s,"display"),Et(s,"aria-hidden"),e.hasFocus()&&_M(t);else{const o=wm.getCurrent(t).exists((e=>Fl(e.element)));OM.unblock(t),Dt(s,"display","none"),kt(s,"aria-hidden","true"),o&&e.focus()}})(e,t(),s,o.providers),((e,t)=>{e.dispatch("AfterProgressState",{state:t})})(e,s))};e.on("ProgressState",(t=>{if(s.on(clearTimeout),h(t.time)){const o=Uh.setEditorTimeout(e,(()=>a(t.state)),t.time);s.set(o)}else a(t.state),s.clear()}))},EM=(e,t,o)=>({within:e,extra:t,withinWidth:o}),AM=(e,t,o)=>{const n=j(e,((e,t)=>((e,t)=>{const n=o(e);return A.some({element:e,start:t,finish:t+n,width:n})})(t,e.len).fold(x(e),(t=>({len:t.finish,list:e.list.concat([t])})))),{len:0,list:[]}).list,s=U(n,(e=>e.finish<=t)),r=W(s,((e,t)=>e+t.width),0);return{within:s,extra:n.slice(s.length),withinWidth:r}},MM=e=>H(e,(e=>e.element)),DM=(e,t)=>{const o=H(t,(e=>ai(e)));pA.setGroups(e,o)},BM=(e,t,o)=>{const n=t.builtGroups.get();if(0===n.length)return;const s=nm(e,t,"primary"),r=uS.getCoupled(e,"overflowGroup");Dt(s.element,"visibility","hidden");const a=n.concat([r]),i=re(a,(e=>Rl(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()))));o([]),DM(s,a);const l=((e,t,o,n)=>{const s=((e,t,o)=>{const n=AM(t,e,o);return 0===n.extra.length?A.some(n):A.none()})(e,t,o).getOrThunk((()=>AM(t,e-o(n),o))),r=s.within,a=s.extra,i=s.withinWidth;return 1===a.length&&a[0].width<=o(n)?((e,t,o)=>{const n=MM(e.concat(t));return EM(n,[],o)})(r,a,i):a.length>=1?((e,t,o,n)=>{const s=MM(e).concat([o]);return EM(s,MM(t),n)})(r,a,n,i):((e,t,o)=>EM(MM(e),[],o))(r,0,i)})(Jt(s.element),t.builtGroups.get(),(e=>Jt(e.element)),r);0===l.extra.length?(Kp.remove(s,r),o([])):(DM(s,l.within),o(l.extra)),Ht(s.element,"visibility"),Lt(s.element),i.each(oh.focus)},FM=x([hu("splitToolbarBehaviours",[uS]),es("builtGroups",(()=>Es([])))]),IM=x([Ai(["overflowToggledClass"]),fs("getOverflowBounds"),os("lazySink"),es("overflowGroups",(()=>Es([]))),Di("onOpened"),Di("onClosed")].concat(FM())),RM=x([Uu({factory:pA,schema:mA(),name:"primary"}),Wu({schema:mA(),name:"overflow"}),Wu({name:"overflow-button"}),Wu({name:"overflow-group"})]),NM=x(((e,t)=>{((e,t)=>{const o=Kt.max(e,t,["margin-left","border-left-width","padding-left","padding-right","border-right-width","margin-right"]);Dt(e,"max-width",o+"px")})(e,Math.floor(t))})),VM=x([Ai(["toggledClass"]),os("lazySink"),is("fetch"),fs("getBounds"),vs("fireDismissalEventInstead",[ys("event",Cr())]),wc(),Di("onToggled")]),zM=x([Wu({name:"button",overrides:e=>({dom:{attributes:{"aria-haspopup":"true"}},buttonBehaviours:kl([dh.config({toggleClass:e.markers.toggledClass,aria:{mode:"expanded"},toggleOnExecute:!1,onToggled:e.onToggled})])})}),Wu({factory:pA,schema:mA(),name:"toolbar",overrides:e=>({toolbarBehaviours:kl([Pp.config({mode:"cyclic",onEscape:t=>(om(t,e,"button").each(oh.focus),A.none())})])})})]),HM=Ql(),LM=(e,t)=>{const o=uS.getCoupled(e,"toolbarSandbox");Xd.isOpen(o)?Xd.close(o):Xd.open(o,t.toolbar())},PM=(e,t,o,n)=>{const s=o.getBounds.map((e=>e())),r=o.lazySink(e).getOrDie();Sd.positionWithinBounds(r,t,{anchor:{type:"hotspot",hotspot:e,layouts:n,overrides:{maxWidthFunction:NM()}}},s)},UM=(e,t,o,n,s)=>{pA.setGroups(t,s),PM(e,t,o,n),dh.on(e)},WM=bm({name:"FloatingToolbarButton",factory:(e,t,o,n)=>({...Wh.sketch({...n.button(),action:e=>{LM(e,n)},buttonBehaviours:yu({dump:n.button().buttonBehaviours},[uS.config({others:{toolbarSandbox:t=>((e,t,o)=>{const n=bi();return{dom:{tag:"div",attributes:{id:n.id}},behaviours:kl([Pp.config({mode:"special",onEscape:e=>(Xd.close(e),A.some(!0))}),Xd.config({onOpen:(s,r)=>{const a=HM.get().getOr(!1);o.fetch().get((s=>{UM(e,r,o,t.layouts,s),n.link(e.element),a||Pp.focusIn(r)}))},onClose:()=>{dh.off(e),HM.get().getOr(!1)||oh.focus(e),n.unlink(e.element)},isPartOf:(t,o,n)=>vi(o,n)||vi(e,n),getAttachPoint:()=>o.lazySink(e).getOrDie()}),Al.config({channels:{...Qd({isExtraPart:T,...o.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...tu({doReposition:()=>{Xd.getState(uS.getCoupled(e,"toolbarSandbox")).each((n=>{PM(e,n,o,t.layouts)}))}})}})])}})(t,o,e)}})])}),apis:{setGroups:(t,n)=>{Xd.getState(uS.getCoupled(t,"toolbarSandbox")).each((s=>{UM(t,s,e,o.layouts,n)}))},reposition:t=>{Xd.getState(uS.getCoupled(t,"toolbarSandbox")).each((n=>{PM(t,n,e,o.layouts)}))},toggle:e=>{LM(e,n)},toggleWithoutFocusing:e=>{((e,t)=>{HM.set(!0),LM(e,t),HM.clear()})(e,n)},getToolbar:e=>Xd.getState(uS.getCoupled(e,"toolbarSandbox")),isOpen:e=>Xd.isOpen(uS.getCoupled(e,"toolbarSandbox"))}}),configFields:VM(),partFields:zM(),apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},toggleWithoutFocusing:(e,t)=>{e.toggleWithoutFocusing(t)},getToolbar:(e,t)=>e.getToolbar(t),isOpen:(e,t)=>e.isOpen(t)}}),jM=x([os("items"),Ai(["itemSelector"]),hu("tgroupBehaviours",[Pp])]),GM=x([Gu({name:"items",unit:"item"})]),$M=bm({name:"ToolbarGroup",configFields:jM(),partFields:GM(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:bu(e.tgroupBehaviours,[Pp.config({mode:"flow",selector:e.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}})}),qM=e=>H(e,(e=>ai(e))),XM=(e,t,o)=>{BM(e,o,(n=>{o.overflowGroups.set(n),t.getOpt(e).each((e=>{WM.setGroups(e,qM(n))}))}))},YM=bm({name:"SplitFloatingToolbar",configFields:IM(),partFields:RM(),factory:(e,t,o,n)=>{const s=Gh(WM.sketch({fetch:()=>fS((t=>{t(qM(e.overflowGroups.get()))})),layouts:{onLtr:()=>[rl,sl],onRtl:()=>[sl,rl],onBottomLtr:()=>[il,al],onBottomRtl:()=>[al,il]},getBounds:o.getOverflowBounds,lazySink:e.lazySink,fireDismissalEventInstead:{},markers:{toggledClass:e.markers.overflowToggledClass},parts:{button:n["overflow-button"](),toolbar:n.overflow()},onToggled:(t,o)=>e[o?"onOpened":"onClosed"](t)}));return{uid:e.uid,dom:e.dom,components:t,behaviours:bu(e.splitToolbarBehaviours,[uS.config({others:{overflowGroup:()=>$M.sketch({...n["overflow-group"](),items:[s.asSpec()]})}})]),apis:{setGroups:(t,o)=>{e.builtGroups.set(H(o,t.getSystem().build)),XM(t,s,e)},refresh:t=>XM(t,s,e),toggle:e=>{s.getOpt(e).each((e=>{WM.toggle(e)}))},toggleWithoutFocusing:e=>{s.getOpt(e).each(WM.toggleWithoutFocusing)},isOpen:e=>s.getOpt(e).map(WM.isOpen).getOr(!1),reposition:e=>{s.getOpt(e).each((e=>{WM.reposition(e)}))},getOverflow:e=>s.getOpt(e).bind(WM.getToolbar)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},toggleWithoutFocusing:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t),getOverflow:(e,t)=>e.getOverflow(t)}}),KM=x([Ai(["closedClass","openClass","shrinkingClass","growingClass","overflowToggledClass"]),Di("onOpened"),Di("onClosed")].concat(FM())),JM=x([Uu({factory:pA,schema:mA(),name:"primary"}),Uu({factory:pA,schema:mA(),name:"overflow",overrides:e=>({toolbarBehaviours:kl([Q_.config({dimension:{property:"height"},closedClass:e.markers.closedClass,openClass:e.markers.openClass,shrinkingClass:e.markers.shrinkingClass,growingClass:e.markers.growingClass,onShrunk:t=>{om(t,e,"overflow-button").each((e=>{dh.off(e),oh.focus(e)})),e.onClosed(t)},onGrown:t=>{Pp.focusIn(t),e.onOpened(t)},onStartGrow:t=>{om(t,e,"overflow-button").each(dh.on)}}),Pp.config({mode:"acyclic",onEscape:t=>(om(t,e,"overflow-button").each(oh.focus),A.some(!0))})])})}),Wu({name:"overflow-button",overrides:e=>({buttonBehaviours:kl([dh.config({toggleClass:e.markers.overflowToggledClass,aria:{mode:"pressed"},toggleOnExecute:!1})])})}),Wu({name:"overflow-group"})]),ZM=(e,t)=>{om(e,t,"overflow-button").bind((()=>om(e,t,"overflow"))).each((o=>{QM(e,t),Q_.toggleGrow(o)}))},QM=(e,t)=>{om(e,t,"overflow").each((o=>{BM(e,t,(e=>{const t=H(e,(e=>ai(e)));pA.setGroups(o,t)})),om(e,t,"overflow-button").each((e=>{Q_.hasGrown(o)&&dh.on(e)})),Q_.refresh(o)}))},eD=bm({name:"SplitSlidingToolbar",configFields:KM(),partFields:JM(),factory:(e,t,o,n)=>{const s="alloy.toolbar.toggle";return{uid:e.uid,dom:e.dom,components:t,behaviours:bu(e.splitToolbarBehaviours,[uS.config({others:{overflowGroup:e=>$M.sketch({...n["overflow-group"](),items:[Wh.sketch({...n["overflow-button"](),action:t=>{Fr(e,s)}})]})}}),Jp("toolbar-toggle-events",[Ur(s,(t=>{ZM(t,e)}))])]),apis:{setGroups:(t,o)=>{((t,o)=>{const n=H(o,t.getSystem().build);e.builtGroups.set(n)})(t,o),QM(t,e)},refresh:t=>QM(t,e),toggle:t=>ZM(t,e),isOpen:t=>((e,t)=>om(e,t,"overflow").map(Q_.hasGrown).getOr(!1))(t,e)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},toggle:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t)}}),tD=e=>{const t=e.title.fold((()=>({})),(e=>({attributes:{title:e}})));return{dom:{tag:"div",classes:["tox-toolbar__group"],...t},components:[$M.parts.items({})],items:e.items,markers:{itemSelector:"*:not(.tox-split-button) > .tox-tbtn:not([disabled]), .tox-split-button:not([disabled]), .tox-toolbar-nav-js:not([disabled]), .tox-number-input:not([disabled])"},tgroupBehaviours:kl([ck.config({}),oh.config({})])}},oD=e=>$M.sketch(tD(e)),nD=(e,t)=>{const o=Kr((t=>{const o=H(e.initGroups,oD);pA.setGroups(t,o)}));return kl([Ox(e.providers.isDisabled),Sx(),Pp.config({mode:t,onEscape:e.onEscape,selector:".tox-toolbar__group"}),Jp("toolbar-events",[o])])},sD=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return{uid:e.uid,dom:{tag:"div",classes:["tox-toolbar-overlord"]},parts:{"overflow-group":tD({title:A.none(),items:[]}),"overflow-button":TT({name:"more",icon:A.some("more-drawer"),enabled:!0,tooltip:A.some("Reveal or hide additional toolbar items"),primary:!1,buttonType:A.none(),borderless:!1},A.none(),e.providers)},splitToolbarBehaviours:nD(e,t)}},rD=e=>{const t=sD(e),o=YM.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}});return YM.sketch({...t,lazySink:e.getSink,getOverflowBounds:()=>{const t=e.moreDrawerData.lazyHeader().element,o=Zo(t),n=ot(t),s=Zo(n),r=Math.max(n.dom.scrollHeight,s.height);return Ko(o.x+4,s.y,o.width-8,r)},parts:{...t.parts,overflow:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:e.attributes}}},components:[o],markers:{overflowToggledClass:"tox-tbtn--enabled"},onOpened:t=>e.onToggled(t,!0),onClosed:t=>e.onToggled(t,!1)})},aD=e=>{const t=eD.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}}),o=eD.parts.overflow({dom:{tag:"div",classes:["tox-toolbar__overflow"]}}),n=sD(e);return eD.sketch({...n,components:[t,o],markers:{openClass:"tox-toolbar__overflow--open",closedClass:"tox-toolbar__overflow--closed",growingClass:"tox-toolbar__overflow--growing",shrinkingClass:"tox-toolbar__overflow--shrinking",overflowToggledClass:"tox-tbtn--enabled"},onOpened:t=>{t.getSystem().broadcastOn([qA()],{type:"opened"}),e.onToggled(t,!0)},onClosed:t=>{t.getSystem().broadcastOn([qA()],{type:"closed"}),e.onToggled(t,!1)}})},iD=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return pA.sketch({uid:e.uid,dom:{tag:"div",classes:["tox-toolbar"].concat(e.type===sb.scrolling?["tox-toolbar--scrolling"]:[])},components:[pA.parts.groups({})],toolbarBehaviours:nD(e,t)})},lD=[py,hy,ps("tooltip"),ks("buttonType","secondary",["primary","secondary"]),Cs("borderless",!1),is("onAction")],cD={button:[...lD,sy,as("type",["button"])],togglebutton:[...lD,Cs("active",!1),as("type",["togglebutton"])]},dD=[as("type",["group"]),_s("buttons",[],Jn("type",cD))],uD=Jn("type",{...cD,group:dD}),mD=Dn([_s("buttons",[],uD),is("onShow"),is("onHide")]),gD=(e,t)=>((e,t)=>{var o,n;const s="togglebutton"===e.type,r=e.icon.map((e=>f_(e,t.icons))).map(Gh),a={...e,name:s?e.text.getOr(e.icon.getOr("")):null!==(o=e.text)&&void 0!==o?o:e.icon.getOr(""),primary:"primary"===e.buttonType,buttonType:A.from(e.buttonType),tooltip:e.tooltip,icon:e.icon,enabled:!0,borderless:e.borderless},i=ET(null!==(n=e.buttonType)&&void 0!==n?n:"secondary"),l=s?e.text.map(t.translate):A.some(t.translate(e.text)),c=l.map(ti),d=a.tooltip.or(l).map((e=>({"aria-label":t.translate(e),title:t.translate(e)}))).getOr({}),u=r.map((e=>e.asSpec())),m=Dx([u,c]),g=e.icon.isSome()&&c.isSome(),p={tag:"button",classes:i.concat(...e.icon.isSome()&&!g?["tox-button--icon"]:[]).concat(...g?["tox-button--icon-and-text"]:[]).concat(...e.borderless?["tox-button--naked"]:[]).concat(..."togglebutton"===e.type&&e.active?["tox-button--enabled"]:[]),attributes:d},h=_T(a,A.some((o=>{const n=e=>{r.map((n=>n.getOpt(o).each((o=>{Kp.set(o,[f_(e,t.icons)])}))))};return s?e.onAction({setIcon:n,setActive:e=>{const t=o.element;e?(La(t,"tox-button--enabled"),kt(t,"aria-pressed",!0)):(Pa(t,"tox-button--enabled"),Et(t,"aria-pressed"))},isActive:()=>Ua(o.element,"tox-button--enabled")}):"button"===e.type?e.onAction({setIcon:n}):void 0})),[],p,m,t);return Wh.sketch(h)})(e,t),pD=Do().deviceType,hD=pD.isPhone(),fD=pD.isTablet();var bD=bm({name:"silver.View",configFields:[os("viewConfig")],partFields:[ju({factory:{sketch:e=>{let t=!1;const o=H(e.buttons,(o=>"group"===o.type?(t=!0,((e,t)=>({dom:{tag:"div",classes:["tox-view__toolbar__group"]},components:H(e.buttons,(e=>gD(e,t)))}))(o,e.providers)):gD(o,e.providers)));return{uid:e.uid,dom:{tag:"div",classes:[t?"tox-view__toolbar":"tox-view__header",...hD||fD?["tox-view--mobile","tox-view--scrolling"]:[]]},behaviours:kl([oh.config({}),Pp.config({mode:"flow",selector:"button, .tox-button",focusInside:pg.OnEnterOrSpaceMode})]),components:t?o:[ok.sketch({dom:{tag:"div",classes:["tox-view__header-start"]},components:[]}),ok.sketch({dom:{tag:"div",classes:["tox-view__header-end"]},components:o})]}}},schema:[os("buttons"),os("providers")],name:"header"}),ju({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-view__pane"]}})},schema:[],name:"pane"})],factory:(e,t,o,n)=>{const s={getPane:t=>aA.getPart(t,e,"pane"),getOnShow:t=>e.viewConfig.onShow,getOnHide:t=>e.viewConfig.onHide};return{uid:e.uid,dom:e.dom,components:t,apis:s}},apis:{getPane:(e,t)=>e.getPane(t),getOnShow:(e,t)=>e.getOnShow(t),getOnHide:(e,t)=>e.getOnHide(t)}});const vD=(e,t,o)=>pe(t,((t,n)=>{const s=Xn(qn("view",mD,t));return e.slot(n,bD.sketch({dom:{tag:"div",classes:["tox-view"]},viewConfig:s,components:[...s.buttons.length>0?[bD.parts.header({buttons:s.buttons,providers:o})]:[],bD.parts.pane({})]}))})),yD=(e,t)=>pM.sketch((o=>({dom:{tag:"div",classes:["tox-view-wrap__slot-container"]},components:vD(o,e,t),slotBehaviours:lx([Kr((e=>pM.hideAllSlots(e)))])}))),xD=e=>G(pM.getSlotNames(e),(t=>pM.isShowing(e,t))),wD=(e,t,o)=>{pM.getSlot(e,t).each((e=>{bD.getPane(e).each((t=>{var n;o(e)((n=t.element.dom,{getContainer:x(n)}))}))}))};var SD=fm({factory:(e,t)=>{const o={setViews:(e,o)=>{Kp.set(e,[yD(o,t.backstage.shared.providers)])},whichView:e=>wm.getCurrent(e).bind(xD),toggleView:(e,t,o,n)=>wm.getCurrent(e).exists((s=>{const r=xD(s),a=r.exists((e=>n===e)),i=pM.getSlot(s,n).isSome();return i&&(pM.hideAllSlots(s),a?((e=>{const t=e.element;Dt(t,"display","none"),kt(t,"aria-hidden","true")})(e),t()):(o(),(e=>{const t=e.element;Ht(t,"display"),Et(t,"aria-hidden")})(e),pM.showSlot(s,n),((e,t)=>{wD(e,t,bD.getOnShow)})(s,n)),r.each((e=>((e,t)=>wD(e,t,bD.getOnHide))(s,e)))),i}))};return{uid:e.uid,dom:{tag:"div",classes:["tox-view-wrap"],attributes:{"aria-hidden":"true"},styles:{display:"none"}},components:[],behaviours:kl([Kp.config({}),wm.config({find:e=>{const t=Kp.contents(e);return oe(t)}})]),apis:o}},name:"silver.ViewWrapper",configFields:[os("backstage")],apis:{setViews:(e,t,o)=>e.setViews(t,o),toggleView:(e,t,o,n,s)=>e.toggleView(t,o,n,s),whichView:(e,t)=>e.whichView(t)}});const kD=iA.optional({factory:lM,name:"menubar",schema:[os("backstage")]}),CD=iA.optional({factory:{sketch:e=>uA.sketch({uid:e.uid,dom:e.dom,listBehaviours:kl([Pp.config({mode:"acyclic",selector:".tox-toolbar"})]),makeItem:()=>iD({type:e.type,uid:la("multiple-toolbar-item"),cyclicKeying:!1,initGroups:[],providers:e.providers,onEscape:()=>(e.onEscape(),A.some(!0))}),setupItem:(e,t,o,n)=>{pA.setGroups(t,o)},shell:!0})},name:"multiple-toolbar",schema:[os("dom"),os("onEscape")]}),OD=iA.optional({factory:{sketch:e=>{const t=(e=>e.type===sb.sliding?aD:e.type===sb.floating?rD:iD)(e);return t({type:e.type,uid:e.uid,onEscape:()=>(e.onEscape(),A.some(!0)),onToggled:(t,o)=>e.onToolbarToggled(o),cyclicKeying:!1,initGroups:[],getSink:e.getSink,providers:e.providers,moreDrawerData:{lazyToolbar:e.lazyToolbar,lazyMoreButton:e.lazyMoreButton,lazyHeader:e.lazyHeader},attributes:e.attributes})}},name:"toolbar",schema:[os("dom"),os("onEscape"),os("getSink")]}),_D=iA.optional({factory:{sketch:e=>{const t=e.editor,o=e.sticky?tM:bA;return{uid:e.uid,dom:e.dom,components:e.components,behaviours:kl(o(t,e.sharedBackstage))}}},name:"header",schema:[os("dom")]}),TD=iA.optional({factory:{sketch:e=>({uid:e.uid,dom:e.dom,components:[{dom:{tag:"a",attributes:{href:"https://www.tiny.cloud/tinymce-self-hosted-premium-features/?utm_source=TinyMCE&utm_medium=SPAP&utm_campaign=SPAP&utm_id=editorreferral",rel:"noopener",target:"_blank","aria-hidden":"true"},classes:["tox-promotion-link"],innerHtml:"\u26a1\ufe0fUpgrade"}}]})},name:"promotion",schema:[os("dom")]}),ED=iA.optional({name:"socket",schema:[os("dom")]}),AD=iA.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-sidebar"],attributes:{role:"presentation"}},components:[{dom:{tag:"div",classes:["tox-sidebar__slider"]},components:[],behaviours:kl([ck.config({}),oh.config({}),Q_.config({dimension:{property:"width"},closedClass:"tox-sidebar--sliding-closed",openClass:"tox-sidebar--sliding-open",shrinkingClass:"tox-sidebar--sliding-shrinking",growingClass:"tox-sidebar--sliding-growing",onShrunk:e=>{wm.getCurrent(e).each(pM.hideAllSlots),Fr(e,SM)},onGrown:e=>{Fr(e,SM)},onStartGrow:e=>{Ir(e,wM,{width:Nt(e.element,"width").getOr("")})},onStartShrink:e=>{Ir(e,wM,{width:Jt(e.element)+"px"})}}),Kp.config({}),wm.config({find:e=>{const t=Kp.contents(e);return oe(t)}})])}],behaviours:kl([BO(0),Jp("sidebar-sliding-events",[Ur(wM,((e,t)=>{Dt(e.element,"width",t.event.width)})),Ur(SM,((e,t)=>{Ht(e.element,"width")}))])])})},name:"sidebar",schema:[os("dom")]}),MD=iA.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",attributes:{"aria-hidden":"true"},classes:["tox-throbber"],styles:{display:"none"}},behaviours:kl([Kp.config({}),OM.config({focus:!1}),wm.config({find:e=>oe(e.components())})]),components:[]})},name:"throbber",schema:[os("dom")]}),DD=iA.optional({factory:SD,name:"viewWrapper",schema:[os("backstage")]}),BD=iA.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-editor-container"]},components:e.components})},name:"editorContainer",schema:[]});var FD=bm({name:"OuterContainer",factory:(e,t,o)=>{let n=!1;const s={getSocket:t=>aA.getPart(t,e,"socket"),setSidebar:(t,o,n)=>{aA.getPart(t,e,"sidebar").each((e=>((e,t,o)=>{wm.getCurrent(e).each((n=>{Kp.set(n,[vM(t)]);const s=null==o?void 0:o.toLowerCase();r(s)&&ve(t,s)&&wm.getCurrent(n).each((t=>{pM.showSlot(t,s),Q_.immediateGrow(n),Ht(n.element,"width"),yM(e.element,"region")}))}))})(e,o,n)))},toggleSidebar:(t,o)=>{aA.getPart(t,e,"sidebar").each((e=>((e,t)=>{wm.getCurrent(e).each((o=>{wm.getCurrent(o).each((n=>{Q_.hasGrown(o)?pM.isShowing(n,t)?(Q_.shrink(o),yM(e.element,"presentation")):(pM.hideAllSlots(n),pM.showSlot(n,t),yM(e.element,"region")):(pM.hideAllSlots(n),pM.showSlot(n,t),Q_.grow(o),yM(e.element,"region"))}))}))})(e,o)))},whichSidebar:t=>aA.getPart(t,e,"sidebar").bind(xM).getOrNull(),getHeader:t=>aA.getPart(t,e,"header"),getToolbar:t=>aA.getPart(t,e,"toolbar"),setToolbar:(t,o)=>{aA.getPart(t,e,"toolbar").each((e=>{const t=H(o,oD);e.getApis().setGroups(e,t)}))},setToolbars:(t,o)=>{aA.getPart(t,e,"multiple-toolbar").each((e=>{const t=H(o,(e=>H(e,oD)));uA.setItems(e,t)}))},refreshToolbar:t=>{aA.getPart(t,e,"toolbar").each((e=>e.getApis().refresh(e)))},toggleToolbarDrawer:t=>{aA.getPart(t,e,"toolbar").each((e=>{ke(e.getApis().toggle,(t=>t(e)))}))},toggleToolbarDrawerWithoutFocusing:t=>{aA.getPart(t,e,"toolbar").each((e=>{ke(e.getApis().toggleWithoutFocusing,(t=>t(e)))}))},isToolbarDrawerToggled:t=>aA.getPart(t,e,"toolbar").bind((e=>A.from(e.getApis().isOpen).map((t=>t(e))))).getOr(!1),getThrobber:t=>aA.getPart(t,e,"throbber"),focusToolbar:t=>{aA.getPart(t,e,"toolbar").orThunk((()=>aA.getPart(t,e,"multiple-toolbar"))).each((e=>{Pp.focusIn(e)}))},setMenubar:(t,o)=>{aA.getPart(t,e,"menubar").each((e=>{lM.setMenus(e,o)}))},focusMenubar:t=>{aA.getPart(t,e,"menubar").each((e=>{lM.focus(e)}))},setViews:(t,o)=>{aA.getPart(t,e,"viewWrapper").each((e=>{SD.setViews(e,o)}))},toggleView:(t,o)=>aA.getPart(t,e,"viewWrapper").exists((e=>SD.toggleView(e,(()=>s.showMainView(t)),(()=>s.hideMainView(t)),o))),whichView:t=>aA.getPart(t,e,"viewWrapper").bind(SD.whichView).getOrNull(),hideMainView:t=>{n=s.isToolbarDrawerToggled(t),n&&s.toggleToolbarDrawer(t),aA.getPart(t,e,"editorContainer").each((e=>{const t=e.element;Dt(t,"display","none"),kt(t,"aria-hidden","true")}))},showMainView:t=>{n&&s.toggleToolbarDrawer(t),aA.getPart(t,e,"editorContainer").each((e=>{const t=e.element;Ht(t,"display"),Et(t,"aria-hidden")}))}};return{uid:e.uid,dom:e.dom,components:t,apis:s,behaviours:e.behaviours}},configFields:[os("dom"),os("behaviours")],partFields:[_D,kD,OD,CD,ED,AD,TD,MD,DD,BD],apis:{getSocket:(e,t)=>e.getSocket(t),setSidebar:(e,t,o,n)=>{e.setSidebar(t,o,n)},toggleSidebar:(e,t,o)=>{e.toggleSidebar(t,o)},whichSidebar:(e,t)=>e.whichSidebar(t),getHeader:(e,t)=>e.getHeader(t),getToolbar:(e,t)=>e.getToolbar(t),setToolbar:(e,t,o)=>{e.setToolbar(t,o)},setToolbars:(e,t,o)=>{e.setToolbars(t,o)},refreshToolbar:(e,t)=>e.refreshToolbar(t),toggleToolbarDrawer:(e,t)=>{e.toggleToolbarDrawer(t)},toggleToolbarDrawerWithoutFocusing:(e,t)=>{e.toggleToolbarDrawerWithoutFocusing(t)},isToolbarDrawerToggled:(e,t)=>e.isToolbarDrawerToggled(t),getThrobber:(e,t)=>e.getThrobber(t),setMenubar:(e,t,o)=>{e.setMenubar(t,o)},focusMenubar:(e,t)=>{e.focusMenubar(t)},focusToolbar:(e,t)=>{e.focusToolbar(t)},setViews:(e,t,o)=>{e.setViews(t,o)},toggleView:(e,t,o)=>e.toggleView(t,o),whichView:(e,t)=>e.whichView(t)}});const ID={file:{title:"File",items:"newdocument restoredraft | preview | export print | deleteallconversations"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall | searchreplace"},view:{title:"View",items:"code | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments"},insert:{title:"Insert",items:"image link media addcomment pageembed template inserttemplate codesample inserttable accordion | charmap emoticons hr | pagebreak nonbreaking anchor tableofcontents footnotes | mergetags | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | styles blocks fontfamily fontsize align lineheight | forecolor backcolor | language | removeformat"},tools:{title:"Tools",items:"aidialog aishortcuts | spellchecker spellcheckerlanguage | autocorrect capitalization | a11ycheck code typography wordcount addtemplate"},table:{title:"Table",items:"inserttable | cell row column | advtablesort | tableprops deletetable"},help:{title:"Help",items:"help"}},RD=e=>e.split(" "),ND=(e,t)=>{const o={...ID,...t.menus},n=ae(t.menus).length>0,s=void 0===t.menubar||!0===t.menubar?RD("file edit view insert format tools table help"):RD(!1===t.menubar?"":t.menubar),a=U(s,(e=>{const o=ve(ID,e);return n?o||be(t.menus,e).exists((e=>ve(e,"items"))):o})),i=H(a,(n=>{const s=o[n];return((e,t,o)=>{const n=kb(o).split(/[ ,]/);return{text:e.title,getItems:()=>X(e.items,(e=>{const o=e.toLowerCase();return 0===o.trim().length||N(n,(e=>e===o))?[]:"separator"===o||"|"===o?[{type:"separator"}]:t.menuItems[o]?[t.menuItems[o]]:[]}))}})({title:s.title,items:RD(s.items)},t,e)}));return U(i,(e=>e.getItems().length>0&&N(e.getItems(),(e=>r(e)||"separator"!==e.type))))},VD=e=>{const t=()=>{e._skinLoaded=!0,(e=>{e.dispatch("SkinLoaded")})(e)};return()=>{e.initialized?t():e.on("init",t)}},zD=(e,t,o)=>(e.on("remove",(()=>o.unload(t))),o.load(t)),HD=(e,t)=>zD(e,t+"/skin.min.css",e.ui.styleSheetLoader),LD=(e,t)=>{var o;return o=Ve(e.getElement()),bt(o).isSome()?zD(e,t+"/skin.shadowdom.min.css",ab.DOM.styleSheetLoader):Promise.resolve()},PD=(e,t)=>{const o=Jb(t);return o&&t.contentCSS.push(o+(e?"/content.inline":"/content")+".min.css"),!Yb(t)&&r(o)?Promise.all([HD(t,o),LD(t,o)]).then(VD(t),((e,t)=>()=>((e,t)=>{e.dispatch("SkinLoadError",t)})(e,{message:"Skin could not be loaded"}))(t)):Promise.resolve(VD(t)())},UD=k(PD,!1),WD=k(PD,!0),jD=(e,t,o)=>{const n=(e,n,r,a)=>{const i=t.shared.providers.translate(e.title);if("separator"===e.type)return A.some({type:"separator",text:i});if("submenu"===e.type){const t=X(e.getStyleItems(),(e=>s(e,n,a)));return 0===n&&t.length<=0?A.none():A.some({type:"nestedmenuitem",text:i,enabled:t.length>0,getSubmenuItems:()=>X(e.getStyleItems(),(e=>s(e,n,a)))})}return A.some({type:"togglemenuitem",text:i,icon:e.icon,active:e.isSelected(a),enabled:!r,onAction:o.onAction(e),...e.getStylePreview().fold((()=>({})),(e=>({meta:{style:e}})))})},s=(e,t,s)=>{const r="formatter"===e.type&&o.isInvalid(e);return 0===t?r?[]:n(e,t,!1,s).toArray():n(e,t,r,s).toArray()},r=e=>{const t=o.getCurrentValue(),n=o.shouldHide?0:1;return X(e,(e=>s(e,n,t)))};return{validateItems:r,getFetch:(e,t)=>(o,n)=>{const s=t(),a=r(s);n(C_(a,pv.CLOSE_ON_EXECUTE,e,{isHorizontalMenu:!1,search:A.none()}))}}},GD=(e,t,o)=>{const n=o.dataset,s="basic"===n.type?()=>H(n.data,(e=>FE(e,o.isSelectedFor,o.getPreviewFor))):n.getData;return{items:jD(0,t,o),getStyleItems:s}},$D=(e,t,o)=>{const{items:n,getStyleItems:s}=GD(0,t,o),r=yw(e,"NodeChange",(t=>{const n=t.getComponent();o.updateText(n),Rm.set(t.getComponent(),!e.selection.isEditable())}));return x_({text:o.icon.isSome()?A.none():o.text,icon:o.icon,tooltip:A.from(o.tooltip),role:A.none(),fetch:n.getFetch(t,s),onSetup:r,getApi:e=>({getComponent:x(e)}),columns:1,presets:"normal",classes:o.icon.isSome()?[]:["bespoke"],dropdownBehaviours:[]},"tox-tbtn",t.shared)};var qD;!function(e){e[e.SemiColon=0]="SemiColon",e[e.Space=1]="Space"}(qD||(qD={}));const XD=(e,t,o)=>{const n=(s=((e,t)=>t===qD.SemiColon?e.replace(/;$/,"").split(";"):e.split(" "))(e.options.get(t),o),H(s,(e=>{let t=e,o=e;const n=e.split("=");return n.length>1&&(t=n[0],o=n[1]),{title:t,format:o}})));var s;return{type:"basic",data:n}},YD=[{title:"Left",icon:"align-left",format:"alignleft",command:"JustifyLeft"},{title:"Center",icon:"align-center",format:"aligncenter",command:"JustifyCenter"},{title:"Right",icon:"align-right",format:"alignright",command:"JustifyRight"},{title:"Justify",icon:"align-justify",format:"alignjustify",command:"JustifyFull"}],KD=e=>{const t={type:"basic",data:YD};return{tooltip:"Align",text:A.none(),icon:A.some("align-left"),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:e=>A.none,onAction:t=>()=>G(YD,(e=>e.format===t.format)).each((t=>e.execCommand(t.command))),updateText:t=>{const o=G(YD,(t=>e.formatter.match(t.format))).fold(x("left"),(e=>e.title.toLowerCase()));Ir(t,y_,{icon:`align-${o}`})},dataset:t,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},JD=(e,t)=>{const o=t(),n=H(o,(e=>e.format));return A.from(e.formatter.closest(n)).bind((e=>G(o,(t=>t.format===e)))).orThunk((()=>Ce(e.formatter.match("p"),{title:"Paragraph",format:"p"})))},ZD=e=>{const t="Paragraph",o=XD(e,"block_formats",qD.SemiColon);return{tooltip:"Blocks",text:A.some(t),icon:A.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},onAction:xw(e),updateText:n=>{const s=JD(e,(()=>o.data)).fold(x(t),(e=>e.title));Ir(n,v_,{text:s})},dataset:o,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},QD=["-apple-system","Segoe UI","Roboto","Helvetica Neue","sans-serif"],eB=e=>{const t=e.split(/\s*,\s*/);return H(t,(e=>e.replace(/^['"]+|['"]+$/g,"")))},tB=e=>{const t="System Font",o=()=>{const o=e=>e?eB(e)[0]:"",s=e.queryCommandValue("FontName"),r=n.data,a=s?s.toLowerCase():"",i=G(r,(e=>{const t=e.format;return t.toLowerCase()===a||o(t).toLowerCase()===o(a).toLowerCase()})).orThunk((()=>Ce((e=>0===e.indexOf("-apple-system")&&(()=>{const t=eB(e.toLowerCase());return Y(QD,(e=>t.indexOf(e.toLowerCase())>-1))})())(a),{title:t,format:a})));return{matchOpt:i,font:s}},n=XD(e,"font_family_formats",qD.SemiColon);return{tooltip:"Fonts",text:A.some(t),icon:A.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getCurrentValue:()=>{const{matchOpt:e}=o();return e},getPreviewFor:e=>()=>A.some({tag:"div",styles:-1===e.indexOf("dings")?{"font-family":e}:{}}),onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontName",!1,t.format)}))},updateText:e=>{const{matchOpt:t,font:n}=o(),s=t.fold(x(n),(e=>e.title));Ir(e,v_,{text:s})},dataset:n,shouldHide:!1,isInvalid:T}},oB={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},nB=(()=>{const e="[0-9]+",t="[eE][+-]?"+e,o=e=>`(?:${e})?`,n=["Infinity",e+"\\."+o(e)+o(t),"\\."+e+o(t),e+o(t)].join("|");return new RegExp(`^([+-]?(?:${n}))(.*)$`)})(),sB=(e,t)=>A.from(nB.exec(e)).bind((e=>{const o=Number(e[1]),n=e[2];return((e,t)=>N(t,(t=>N(oB[t],(t=>e===t)))))(n,t)?A.some({value:o,unit:n}):A.none()})),rB={tab:x(9),escape:x(27),enter:x(13),backspace:x(8),delete:x(46),left:x(37),up:x(38),right:x(39),down:x(40),space:x(32),home:x(36),end:x(35),pageUp:x(33),pageDown:x(34)},aB={"8pt":"1","10pt":"2","12pt":"3","14pt":"4","18pt":"5","24pt":"6","36pt":"7"},iB={"xx-small":"7pt","x-small":"8pt",small:"10pt",medium:"12pt",large:"14pt","x-large":"18pt","xx-large":"24pt"},lB=(e,t)=>/[0-9.]+px$/.test(e)?((e,t)=>{const o=Math.pow(10,t);return Math.round(e*o)/o})(72*parseInt(e,10)/96,t||0)+"pt":be(iB,e).getOr(e),cB=e=>be(aB,e).getOr(""),dB=e=>{const t=()=>{let t=A.none();const o=n.data,s=e.queryCommandValue("FontSize");if(s)for(let e=3;t.isNone()&&e>=0;e--){const n=lB(s,e),r=cB(n);t=G(o,(e=>e.format===s||e.format===n||e.format===r))}return{matchOpt:t,size:s}},o=x(A.none),n=XD(e,"font_size_formats",qD.Space);return{tooltip:"Font sizes",text:A.some("12pt"),icon:A.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getPreviewFor:o,getCurrentValue:()=>{const{matchOpt:e}=t();return e},onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontSize",!1,t.format)}))},updateText:e=>{const{matchOpt:o,size:n}=t(),s=o.fold(x(n),(e=>e.title));Ir(e,v_,{text:s})},dataset:n,shouldHide:!1,isInvalid:T}},uB=(e,t)=>{const o="Paragraph";return{tooltip:"Formats",text:A.some(o),icon:A.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return void 0!==o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},onAction:xw(e),updateText:t=>{const n=e=>EE(e)?X(e.items,n):AE(e)?[{title:e.title,format:e.format}]:[],s=X(BE(e),n),r=JD(e,x(s)).fold(x(o),(e=>e.title));Ir(t,v_,{text:r})},shouldHide:wb(e),isInvalid:t=>!e.formatter.canApply(t.format),dataset:t}},mB=x([os("toggleClass"),os("fetch"),Fi("onExecute"),ys("getHotspot",A.some),ys("getAnchorOverrides",x({})),wc(),Fi("onItemExecute"),us("lazySink"),os("dom"),Di("onOpen"),hu("splitDropdownBehaviours",[uS,Pp,oh]),ys("matchWidth",!1),ys("useMinWidth",!1),ys("eventOrder",{}),us("role")].concat(ES())),gB=Uu({factory:Wh,schema:[os("dom")],name:"arrow",defaults:()=>({buttonBehaviours:kl([oh.revoke()])}),overrides:e=>({dom:{tag:"span",attributes:{role:"presentation"}},action:t=>{t.getSystem().getByUid(e.uid).each(Rr)},buttonBehaviours:kl([dh.config({toggleOnExecute:!1,toggleClass:e.toggleClass})])})}),pB=Uu({factory:Wh,schema:[os("dom")],name:"button",defaults:()=>({buttonBehaviours:kl([oh.revoke()])}),overrides:e=>({dom:{tag:"span",attributes:{role:"presentation"}},action:t=>{t.getSystem().getByUid(e.uid).each((o=>{e.onExecute(o,t)}))}})}),hB=x([gB,pB,ju({factory:{sketch:e=>({uid:e.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:e.text}})},schema:[os("text")],name:"aria-descriptor"}),Wu({schema:[Ei()],name:"menu",defaults:e=>({onExecute:(t,o)=>{t.getSystem().getByUid(e.uid).each((n=>{e.onItemExecute(n,t,o)}))}})}),yS()]),fB=bm({name:"SplitDropdown",configFields:mB(),partFields:hB(),factory:(e,t,o,n)=>{const s=e=>{wm.getCurrent(e).each((e=>{Gm.highlightFirst(e),Pp.focusIn(e)}))},r=t=>{kS(e,w,t,n,s,zh.HighlightMenuAndItem).get(b)},a=t=>{const o=nm(t,e,"button");return Rr(o),A.some(!0)},i={...Hr([Kr(((t,o)=>{om(t,e,"aria-descriptor").each((e=>{const o=la("aria");kt(e.element,"id",o),kt(t.element,"aria-describedby",o)}))}))]),...mh(A.some(r))},l={repositionMenus:e=>{dh.isOn(e)&&TS(e)}};return{uid:e.uid,dom:e.dom,components:t,apis:l,eventOrder:{...e.eventOrder,[ur()]:["disabling","toggling","alloy.base.behaviour"]},events:i,behaviours:bu(e.splitDropdownBehaviours,[uS.config({others:{sandbox:t=>{const o=nm(t,e,"arrow");return _S(e,t,{onOpen:()=>{dh.on(o),dh.on(t)},onClose:()=>{dh.off(o),dh.off(t)}})}}}),Pp.config({mode:"special",onSpace:a,onEnter:a,onDown:e=>(r(e),A.some(!0))}),oh.config({}),dh.config({toggleOnExecute:!1,aria:{mode:"expanded"}})]),domModification:{attributes:{role:e.role.getOr("button"),"aria-haspopup":!0}}}},apis:{repositionMenus:(e,t)=>e.repositionMenus(t)}}),bB=e=>({isEnabled:()=>!Rm.isDisabled(e),setEnabled:t=>Rm.set(e,!t),setText:t=>Ir(e,v_,{text:t}),setIcon:t=>Ir(e,y_,{icon:t})}),vB=e=>({setActive:t=>{dh.set(e,t)},isActive:()=>dh.isOn(e),isEnabled:()=>!Rm.isDisabled(e),setEnabled:t=>Rm.set(e,!t),setText:t=>Ir(e,v_,{text:t}),setIcon:t=>Ir(e,y_,{icon:t})}),yB=(e,t)=>e.map((e=>({"aria-label":t.translate(e),title:t.translate(e)}))).getOr({}),xB=la("focus-button"),wB=(e,t,o,n,s)=>{const r=t.map((e=>Gh(b_(e,"tox-tbtn",s)))),a=e.map((e=>Gh(f_(e,s.icons))));return{dom:{tag:"button",classes:["tox-tbtn"].concat(t.isSome()?["tox-tbtn--select"]:[]),attributes:yB(o,s)},components:Dx([a.map((e=>e.asSpec())),r.map((e=>e.asSpec()))]),eventOrder:{[Ws()]:["focusing","alloy.base.behaviour",u_],[Sr()]:[u_,"toolbar-group-button-events"]},buttonBehaviours:kl([Ox(s.isDisabled),Sx(),Jp(u_,[Kr(((e,t)=>g_(e))),Ur(v_,((e,t)=>{r.bind((t=>t.getOpt(e))).each((e=>{Kp.set(e,[ti(s.translate(t.event.text))])}))})),Ur(y_,((e,t)=>{a.bind((t=>t.getOpt(e))).each((e=>{Kp.set(e,[f_(t.event.icon,s.icons)])}))})),Ur(Ws(),((e,t)=>{t.event.prevent(),Fr(e,xB)}))])].concat(n.getOr([])))}},SB=(e,t,o)=>{var n;const s=Es(b),r=wB(e.icon,e.text,e.tooltip,A.none(),o);return Wh.sketch({dom:r.dom,components:r.components,eventOrder:m_,buttonBehaviours:{...kl([Jp("toolbar-button-events",[(a={onAction:e.onAction,getApi:t.getApi},Qr(((e,t)=>{_x(a,e)((t=>{Ir(e,d_,{buttonApi:t}),a.onAction(t)}))}))),Tx(t,s),Ex(t,s)]),Ox((()=>!e.enabled||o.isDisabled())),Sx()].concat(t.toolbarButtonBehaviours)),[u_]:null===(n=r.buttonBehaviours)||void 0===n?void 0:n[u_]}});var a},kB=(e,t,o)=>SB(e,{toolbarButtonBehaviours:o.length>0?[Jp("toolbarButtonWith",o)]:[],getApi:bB,onSetup:e.onSetup},t),CB=(e,t,o)=>SB(e,{toolbarButtonBehaviours:[Kp.config({}),dh.config({toggleClass:"tox-tbtn--enabled",aria:{mode:"pressed"},toggleOnExecute:!1})].concat(o.length>0?[Jp("toolbarToggleButtonWith",o)]:[]),getApi:vB,onSetup:e.onSetup},t),OB=(e,t,o)=>n=>fS((e=>t.fetch(e))).map((s=>A.from(HS(fn(Zw(la("menu-value"),s,(o=>{t.onItemAction(e(n),o)}),t.columns,t.presets,pv.CLOSE_ON_EXECUTE,t.select.getOr(T),o),{movement:eS(t.columns,t.presets),menuBehaviours:lx("auto"!==t.columns?[]:[Kr(((e,o)=>{ix(e,4,_v(t.presets)).each((({numRows:t,numColumns:o})=>{Pp.setGridSize(e,t,o)}))}))])}))))),_B=[{name:"history",items:["undo","redo"]},{name:"ai",items:["aidialog","aishortcuts"]},{name:"styles",items:["styles"]},{name:"formatting",items:["bold","italic"]},{name:"alignment",items:["alignleft","aligncenter","alignright","alignjustify"]},{name:"indentation",items:["outdent","indent"]},{name:"permanent pen",items:["permanentpen"]},{name:"comments",items:["addcomment"]}],TB=(e,t)=>(o,n,s)=>{const r=e(o).mapError((e=>Kn(e))).getOrDie();return t(r,n,s)},EB={button:TB(Fy,((e,t)=>{return o=e,n=t.shared.providers,kB(o,n,[]);var o,n})),togglebutton:TB(Ny,((e,t)=>{return o=e,n=t.shared.providers,CB(o,n,[]);var o,n})),menubutton:TB(aM,((e,t)=>tT(e,"tox-tbtn",t,A.none(),!1))),splitbutton:TB((e=>qn("SplitButton",iM,e)),((e,t)=>((e,t)=>{const o=e=>({isEnabled:()=>!Rm.isDisabled(e),setEnabled:t=>Rm.set(e,!t),setIconFill:(t,o)=>{pi(e.element,`svg path[class="${t}"], rect[class="${t}"]`).each((e=>{kt(e,"fill",o)}))},setActive:t=>{kt(e.element,"aria-pressed",t),pi(e.element,"span").each((o=>{e.getSystem().getByDom(o).each((e=>dh.set(e,t)))}))},isActive:()=>pi(e.element,"span").exists((t=>e.getSystem().getByDom(t).exists(dh.isOn))),setText:t=>pi(e.element,"span").each((o=>e.getSystem().getByDom(o).each((e=>Ir(e,v_,{text:t}))))),setIcon:t=>pi(e.element,"span").each((o=>e.getSystem().getByDom(o).each((e=>Ir(e,y_,{icon:t})))))}),n=Es(b),s={getApi:o,onSetup:e.onSetup};return fB.sketch({dom:{tag:"div",classes:["tox-split-button"],attributes:{"aria-pressed":!1,...yB(e.tooltip,t.providers)}},onExecute:t=>{const n=o(t);n.isEnabled()&&e.onAction(n)},onItemExecute:(e,t,o)=>{},splitDropdownBehaviours:kl([Cx(t.providers.isDisabled),Sx(),Jp("split-dropdown-events",[Kr(((e,t)=>g_(e))),Ur(xB,oh.focus),Tx(s,n),Ex(s,n)]),Ik.config({})]),eventOrder:{[Sr()]:["alloy.base.behaviour","split-dropdown-events"]},toggleClass:"tox-tbtn--enabled",lazySink:t.getSink,fetch:OB(o,e,t.providers),parts:{menu:Bv(0,e.columns,e.presets)},components:[fB.parts.button(wB(e.icon,e.text,A.none(),A.some([dh.config({toggleClass:"tox-tbtn--enabled",toggleOnExecute:!1})]),t.providers)),fB.parts.arrow({dom:{tag:"button",classes:["tox-tbtn","tox-split-button__chevron"],innerHtml:Zf("chevron-down",t.providers.icons)},buttonBehaviours:kl([Cx(t.providers.isDisabled),Sx(),Qf()])}),fB.parts["aria-descriptor"]({text:t.providers.translate("To open the popup, press Shift+Enter")})]})})(e,t.shared))),grouptoolbarbutton:TB((e=>qn("GroupToolbarButton",nM,e)),((e,t,o)=>{const n=o.ui.registry.getAll().buttons,s={[yc]:t.shared.header.isPositionedAtTop()?vc.TopToBottom:vc.BottomToTop};if(Cb(o)===sb.floating)return((e,t,o,n)=>{const s=t.shared,r=Es(b),a={toolbarButtonBehaviours:[],getApi:bB,onSetup:e.onSetup},i=[Jp("toolbar-group-button-events",[Tx(a,r),Ex(a,r)])];return WM.sketch({lazySink:s.getSink,fetch:()=>fS((t=>{t(H(o(e.items),oD))})),markers:{toggledClass:"tox-tbtn--enabled"},parts:{button:wB(e.icon,e.text,e.tooltip,A.some(i),s.providers),toolbar:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:n}}}})})(e,t,(e=>MB(o,{buttons:n,toolbar:e,allowToolbarGroups:!1},t,A.none())),s);throw new Error("Toolbar groups are only supported when using floating toolbar mode")}))},AB={styles:(e,t)=>{const o={type:"advanced",...t.styles};return $D(e,t,uB(e,o))},fontsize:(e,t)=>$D(e,t,dB(e)),fontsizeinput:(e,t)=>((e,t,o)=>{let n=A.none();const s=yw(e,"NodeChange SwitchMode",(t=>{const s=t.getComponent();n=A.some(s),o.updateInputValue(s),Rm.set(s,!e.selection.isEditable())})),r=e=>({getComponent:x(e)}),a=Es(b),i=la("custom-number-input-events"),l=(e,t,s)=>{const r=n.map((e=>pu.getValue(e))).getOr(""),a=o.getNewValue(r,e),i=r.length-`${a}`.length,l=n.map((e=>e.element.dom.selectionStart-i)),c=n.map((e=>e.element.dom.selectionEnd-i));o.onAction(a,s),n.each((e=>{pu.setValue(e,a),t&&(l.each((t=>e.element.dom.selectionStart=t)),c.each((t=>e.element.dom.selectionEnd=t)))}))},c=(e,t)=>l(((e,t)=>e-t),e,t),d=(e,t)=>l(((e,t)=>e+t),e,t),u=e=>rt(e.element).fold(A.none,(e=>(Dl(e),A.some(!0)))),m=e=>Fl(e.element)?(ct(e.element).each((e=>Dl(e))),A.some(!0)):A.none(),g=(o,n,s,i)=>{const l=t.shared.providers.translate(s),c=la("altExecuting"),d=yw(e,"NodeChange SwitchMode",(t=>{Rm.set(t.getComponent(),!e.selection.isEditable())})),u=e=>{Rm.isDisabled(e)||o(!0)};return Wh.sketch({dom:{tag:"button",attributes:{title:l,"aria-label":l},classes:i.concat(n)},components:[h_(n,t.shared.providers.icons)],buttonBehaviours:kl([Rm.config({}),Jp(c,[Tx({onSetup:d,getApi:r},a),Ex({getApi:r},a),Ur(Ks(),((e,t)=>{t.event.raw.keyCode!==rB.space()&&t.event.raw.keyCode!==rB.enter()||Rm.isDisabled(e)||o(!1)})),Ur(er(),u),Ur(Ps(),u)])]),eventOrder:{[Ks()]:[c,"keying"],[er()]:[c,"alloy.base.behaviour"],[Ps()]:[c,"alloy.base.behaviour"]}})},p=Gh(g((e=>c(!1,e)),"minus","Decrease font size",["highlight-on-focus"])),h=Gh(g((e=>d(!1,e)),"plus","Increase font size",["highlight-on-focus"])),f=Gh({dom:{tag:"div",classes:["tox-input-wrapper","highlight-on-focus"]},components:[Vv.sketch({inputBehaviours:kl([Rm.config({}),Jp(i,[Tx({onSetup:s,getApi:r},a),Ex({getApi:r},a)]),Jp("input-update-display-text",[Ur(v_,((e,t)=>{pu.setValue(e,t.event.text)})),Ur(Ys(),(e=>{o.onAction(pu.getValue(e))})),Ur(Qs(),(e=>{o.onAction(pu.getValue(e))}))]),Pp.config({mode:"special",onEnter:e=>(l(w,!0,!0),A.some(!0)),onEscape:u,onUp:e=>(d(!0,!1),A.some(!0)),onDown:e=>(c(!0,!1),A.some(!0)),onLeft:(e,t)=>(t.cut(),A.none()),onRight:(e,t)=>(t.cut(),A.none())})])})],behaviours:kl([oh.config({}),Pp.config({mode:"special",onEnter:m,onSpace:m,onEscape:u}),Jp("input-wrapper-events",[Ur(qs(),(e=>{L([p,h],(t=>{const o=Ve(t.get(e).element.dom);Fl(o)&&Bl(o)}))}))])])});return{dom:{tag:"div",classes:["tox-number-input"]},components:[p.asSpec(),f.asSpec(),h.asSpec()],behaviours:kl([oh.config({}),Pp.config({mode:"flow",focusInside:pg.OnEnterOrSpaceMode,cycles:!1,selector:"button, .tox-input-wrapper",onEscape:e=>Fl(e.element)?A.none():(Dl(e.element),A.some(!0))})])}})(e,t,(e=>{const t=()=>e.queryCommandValue("FontSize");return{updateInputValue:e=>Ir(e,v_,{text:t()}),onAction:(t,o)=>e.execCommand("FontSize",!1,t,{skip_focus:!o}),getNewValue:(o,n)=>{sB(o,["unsupportedLength","empty"]);const s=sB(o,["unsupportedLength","empty"]).or(sB(t(),["unsupportedLength","empty"])),r=s.map((e=>e.value)).getOr(16),a=Rb(e),i=s.map((e=>e.unit)).filter((e=>""!==e)).getOr(a),l=n(r,(e=>{var t;return null!==(t={em:{step:.1},cm:{step:.1},in:{step:.1},pc:{step:.1},ch:{step:.1},rem:{step:.1}}[e])&&void 0!==t?t:{step:1}})(i).step);return`${(e=>e>=0)(l)?l:r}${i}`}}})(e)),fontfamily:(e,t)=>$D(e,t,tB(e)),blocks:(e,t)=>$D(e,t,ZD(e)),align:(e,t)=>$D(e,t,KD(e))},MB=(e,t,o,n)=>{const s=(e=>{const t=e.toolbar,o=e.buttons;return!1===t?[]:void 0===t||!0===t?(e=>{const t=H(_B,(t=>{const o=U(t.items,(t=>ve(e,t)||ve(AB,t)));return{name:t.name,items:o}}));return U(t,(e=>e.items.length>0))})(o):r(t)?(e=>{const t=e.split("|");return H(t,(e=>({items:e.trim().split(" ")})))})(t):(e=>f(e,(e=>ve(e,"name")&&ve(e,"items"))))(t)?t:(console.error("Toolbar type should be string, string[], boolean or ToolbarGroup[]"),[])})(t),a=H(s,(s=>{const r=X(s.items,(s=>0===s.trim().length?[]:((e,t,o,n,s,r)=>be(t,o.toLowerCase()).orThunk((()=>r.bind((e=>re(e,(e=>be(t,e+o.toLowerCase()))))))).fold((()=>be(AB,o.toLowerCase()).map((t=>t(e,s)))),(t=>"grouptoolbarbutton"!==t.type||n?((e,t,o)=>be(EB,e.type).fold((()=>(console.error("skipping button defined by",e),A.none())),(n=>A.some(n(e,t,o)))))(t,s,e):(console.warn(`Ignoring the '${o}' toolbar button. Group toolbar buttons are only supported when using floating toolbar mode and cannot be nested.`),A.none()))))(e,t.buttons,s,t.allowToolbarGroups,o,n).toArray()));return{title:A.from(e.translate(s.name)),items:r}}));return U(a,(e=>e.items.length>0))},DB=(e,t,o,n)=>{const s=t.mainUi.outerContainer,a=o.toolbar,i=o.buttons;if(f(a,r)){const t=a.map((t=>{const s={toolbar:t,buttons:i,allowToolbarGroups:o.allowToolbarGroups};return MB(e,s,n,A.none())}));FD.setToolbars(s,t)}else FD.setToolbar(s,MB(e,o,n,A.none()))},BB=Do(),FB=BB.os.isiOS()&&BB.os.version.major<=12;var IB=Object.freeze({__proto__:null,render:(e,t,o,n,s)=>{const{mainUi:r,uiMotherships:a}=t,i=Es(0),l=r.outerContainer;e.on("SkinLoaded",(()=>{DB(e,t,o,n)})),UD(e);const d=Ve(s.targetNode),u=ft(ht(d));Rd(d,r.mothership),((e,t,o)=>{lv(e)&&Rd(o.mainUi.mothership.element,o.popupUi.mothership),Id(t,o.dialogUi.mothership)})(e,u,t),e.on("PostRender",(()=>{FD.setSidebar(l,o.sidebar,$b(e)),DB(e,t,o,n),i.set(e.getWin().innerWidth),FD.setMenubar(l,ND(e,o)),FD.setViews(l,o.views),((e,t)=>{const{uiMotherships:o}=t,n=e.dom;let s=e.getWin();const r=e.getDoc().documentElement,a=Es($t(s.innerWidth,s.innerHeight)),i=Es($t(r.offsetWidth,r.offsetHeight)),l=()=>{const t=a.get();t.left===s.innerWidth&&t.top===s.innerHeight||(a.set($t(s.innerWidth,s.innerHeight)),gw(e))},c=()=>{const t=e.getDoc().documentElement,o=i.get();o.left===t.offsetWidth&&o.top===t.offsetHeight||(i.set($t(t.offsetWidth,t.offsetHeight)),gw(e))},d=t=>{((e,t)=>{e.dispatch("ScrollContent",t)})(e,t)};n.bind(s,"resize",l),n.bind(s,"scroll",d);const u=oc(Ve(e.getBody()),"load",c);e.on("hide",(()=>{L(o,(e=>{Dt(e.element,"display","none")}))})),e.on("show",(()=>{L(o,(e=>{Ht(e.element,"display")}))})),e.on("NodeChange",c),e.on("remove",(()=>{u.unbind(),n.unbind(s,"resize",l),n.unbind(s,"scroll",d),s=null}))})(e,t)}));const m=FD.getSocket(l).getOrDie("Could not find expected socket element");if(FB){Bt(m.element,{overflow:"scroll","-webkit-overflow-scrolling":"touch"});const t=((e,t)=>{let o=null;return{cancel:()=>{c(o)||(clearTimeout(o),o=null)},throttle:(...t)=>{c(o)&&(o=setTimeout((()=>{o=null,e.apply(null,t)}),20))}}})((()=>{e.dispatch("ScrollContent")})),o=tc(m.element,"scroll",t.throttle);e.on("remove",o.unbind)}wx(e,t),e.addCommand("ToggleSidebar",((t,o)=>{FD.toggleSidebar(l,o),e.dispatch("ToggleSidebar")})),e.addQueryValueHandler("ToggleSidebar",(()=>{var e;return null!==(e=FD.whichSidebar(l))&&void 0!==e?e:""})),e.addCommand("ToggleView",((t,o)=>{if(FD.toggleView(l,o)){const t=l.element;r.mothership.broadcastOn([Yd()],{target:t}),L(a,(e=>{e.broadcastOn([Yd()],{target:t})})),c(FD.whichView(l))&&(e.focus(),e.nodeChanged(),FD.refreshToolbar(l))}})),e.addQueryValueHandler("ToggleView",(()=>{var e;return null!==(e=FD.whichView(l))&&void 0!==e?e:""}));const g=Cb(e);g!==sb.sliding&&g!==sb.floating||e.on("ResizeWindow ResizeEditor ResizeContent",(()=>{const o=e.getWin().innerWidth;o!==i.get()&&(FD.refreshToolbar(t.mainUi.outerContainer),i.set(o))}));const p={setEnabled:e=>{xx(t,!e)},isEnabled:()=>!Rm.isDisabled(l)};return{iframeContainer:m.element.dom,editorContainer:l.element.dom,api:p}}});const RB=e=>/^[0-9\.]+(|px)$/i.test(""+e)?A.some(parseInt(""+e,10)):A.none(),NB=e=>h(e)?e+"px":e,VB=(e,t,o)=>{const n=t.filter((t=>e<t)),s=o.filter((t=>e>t));return n.or(s).getOr(e)},zB=e=>{const t=pb(e),o=hb(e),n=bb(e);return RB(t).map((e=>VB(e,o,n)))},{ToolbarLocation:HB,ToolbarMode:LB}=dv,PB=(e,t,o,n,s)=>{const{mainUi:r,uiMotherships:a}=o,i=ab.DOM,l=sv(e),c=iv(e),d=bb(e).or(zB(e)),u=n.shared.header,m=u.isPositionedAtTop,g=Cb(e),p=g===LB.sliding||g===LB.floating,h=Es(!1),f=()=>h.get()&&!e.removed,b=e=>p?e.fold(x(0),(e=>e.components().length>1?Wt(e.components()[1].element):0)):0,v=()=>{L(a,(e=>{e.broadcastOn([Kd()],{})}))},y=o=>{if(!f())return;l||s.on((e=>{const o=d.getOrThunk((()=>{const e=RB(It(xt(),"margin-left")).getOr(0);return Jt(xt())-Xt(t).left+e}));Dt(e.element,"max-width",o+"px")}));const n=l?A.none():(()=>{if(l)return A.none();if(Xt(r.outerContainer.element).left+Zt(r.outerContainer.element)>=window.innerWidth-40||Nt(r.outerContainer.element,"width").isSome()){Dt(r.outerContainer.element,"position","absolute"),Dt(r.outerContainer.element,"left","0px"),Ht(r.outerContainer.element,"width");const e=Zt(r.outerContainer.element);return A.some(e)}return A.none()})();p&&FD.refreshToolbar(r.outerContainer),l||(o=>{s.on((n=>{const s=FD.getToolbar(r.outerContainer),a=b(s),i=Jo(t),{top:l,left:c}=((e,t)=>lv(e)?xA(t):A.none())(e,r.outerContainer.element).fold((()=>({top:m()?Math.max(i.y-Wt(n.element)+a,0):i.bottom,left:i.x})),(e=>{var t;const o=Jo(e),s=null!==(t=e.dom.scrollTop)&&void 0!==t?t:0,r=Ze(e,xt()),l=r?Math.max(i.y-Wt(n.element)+a,0):i.y-o.y+s-Wt(n.element)+a;return{top:m()?l:i.bottom,left:r?i.x:i.x-o.x}})),d={position:"absolute",left:Math.round(c)+"px",top:Math.round(l)+"px"},u=o.map((e=>{const t=Uo(),o=window.innerWidth-(c-t.left);return{width:Math.max(Math.min(e,o),150)+"px"}})).getOr({});Bt(r.outerContainer.element,{...d,...u})}))})(n),c&&s.on(o),v()},w=()=>!(l||!c||!f())&&s.get().exists((o=>{const n=u.getDockingMode(),a=(o=>{switch(_b(e)){case HB.auto:const e=FD.getToolbar(r.outerContainer),n=b(e),s=Wt(o.element)-n,a=Jo(t);if(a.y>s)return"top";{const e=ot(t),o=Math.max(e.dom.scrollHeight,Wt(e));return a.bottom<o-s||en().bottom<a.bottom-s?"bottom":"top"}case HB.bottom:return"bottom";case HB.top:default:return"top"}})(o);return a!==n&&(i=a,s.on((e=>{$A.setModes(e,[i]),u.setDockingMode(i);const t=m()?vc.TopToBottom:vc.BottomToTop;kt(e.element,yc,t)})),!0);var i}));return{isVisible:f,isPositionedAtTop:m,show:()=>{h.set(!0),Dt(r.outerContainer.element,"display","flex"),i.addClass(e.getBody(),"mce-edit-focus"),L(a,(e=>{Ht(e.element,"display")})),w(),lv(e)?y((e=>$A.isDocked(e)?$A.reset(e):$A.refresh(e))):y($A.refresh)},hide:()=>{h.set(!1),Dt(r.outerContainer.element,"display","none"),i.removeClass(e.getBody(),"mce-edit-focus"),L(a,(e=>{Dt(e.element,"display","none")}))},update:y,updateMode:()=>{w()&&y($A.reset)},repositionPopups:v}},UB=(e,t)=>{const o=Jo(e);return{pos:t?o.y:o.bottom,bounds:o}};var WB=Object.freeze({__proto__:null,render:(e,t,o,n,s)=>{const{mainUi:r}=t,a=Ql(),i=Ve(s.targetNode),l=PB(e,i,t,n,a),c=Ab(e);WD(e);const d=()=>{if(a.isSet())return void l.show();a.set(FD.getHeader(r.outerContainer).getOrDie());const s=rv(e);lv(e)?(Rd(i,r.mothership),Rd(i,t.popupUi.mothership)):Id(s,r.mothership),Id(s,t.dialogUi.mothership),DB(e,t,o,n),FD.setMenubar(r.outerContainer,ND(e,o)),l.show(),((e,t,o,n)=>{const s=Es(UB(t,o.isPositionedAtTop())),r=n=>{const{pos:r,bounds:a}=UB(t,o.isPositionedAtTop()),{pos:i,bounds:l}=s.get(),c=a.height!==l.height||a.width!==l.width;s.set({pos:r,bounds:a}),c&&gw(e,n),o.isVisible()&&(i!==r?o.update($A.reset):c&&(o.updateMode(),o.repositionPopups()))};n||(e.on("activate",o.show),e.on("deactivate",o.hide)),e.on("SkinLoaded ResizeWindow",(()=>o.update($A.reset))),e.on("NodeChange keydown",(e=>{requestAnimationFrame((()=>r(e)))}));let a=0;const i=PO((()=>o.update($A.refresh)),33);e.on("ScrollWindow",(()=>{const e=Uo().left;e!==a&&(a=e,i.throttle()),o.updateMode()})),lv(e)&&e.on("ElementScroll",(e=>{o.update($A.refresh)}));const l=Zl();l.set(oc(Ve(e.getBody()),"load",(e=>r(e.raw)))),e.on("remove",(()=>{l.clear()}))})(e,i,l,c),e.nodeChanged()};e.on("show",d),e.on("hide",l.hide),c||(e.on("focus",d),e.on("blur",l.hide)),e.on("init",(()=>{(e.hasFocus()||c)&&d()})),wx(e,t);const u={show:d,hide:l.hide,setEnabled:e=>{xx(t,!e)},isEnabled:()=>!Rm.isDisabled(r.outerContainer)};return{editorContainer:r.outerContainer.element.dom,api:u}}});const jB="contexttoolbar-hide",GB=(e,t)=>Ur(d_,((o,n)=>{const s=(e=>({hide:()=>Fr(e,hr()),getValue:()=>pu.getValue(e)}))(e.get(o));t.onAction(s,n.event.buttonApi)})),$B=(e,t)=>{const o=e.label.fold((()=>({})),(e=>({"aria-label":e}))),n=Gh(Vv.sketch({inputClasses:["tox-toolbar-textfield","tox-toolbar-nav-js"],data:e.initValue(),inputAttributes:o,selectOnFocus:!0,inputBehaviours:kl([Pp.config({mode:"special",onEnter:e=>s.findPrimary(e).map((e=>(Rr(e),!0))),onLeft:(e,t)=>(t.cut(),A.none()),onRight:(e,t)=>(t.cut(),A.none())})])})),s=((e,t,o)=>{const n=H(t,(t=>Gh(((e,t,o)=>(e=>"contextformtogglebutton"===e.type)(t)?((e,t,o)=>{const{primary:n,...s}=t.original,r=Xn(Ny({...s,type:"togglebutton",onAction:b}));return CB(r,o,[GB(e,t)])})(e,t,o):((e,t,o)=>{const{primary:n,...s}=t.original,r=Xn(Fy({...s,type:"button",onAction:b}));return kB(r,o,[GB(e,t)])})(e,t,o))(e,t,o))));return{asSpecs:()=>H(n,(e=>e.asSpec())),findPrimary:e=>re(t,((t,o)=>t.primary?A.from(n[o]).bind((t=>t.getOpt(e))).filter(C(Rm.isDisabled)):A.none()))}})(n,e.commands,t);return[{title:A.none(),items:[n.asSpec()]},{title:A.none(),items:s.asSpecs()}]},qB=(e,t,o)=>t.bottom-e.y>=o&&e.bottom-t.y>=o,XB=e=>{const t=(e=>{const t=e.getBoundingClientRect();if(t.height<=0&&t.width<=0){const o=ut(Ve(e.startContainer),e.startOffset).element;return($e(o)?st(o):A.some(o)).filter(Ge).map((e=>e.dom.getBoundingClientRect())).getOr(t)}return t})(e.selection.getRng());if(e.inline){const e=Uo();return Ko(e.left+t.left,e.top+t.top,t.width,t.height)}{const o=Zo(Ve(e.getBody()));return Ko(o.x+t.left,o.y+t.top,t.width,t.height)}},YB=(e,t,o,n=0)=>{const s=Go(window),r=Jo(Ve(e.getContentAreaContainer())),a=Kb(e)||Qb(e)||tv(e),{x:i,width:l}=((e,t,o)=>{const n=Math.max(e.x+o,t.x);return{x:n,width:Math.min(e.right-o,t.right)-n}})(r,s,n);if(e.inline&&!a)return Ko(i,s.y,l,s.height);{const a=t.header.isPositionedAtTop(),{y:c,bottom:d}=((e,t,o,n,s,r)=>{const a=Ve(e.getContainer()),i=pi(a,".tox-editor-header").getOr(a),l=Jo(i),c=l.y>=t.bottom,d=n&&!c;if(e.inline&&d)return{y:Math.max(l.bottom+r,o.y),bottom:o.bottom};if(e.inline&&!d)return{y:o.y,bottom:Math.min(l.y-r,o.bottom)};const u="line"===s?Jo(a):t;return d?{y:Math.max(l.bottom+r,o.y),bottom:Math.min(u.bottom-r,o.bottom)}:{y:Math.max(u.y+r,o.y),bottom:Math.min(l.y-r,o.bottom)}})(e,r,s,a,o,n);return Ko(i,c,l,d-c)}},KB={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"],inset:["tox-pop--inset"]},JB={maxHeightFunction:cc(),maxWidthFunction:NM()},ZB=e=>"node"===e,QB=(e,t,o,n,s)=>{const r=XB(e),a=n.lastElement().exists((e=>Ze(o,e)));return((e,t)=>{const o=e.selection.getRng(),n=ut(Ve(o.startContainer),o.startOffset);return o.startContainer===o.endContainer&&o.startOffset===o.endOffset-1&&Ze(n.element,t)})(e,o)?a?gE:lE:a?((e,o,s)=>{const a=Nt(e,"position");Dt(e,"position",o);const i=qB(r,Jo(t),-20)&&!n.isReposition()?hE:gE;return a.each((t=>Dt(e,"position",t))),i})(t,n.getMode()):("fixed"===n.getMode()?s.y+Uo().top:s.y)+(Wt(t)+12)<=r.y?lE:cE},eF=(e,t,o,n)=>{const s=t=>(n,s,r,a,i)=>({...QB(e,a,t,o,i)({...n,y:i.y,height:i.height},s,r,a,i),alwaysFit:!0}),r=e=>ZB(n)?[s(e)]:[];return t?{onLtr:e=>[cl,sl,rl,al,il,ll].concat(r(e)),onRtl:e=>[cl,rl,sl,il,al,ll].concat(r(e))}:{onLtr:e=>[ll,cl,al,sl,il,rl].concat(r(e)),onRtl:e=>[ll,cl,il,rl,al,sl].concat(r(e))}},tF=(e,t)=>{const o=U(t,(t=>t.predicate(e.dom))),{pass:n,fail:s}=P(o,(e=>"contexttoolbar"===e.type));return{contextToolbars:n,contextForms:s}},oF=(e,t)=>{const o={},n=[],s=[],r={},a={},i=ae(e);return L(i,(i=>{const l=e[i];"contextform"===l.type?((e,i)=>{const l=Xn(qn("ContextForm",Wy,i));o[e]=l,l.launch.map((o=>{r["form:"+e]={...i.launch,type:"contextformtogglebutton"===o.type?"togglebutton":"button",onAction:()=>{t(l)}}})),"editor"===l.scope?s.push(l):n.push(l),a[e]=l})(i,l):"contexttoolbar"===l.type&&((e,t)=>{var o;(o=t,qn("ContextToolbar",jy,o)).each((o=>{"editor"===t.scope?s.push(o):n.push(o),a[e]=o}))})(i,l)})),{forms:o,inNodeScope:n,inEditorScope:s,lookupTable:a,formNavigators:r}},nF=la("forward-slide"),sF=la("backward-slide"),rF=la("change-slide-event"),aF="tox-pop--resizing",iF="tox-pop--transition",lF=(e,t,o,n)=>{const s=n.backstage,r=s.shared,a=Do().deviceType.isTouch,i=Ql(),l=Ql(),c=Ql(),d=ri((e=>{const t=Es([]);return Ph.sketch({dom:{tag:"div",classes:["tox-pop"]},fireDismissalEventInstead:{event:"doNotDismissYet"},onShow:e=>{t.set([]),Ph.getContent(e).each((e=>{Ht(e.element,"visibility")})),Pa(e.element,aF),Ht(e.element,"width")},inlineBehaviours:kl([Jp("context-toolbar-events",[Yr(or(),((e,t)=>{"width"===t.event.raw.propertyName&&(Pa(e.element,aF),Ht(e.element,"width"))})),Ur(rF,((e,t)=>{const o=e.element;Ht(o,"width");const n=Jt(o);Ph.setContent(e,t.event.contents),La(o,aF);const s=Jt(o);Dt(o,"width",n+"px"),Ph.getContent(e).each((e=>{t.event.focus.bind((e=>(Dl(e),Rl(o)))).orThunk((()=>(Pp.focusIn(e),Il(ht(o)))))})),setTimeout((()=>{Dt(e.element,"width",s+"px")}),0)})),Ur(nF,((e,o)=>{Ph.getContent(e).each((o=>{t.set(t.get().concat([{bar:o,focus:Il(ht(e.element))}]))})),Ir(e,rF,{contents:o.event.forwardContents,focus:A.none()})})),Ur(sF,((e,o)=>{ne(t.get()).each((o=>{t.set(t.get().slice(0,t.get().length-1)),Ir(e,rF,{contents:ai(o.bar),focus:o.focus})}))}))]),Pp.config({mode:"special",onEscape:o=>ne(t.get()).fold((()=>e.onEscape()),(e=>(Fr(o,sF),A.some(!0))))})]),lazySink:()=>sn.value(e.sink)})})({sink:o,onEscape:()=>(e.focus(),A.some(!0))})),u=()=>{const t=c.get().getOr("node"),o=ZB(t)?1:0;return YB(e,r,t,o)},m=()=>!(e.removed||a()&&s.isContextMenuOpen()),g=()=>{if(m()){const t=u(),o=xe(c.get(),"node")?((e,t)=>t.filter((e=>yt(e)&&je(e))).map(Zo).getOrThunk((()=>XB(e))))(e,i.get()):XB(e);return t.height<=0||!qB(o,t,.01)}return!0},p=()=>{i.clear(),l.clear(),c.clear(),Ph.hide(d)},h=()=>{if(Ph.isOpen(d)){const e=d.element;Ht(e,"display"),g()?Dt(e,"display","none"):(l.set(0),Ph.reposition(d))}},f=t=>({dom:{tag:"div",classes:["tox-pop__dialog"]},components:[t],behaviours:kl([Pp.config({mode:"acyclic"}),Jp("pop-dialog-wrap-events",[Kr((t=>{e.shortcuts.add("ctrl+F9","focus statusbar",(()=>Pp.focusIn(t)))})),Jr((t=>{e.shortcuts.remove("ctrl+F9")}))])])}),v=Qt((()=>oF(t,(e=>{const t=y([e]);Ir(d,nF,{forwardContents:f(t)})})))),y=t=>{const{buttons:o}=e.ui.registry.getAll(),s={...o,...v().formNavigators},a=Cb(e)===sb.scrolling?sb.scrolling:sb.default,i=q(H(t,(t=>"contexttoolbar"===t.type?((t,o)=>MB(e,{buttons:t,toolbar:o.items,allowToolbarGroups:!1},n.backstage,A.some(["form:"])))(s,t):((e,t)=>$B(e,t))(t,r.providers))));return iD({type:a,uid:la("context-toolbar"),initGroups:i,onEscape:A.none,cyclicKeying:!0,providers:r.providers})},x=(t,n)=>{if(S.cancel(),!m())return;const s=y(t),p=t[0].position,h=((t,n)=>{const s="node"===t?r.anchors.node(n):r.anchors.cursor(),c=((e,t,o,n)=>"line"===t?{bubble:gc(12,0,KB),layouts:{onLtr:()=>[dl],onRtl:()=>[ul]},overrides:JB}:{bubble:gc(0,12,KB,1/12),layouts:eF(e,o,n,t),overrides:JB})(e,t,a(),{lastElement:i.get,isReposition:()=>xe(l.get(),0),getMode:()=>Sd.getMode(o)});return fn(s,c)})(p,n);c.set(p),l.set(1);const b=d.element;Ht(b,"display"),(e=>xe(Se(e,i.get(),Ze),!0))(n)||(Pa(b,iF),Sd.reset(o,d)),Ph.showWithinBounds(d,f(s),{anchor:h,transition:{classes:[iF],mode:"placement"}},(()=>A.some(u()))),n.fold(i.clear,i.set),g()&&Dt(b,"display","none")};let w=!1;const S=PO((()=>{!e.hasFocus()||e.removed||w||(Ua(d.element,iF)?S.throttle():((e,t)=>{const o=Ve(t.getBody()),n=e=>Ze(e,o),s=Ve(t.selection.getNode());return(e=>!n(e)&&!Qe(o,e))(s)?A.none():((e,t,o)=>{const n=tF(e,t);if(n.contextForms.length>0)return A.some({elem:e,toolbars:[n.contextForms[0]]});{const t=tF(e,o);if(t.contextForms.length>0)return A.some({elem:e,toolbars:[t.contextForms[0]]});if(n.contextToolbars.length>0||t.contextToolbars.length>0){const o=(e=>{if(e.length<=1)return e;{const t=t=>N(e,(e=>e.position===t)),o=t=>U(e,(e=>e.position===t)),n=t("selection"),s=t("node");if(n||s){if(s&&n){const e=o("node"),t=H(o("selection"),(e=>({...e,position:"node"})));return e.concat(t)}return o(n?"selection":"node")}return o("line")}})(n.contextToolbars.concat(t.contextToolbars));return A.some({elem:e,toolbars:o})}return A.none()}})(s,e.inNodeScope,e.inEditorScope).orThunk((()=>((e,t,o)=>e(t)?A.none():Fs(t,(e=>{if(Ge(e)){const{contextToolbars:t,contextForms:n}=tF(e,o.inNodeScope),s=n.length>0?n:(e=>{if(e.length<=1)return e;{const t=t=>G(e,(e=>e.position===t));return t("selection").orThunk((()=>t("node"))).orThunk((()=>t("line"))).map((e=>e.position)).fold((()=>[]),(t=>U(e,(e=>e.position===t))))}})(t);return s.length>0?A.some({elem:e,toolbars:s}):A.none()}return A.none()}),e))(n,s,e)))})(v(),e).fold(p,(e=>{x(e.toolbars,A.some(e.elem))})))}),17);e.on("init",(()=>{e.on("remove",p),e.on("ScrollContent ScrollWindow ObjectResized ResizeEditor longpress",h),e.on("click keyup focus SetContent",S.throttle),e.on(jB,p),e.on("contexttoolbar-show",(t=>{const o=v();be(o.lookupTable,t.toolbarKey).each((o=>{x([o],Ce(t.target!==e,t.target)),Ph.getContent(d).each(Pp.focusIn)}))})),e.on("focusout",(t=>{Uh.setEditorTimeout(e,(()=>{Rl(o.element).isNone()&&Rl(d.element).isNone()&&p()}),0)})),e.on("SwitchMode",(()=>{e.mode.isReadOnly()&&p()})),e.on("AfterProgressState",(t=>{t.state?p():e.hasFocus()&&S.throttle()})),e.on("dragstart",(()=>{w=!0})),e.on("dragend drop",(()=>{w=!1})),e.on("NodeChange",(e=>{Rl(d.element).fold(S.throttle,b)}))}))},cF=(e,t)=>{const o=()=>{const o=t.getOptions(e),n=t.getCurrent(e).map(t.hash),s=Ql();return H(o,(o=>({type:"togglemenuitem",text:t.display(o),onSetup:r=>{const a=e=>{e&&(s.on((e=>e.setActive(!1))),s.set(r)),r.setActive(e)};a(xe(n,t.hash(o)));const i=t.watcher(e,o,a);return()=>{s.clear(),i()}},onAction:()=>t.setCurrent(e,o)})))};e.ui.registry.addMenuButton(t.name,{tooltip:t.text,icon:t.icon,fetch:e=>e(o()),onSetup:t.onToolbarSetup}),e.ui.registry.addNestedMenuItem(t.name,{type:"nestedmenuitem",text:t.text,getSubmenuItems:o,onSetup:t.onMenuSetup})},dF=e=>{cF(e,(e=>({name:"lineheight",text:"Line height",icon:"line-height",getOptions:Zb,hash:e=>((e,t)=>sB(e,["fixed","relative","empty"]).map((({value:e,unit:t})=>e+t)))(e).getOr(e),display:w,watcher:(e,t,o)=>e.formatter.formatChanged("lineheight",o,!1,{value:t}).unbind,getCurrent:e=>A.from(e.queryCommandValue("LineHeight")),setCurrent:(e,t)=>e.execCommand("LineHeight",!1,t),onToolbarSetup:bw(e),onMenuSetup:bw(e)}))(e)),(e=>A.from(Sb(e)).map((t=>({name:"language",text:"Language",icon:"language",getOptions:x(t),hash:e=>u(e.customCode)?e.code:`${e.code}/${e.customCode}`,display:e=>e.title,watcher:(e,t,o)=>{var n;return e.formatter.formatChanged("lang",o,!1,{value:t.code,customValue:null!==(n=t.customCode)&&void 0!==n?n:null}).unbind},getCurrent:e=>{const t=Ve(e.selection.getNode());return Is(t,(e=>A.some(e).filter(Ge).bind((e=>_t(e,"lang").map((t=>({code:t,customCode:_t(e,"data-mce-lang").getOrUndefined(),title:""})))))))},setCurrent:(e,t)=>e.execCommand("Lang",!1,t),onToolbarSetup:t=>{const o=Zl();return t.setActive(e.formatter.match("lang",{},void 0,!0)),o.set(e.formatter.formatChanged("lang",t.setActive,!0)),fw(o.clear,bw(e)(t))},onMenuSetup:bw(e)}))))(e).each((t=>cF(e,t)))},uF=e=>yw(e,"NodeChange",(t=>{t.setEnabled(e.queryCommandState("outdent")&&e.selection.isEditable())})),mF=(e,t)=>o=>{o.setActive(t.get());const n=e=>{t.set(e.state),o.setActive(e.state)};return e.on("PastePlainTextToggle",n),fw((()=>e.off("PastePlainTextToggle",n)),bw(e)(o))},gF=(e,t)=>()=>{e.execCommand("mceToggleFormat",!1,t)},pF=e=>{(e=>{(e=>{LO.each([{name:"bold",text:"Bold",icon:"bold"},{name:"italic",text:"Italic",icon:"italic"},{name:"underline",text:"Underline",icon:"underline"},{name:"strikethrough",text:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",icon:"superscript"}],((t,o)=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:vw(e,t.name),onAction:gF(e,t.name)})}));for(let t=1;t<=6;t++){const o="h"+t;e.ui.registry.addToggleButton(o,{text:o.toUpperCase(),tooltip:"Heading "+t,onSetup:vw(e,o),onAction:gF(e,o)})}})(e),(e=>{LO.each([{name:"copy",text:"Copy",action:"Copy",icon:"copy"},{name:"help",text:"Help",action:"mceHelp",icon:"help"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"print",text:"Print",action:"mcePrint",icon:"print"}],(t=>{e.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onAction:ww(e,t.action)})})),LO.each([{name:"cut",text:"Cut",action:"Cut",icon:"cut"},{name:"paste",text:"Paste",action:"Paste",icon:"paste"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"remove",text:"Remove",action:"Delete",icon:"remove"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:bw(e),onAction:ww(e,t.action)})}))})(e),(e=>{LO.each([{name:"blockquote",text:"Blockquote",action:"mceBlockQuote",icon:"quote"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:ww(e,t.action),onSetup:vw(e,t.name)})}))})(e)})(e),(e=>{LO.each([{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"copy",text:"Copy",action:"Copy",icon:"copy",shortcut:"Meta+C"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A"},{name:"print",text:"Print...",action:"mcePrint",icon:"print",shortcut:"Meta+P"}],(t=>{e.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onAction:ww(e,t.action)})})),LO.each([{name:"bold",text:"Bold",action:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",action:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",action:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",action:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",action:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",action:"Superscript",icon:"superscript"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"cut",text:"Cut",action:"Cut",icon:"cut",shortcut:"Meta+X"},{name:"paste",text:"Paste",action:"Paste",icon:"paste",shortcut:"Meta+V"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onSetup:bw(e),onAction:ww(e,t.action)})})),e.ui.registry.addMenuItem("codeformat",{text:"Code",icon:"sourcecode",onSetup:bw(e),onAction:gF(e,"code")})})(e)},hF=(e,t)=>yw(e,"Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",(o=>{o.setEnabled(!e.mode.isReadOnly()&&e.undoManager[t]())})),fF=e=>yw(e,"VisualAid",(t=>{t.setActive(e.hasVisual)})),bF=(e,t)=>{(e=>{L([{name:"alignleft",text:"Align left",cmd:"JustifyLeft",icon:"align-left"},{name:"aligncenter",text:"Align center",cmd:"JustifyCenter",icon:"align-center"},{name:"alignright",text:"Align right",cmd:"JustifyRight",icon:"align-right"},{name:"alignjustify",text:"Justify",cmd:"JustifyFull",icon:"align-justify"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:ww(e,t.cmd),onSetup:vw(e,t.name)})})),e.ui.registry.addButton("alignnone",{tooltip:"No alignment",icon:"align-none",onSetup:bw(e),onAction:ww(e,"JustifyNone")})})(e),pF(e),((e,t)=>{((e,t)=>{const o=GD(0,t,KD(e));e.ui.registry.addNestedMenuItem("align",{text:t.shared.providers.translate("Align"),onSetup:bw(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=GD(0,t,tB(e));e.ui.registry.addNestedMenuItem("fontfamily",{text:t.shared.providers.translate("Fonts"),onSetup:bw(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o={type:"advanced",...t.styles},n=GD(0,t,uB(e,o));e.ui.registry.addNestedMenuItem("styles",{text:"Formats",onSetup:bw(e),getSubmenuItems:()=>n.items.validateItems(n.getStyleItems())})})(e,t),((e,t)=>{const o=GD(0,t,ZD(e));e.ui.registry.addNestedMenuItem("blocks",{text:"Blocks",onSetup:bw(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=GD(0,t,dB(e));e.ui.registry.addNestedMenuItem("fontsize",{text:"Font sizes",onSetup:bw(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t)})(e,t),(e=>{(e=>{e.ui.registry.addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onSetup:hF(e,"hasUndo"),onAction:ww(e,"undo")}),e.ui.registry.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onSetup:hF(e,"hasRedo"),onAction:ww(e,"redo")})})(e),(e=>{e.ui.registry.addButton("undo",{tooltip:"Undo",icon:"undo",enabled:!1,onSetup:hF(e,"hasUndo"),onAction:ww(e,"undo")}),e.ui.registry.addButton("redo",{tooltip:"Redo",icon:"redo",enabled:!1,onSetup:hF(e,"hasRedo"),onAction:ww(e,"redo")})})(e)})(e),(e=>{(e=>{e.addCommand("mceApplyTextcolor",((t,o)=>{((e,t,o)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.apply(t,{value:o}),e.nodeChanged()}))})(e,t,o)})),e.addCommand("mceRemoveTextcolor",(t=>{((e,t)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.remove(t,{value:null},void 0,!0),e.nodeChanged()}))})(e,t)}))})(e);const t=Lw(e),o=Pw(e),n=Es(t),s=Es(o);Yw(e,"forecolor","forecolor","Text color",n),Yw(e,"backcolor","hilitecolor","Background color",s),Kw(e,"forecolor","forecolor","Text color",n),Kw(e,"backcolor","hilitecolor","Background color",s)})(e),(e=>{(e=>{e.ui.registry.addButton("visualaid",{tooltip:"Visual aids",text:"Visual aids",onAction:ww(e,"mceToggleVisualAid")})})(e),(e=>{e.ui.registry.addToggleMenuItem("visualaid",{text:"Visual aids",onSetup:fF(e),onAction:ww(e,"mceToggleVisualAid")})})(e)})(e),(e=>{(e=>{e.ui.registry.addButton("outdent",{tooltip:"Decrease indent",icon:"outdent",onSetup:uF(e),onAction:ww(e,"outdent")}),e.ui.registry.addButton("indent",{tooltip:"Increase indent",icon:"indent",onSetup:bw(e),onAction:ww(e,"indent")})})(e)})(e),dF(e),(e=>{const t=Es(Gb(e)),o=()=>e.execCommand("mceTogglePlainTextPaste");e.ui.registry.addToggleButton("pastetext",{active:!1,icon:"paste-text",tooltip:"Paste as text",onAction:o,onSetup:mF(e,t)}),e.ui.registry.addToggleMenuItem("pastetext",{text:"Paste as text",icon:"paste-text",onAction:o,onSetup:mF(e,t)})})(e)},vF=e=>r(e)?e.split(/[ ,]/):e,yF=e=>t=>t.options.get(e),xF=yF("contextmenu_never_use_native"),wF=yF("contextmenu_avoid_overlap"),SF=e=>{const t=e.ui.registry.getAll().contextMenus,o=e.options.get("contextmenu");return e.options.isSet("contextmenu")?o:U(o,(e=>ve(t,e)))},kF=(e,t)=>({type:"makeshift",x:e,y:t}),CF=e=>"longpress"===e.type||0===e.type.indexOf("touch"),OF=(e,t)=>"contextmenu"===t.type||"longpress"===t.type?e.inline?(e=>{if(CF(e)){const t=e.touches[0];return kF(t.pageX,t.pageY)}return kF(e.pageX,e.pageY)})(t):((e,t)=>{const o=ab.DOM.getPos(e);return((e,t,o)=>kF(e.x+t,e.y+o))(t,o.x,o.y)})(e.getContentAreaContainer(),(e=>{if(CF(e)){const t=e.touches[0];return kF(t.clientX,t.clientY)}return kF(e.clientX,e.clientY)})(t)):_F(e),_F=e=>({type:"selection",root:Ve(e.selection.getNode())}),TF=(e,t,o)=>{switch(o){case"node":return(e=>({type:"node",node:A.some(Ve(e.selection.getNode())),root:Ve(e.getBody())}))(e);case"point":return OF(e,t);case"selection":return _F(e)}},EF=(e,t,o,n,s,r)=>{const a=o(),i=TF(e,t,r);C_(a,pv.CLOSE_ON_EXECUTE,n,{isHorizontalMenu:!1,search:A.none()}).map((e=>{t.preventDefault(),Ph.showMenuAt(s,{anchor:i},{menu:{markers:Av("normal")},data:e})}))},AF={onLtr:()=>[cl,sl,rl,al,il,ll,lE,cE,iE,rE,aE,sE],onRtl:()=>[cl,rl,sl,il,al,ll,lE,cE,aE,sE,iE,rE]},MF={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},DF=(e,t,o,n,s,r)=>{const a=Do(),i=a.os.isiOS(),l=a.os.isMacOS(),c=a.os.isAndroid(),d=a.deviceType.isTouch(),u=()=>{const a=o();((e,t,o,n,s,r,a)=>{const i=((e,t,o)=>{const n=TF(e,t,o);return{bubble:gc(0,"point"===o?12:0,MF),layouts:AF,overrides:{maxWidthFunction:NM(),maxHeightFunction:cc()},...n}})(e,t,r);C_(o,pv.CLOSE_ON_EXECUTE,n,{isHorizontalMenu:!0,search:A.none()}).map((o=>{t.preventDefault();const l=a?zh.HighlightMenuAndItem:zh.HighlightNone;Ph.showMenuWithinBounds(s,{anchor:i},{menu:{markers:Av("normal"),highlightOnOpen:l},data:o,type:"horizontal"},(()=>A.some(YB(e,n.shared,"node"===r?"node":"selection")))),e.dispatch(jB)}))})(e,t,a,n,s,r,!(c||i||l&&d))};if((l||i)&&"node"!==r){const o=()=>{(e=>{const t=e.selection.getRng(),o=()=>{Uh.setEditorTimeout(e,(()=>{e.selection.setRng(t)}),10),r()};e.once("touchend",o);const n=e=>{e.preventDefault(),e.stopImmediatePropagation()};e.on("mousedown",n,!0);const s=()=>r();e.once("longpresscancel",s);const r=()=>{e.off("touchend",o),e.off("longpresscancel",s),e.off("mousedown",n)}})(e),u()};((e,t)=>{const o=e.selection;if(o.isCollapsed()||t.touches.length<1)return!1;{const n=t.touches[0],s=o.getRng();return Jc(e.getWin(),Lc.domRange(s)).exists((e=>e.left<=n.clientX&&e.right>=n.clientX&&e.top<=n.clientY&&e.bottom>=n.clientY))}})(e,t)?o():(e.once("selectionchange",o),e.once("touchend",(()=>e.off("selectionchange",o))))}else u()},BF=e=>r(e)?"|"===e:"separator"===e.type,FF={type:"separator"},IF=e=>{const t=e=>({text:e.text,icon:e.icon,enabled:e.enabled,shortcut:e.shortcut});if(r(e))return e;switch(e.type){case"separator":return FF;case"submenu":return{type:"nestedmenuitem",...t(e),getSubmenuItems:()=>{const t=e.getSubmenuItems();return r(t)?t:H(t,IF)}};default:const o=e;return{type:"menuitem",...t(o),onAction:v(o.onAction)}}},RF=(e,t)=>{if(0===t.length)return e;const o=ne(e).filter((e=>!BF(e))).fold((()=>[]),(e=>[FF]));return e.concat(o).concat(t).concat([FF])},NF=(e,t)=>!(e=>"longpress"===e.type||ve(e,"touches"))(t)&&(2!==t.button||t.target===e.getBody()&&""===t.pointerType),VF=(e,t)=>NF(e,t)?e.selection.getStart(!0):t.target,zF=(e,t,o)=>{const n=Do().deviceType.isTouch,s=ri(Ph.sketch({dom:{tag:"div"},lazySink:t,onEscape:()=>e.focus(),onShow:()=>o.setContextMenuState(!0),onHide:()=>o.setContextMenuState(!1),fireDismissalEventInstead:{},inlineBehaviours:kl([Jp("dismissContextMenu",[Ur(Cr(),((t,o)=>{Xd.close(t),e.focus()}))])])})),a=()=>Ph.hide(s),i=t=>{if(xF(e)&&t.preventDefault(),((e,t)=>t.ctrlKey&&!xF(e))(e,t)||(e=>0===SF(e).length)(e))return;const a=((e,t)=>{const o=wF(e),n=NF(e,t)?"selection":"point";if(De(o)){const s=VF(e,t);return $S(Ve(s),o)?"node":n}return n})(e,t);(n()?DF:EF)(e,t,(()=>{const o=VF(e,t),n=e.ui.registry.getAll(),s=SF(e);return((e,t,o)=>{const n=j(t,((t,n)=>be(e,n.toLowerCase()).map((e=>{const n=e.update(o);if(r(n)&&De(Me(n)))return RF(t,n.split(" "));if(l(n)&&n.length>0){const e=H(n,IF);return RF(t,e)}return t})).getOrThunk((()=>t.concat([n])))),[]);return n.length>0&&BF(n[n.length-1])&&n.pop(),n})(n.contextMenus,s,o)}),o,s,a)};e.on("init",(()=>{const t="ResizeEditor ScrollContent ScrollWindow longpresscancel"+(n()?"":" ResizeWindow");e.on(t,a),e.on("longpress contextmenu",i)}))},HF=As([{offset:["x","y"]},{absolute:["x","y"]},{fixed:["x","y"]}]),LF=e=>t=>t.translate(-e.left,-e.top),PF=e=>t=>t.translate(e.left,e.top),UF=e=>(t,o)=>j(e,((e,t)=>t(e)),$t(t,o)),WF=(e,t,o)=>e.fold(UF([PF(o),LF(t)]),UF([LF(t)]),UF([])),jF=(e,t,o)=>e.fold(UF([PF(o)]),UF([]),UF([PF(t)])),GF=(e,t,o)=>e.fold(UF([]),UF([LF(o)]),UF([PF(t),LF(o)])),$F=(e,t,o)=>{const n=e.fold(((e,t)=>({position:A.some("absolute"),left:A.some(e+"px"),top:A.some(t+"px")})),((e,t)=>({position:A.some("absolute"),left:A.some(e-o.left+"px"),top:A.some(t-o.top+"px")})),((e,t)=>({position:A.some("fixed"),left:A.some(e+"px"),top:A.some(t+"px")})));return{right:A.none(),bottom:A.none(),...n}},qF=(e,t,o,n)=>{const s=(e,s)=>(r,a)=>{const i=e(t,o,n);return s(r.getOr(i.left),a.getOr(i.top))};return e.fold(s(GF,XF),s(jF,YF),s(WF,KF))},XF=HF.offset,YF=HF.absolute,KF=HF.fixed,JF=(e,t)=>{const o=Ot(e,t);return u(o)?NaN:parseInt(o,10)},ZF=(e,t,o,n,s,r)=>{const a=((e,t,o,n)=>((e,t)=>{const o=e.element,n=JF(o,t.leftAttr),s=JF(o,t.topAttr);return isNaN(n)||isNaN(s)?A.none():A.some($t(n,s))})(e,t).fold((()=>o),(e=>KF(e.left+n.left,e.top+n.top))))(e,t,o,n),i=t.mustSnap?eI(e,t,a,s,r):tI(e,t,a,s,r),l=WF(a,s,r);return((e,t,o)=>{const n=e.element;kt(n,t.leftAttr,o.left+"px"),kt(n,t.topAttr,o.top+"px")})(e,t,l),i.fold((()=>({coord:KF(l.left,l.top),extra:A.none()})),(e=>({coord:e.output,extra:e.extra})))},QF=(e,t,o,n)=>re(e,(e=>{const s=e.sensor,r=((e,t,o,n,s,r)=>{const a=jF(e,s,r),i=jF(t,s,r);return Math.abs(a.left-i.left)<=o&&Math.abs(a.top-i.top)<=n})(t,s,e.range.left,e.range.top,o,n);return r?A.some({output:qF(e.output,t,o,n),extra:e.extra}):A.none()})),eI=(e,t,o,n,s)=>{const r=t.getSnapPoints(e);return QF(r,o,n,s).orThunk((()=>{const e=j(r,((e,t)=>{const r=t.sensor,a=((e,t,o,n,s,r)=>{const a=jF(e,s,r),i=jF(t,s,r),l=Math.abs(a.left-i.left),c=Math.abs(a.top-i.top);return $t(l,c)})(o,r,t.range.left,t.range.top,n,s);return e.deltas.fold((()=>({deltas:A.some(a),snap:A.some(t)})),(o=>(a.left+a.top)/2<=(o.left+o.top)/2?{deltas:A.some(a),snap:A.some(t)}:e))}),{deltas:A.none(),snap:A.none()});return e.snap.map((e=>({output:qF(e.output,o,n,s),extra:e.extra})))}))},tI=(e,t,o,n,s)=>{const r=t.getSnapPoints(e);return QF(r,o,n,s)};var oI=Object.freeze({__proto__:null,snapTo:(e,t,o,n)=>{const s=t.getTarget(e.element);if(t.repositionTarget){const t=et(e.element),o=Uo(t),r=wA(s),a=((e,t,o)=>({coord:qF(e.output,e.output,t,o),extra:e.extra}))(n,o,r),i=$F(a.coord,0,r);Ft(s,i)}}});const nI="data-initial-z-index",sI=(e,t)=>{e.getSystem().addToGui(t),(e=>{st(e.element).filter(Ge).each((t=>{Nt(t,"z-index").each((e=>{kt(t,nI,e)})),Dt(t,"z-index",It(e.element,"z-index"))}))})(t)},rI=e=>{(e=>{st(e.element).filter(Ge).each((e=>{_t(e,nI).fold((()=>Ht(e,"z-index")),(t=>Dt(e,"z-index",t))),Et(e,nI)}))})(e),e.getSystem().removeFromGui(e)},aI=(e,t,o)=>e.getSystem().build(ok.sketch({dom:{styles:{left:"0px",top:"0px",width:"100%",height:"100%",position:"fixed","z-index":"1000000000000000"},classes:[t]},events:o}));var iI=vs("snaps",[os("getSnapPoints"),Di("onSensor"),os("leftAttr"),os("topAttr"),ys("lazyViewport",en),ys("mustSnap",!1)]);const lI=[ys("useFixed",T),os("blockerClass"),ys("getTarget",w),ys("onDrag",b),ys("repositionTarget",!0),ys("onDrop",b),Os("getBounds",en),iI],cI=e=>{return(t=Nt(e,"left"),o=Nt(e,"top"),n=Nt(e,"position"),t.isSome()&&o.isSome()&&n.isSome()?A.some(((e,t,o)=>("fixed"===o?KF:XF)(parseInt(e,10),parseInt(t,10)))(t.getOrDie(),o.getOrDie(),n.getOrDie())):A.none()).getOrThunk((()=>{const t=Xt(e);return YF(t.left,t.top)}));var t,o,n},dI=(e,t)=>({bounds:e.getBounds(),height:jt(t.element),width:Zt(t.element)}),uI=(e,t,o,n,s)=>{const r=o.update(n,s),a=o.getStartData().getOrThunk((()=>dI(t,e)));r.each((o=>{((e,t,o,n)=>{const s=t.getTarget(e.element);if(t.repositionTarget){const r=et(e.element),a=Uo(r),i=wA(s),l=cI(s),c=((e,t,o,n,s,r,a)=>((e,t,o,n,s)=>{const r=s.bounds,a=jF(t,o,n),i=Yi(a.left,r.x,r.x+r.width-s.width),l=Yi(a.top,r.y,r.y+r.height-s.height),c=YF(i,l);return t.fold((()=>{const e=GF(c,o,n);return XF(e.left,e.top)}),x(c),(()=>{const e=WF(c,o,n);return KF(e.left,e.top)}))})(0,t.fold((()=>{const e=(t=o,a=r.left,i=r.top,t.fold(((e,t)=>XF(e+a,t+i)),((e,t)=>YF(e+a,t+i)),((e,t)=>KF(e+a,t+i))));var t,a,i;const l=WF(e,n,s);return KF(l.left,l.top)}),(t=>{const a=ZF(e,t,o,r,n,s);return a.extra.each((o=>{t.onSensor(e,o)})),a.coord})),n,s,a))(e,t.snaps,l,a,i,n,o),d=$F(c,0,i);Ft(s,d)}t.onDrag(e,s,n)})(e,t,a,o)}))},mI=(e,t,o,n)=>{t.each(rI),o.snaps.each((t=>{((e,t)=>{((e,t)=>{const o=e.element;Et(o,t.leftAttr),Et(o,t.topAttr)})(e,t)})(e,t)}));const s=o.getTarget(e.element);n.reset(),o.onDrop(e,s)},gI=e=>(t,o)=>{const n=e=>{o.setStartData(dI(t,e))};return Hr([Ur(xr(),(e=>{o.getStartData().each((()=>n(e)))})),...e(t,o,n)])};var pI=Object.freeze({__proto__:null,getData:e=>A.from($t(e.x,e.y)),getDelta:(e,t)=>$t(t.left-e.left,t.top-e.top)});const hI=(e,t,o)=>[Ur(Ws(),((n,s)=>{if(0!==s.event.raw.button)return;s.stop();const r=()=>mI(n,A.some(l),e,t),a=qS(r,200),i={drop:r,delayDrop:a.schedule,forceDrop:r,move:o=>{a.cancel(),uI(n,e,t,pI,o)}},l=aI(n,e.blockerClass,(e=>Hr([Ur(Ws(),e.forceDrop),Ur($s(),e.drop),Ur(js(),((t,o)=>{e.move(o.event)})),Ur(Gs(),e.delayDrop)]))(i));o(n),sI(n,l)}))],fI=[...lI,Ri("dragger",{handlers:gI(hI)})];var bI=Object.freeze({__proto__:null,getData:e=>{const t=e.raw.touches;return 1===t.length?(e=>{const t=e[0];return A.some($t(t.clientX,t.clientY))})(t):A.none()},getDelta:(e,t)=>$t(t.left-e.left,t.top-e.top)});const vI=(e,t,o)=>{const n=Ql(),s=o=>{mI(o,n.get(),e,t),n.clear()};return[Ur(Hs(),((r,a)=>{a.stop();const i=()=>s(r),l={drop:i,delayDrop:b,forceDrop:i,move:o=>{uI(r,e,t,bI,o)}},c=aI(r,e.blockerClass,(e=>Hr([Ur(Hs(),e.forceDrop),Ur(Ps(),e.drop),Ur(Us(),e.drop),Ur(Ls(),((t,o)=>{e.move(o.event)}))]))(l));n.set(c),o(r),sI(r,c)})),Ur(Ls(),((o,n)=>{n.stop(),uI(o,e,t,bI,n.event)})),Ur(Ps(),((e,t)=>{t.stop(),s(e)})),Ur(Us(),s)]},yI=fI,xI=[...lI,Ri("dragger",{handlers:gI(vI)})],wI=[...lI,Ri("dragger",{handlers:gI(((e,t,o)=>[...hI(e,t,o),...vI(e,t,o)]))})];var SI=Object.freeze({__proto__:null,mouse:yI,touch:xI,mouseOrTouch:wI}),kI=Object.freeze({__proto__:null,init:()=>{let e=A.none(),t=A.none();const o=x({});return _a({readState:o,reset:()=>{e=A.none(),t=A.none()},update:(t,o)=>t.getData(o).bind((o=>((t,o)=>{const n=e.map((e=>t.getDelta(e,o)));return e=A.some(o),n})(t,o))),getStartData:()=>t,setStartData:e=>{t=A.some(e)}})}});const CI=Tl({branchKey:"mode",branches:SI,name:"dragging",active:{events:(e,t)=>e.dragger.handlers(e,t)},extra:{snap:e=>({sensor:e.sensor,range:e.range,output:e.output,extra:A.from(e.extra)})},state:kI,apis:oI}),OI=(e,t,o,n,s,r)=>e.fold((()=>CI.snap({sensor:YF(o-20,n-20),range:$t(s,r),output:YF(A.some(o),A.some(n)),extra:{td:t}})),(e=>{const s=o-20,r=n-20,a=e.element.dom.getBoundingClientRect();return CI.snap({sensor:YF(s,r),range:$t(40,40),output:YF(A.some(o-a.width/2),A.some(n-a.height/2)),extra:{td:t}})})),_I=(e,t,o)=>({getSnapPoints:e,leftAttr:"data-drag-left",topAttr:"data-drag-top",onSensor:(e,n)=>{const s=n.td;((e,t)=>e.exists((e=>Ze(e,t))))(t.get(),s)||(t.set(s),o(s))},mustSnap:!0}),TI=e=>Gh(Wh.sketch({dom:{tag:"div",classes:["tox-selector"]},buttonBehaviours:kl([CI.config({mode:"mouseOrTouch",blockerClass:"blocker",snaps:e}),Ik.config({})]),eventOrder:{mousedown:["dragging","alloy.base.behaviour"],touchstart:["dragging","alloy.base.behaviour"]}})),EI=(e,t)=>{const o=Es([]),n=Es([]),s=Es(!1),r=Ql(),a=Ql(),i=e=>{const o=Zo(e);return OI(u.getOpt(t),e,o.x,o.y,o.width,o.height)},l=e=>{const o=Zo(e);return OI(m.getOpt(t),e,o.right,o.bottom,o.width,o.height)},c=_I((()=>H(o.get(),(e=>i(e)))),r,(t=>{a.get().each((o=>{e.dispatch("TableSelectorChange",{start:t,finish:o})}))})),d=_I((()=>H(n.get(),(e=>l(e)))),a,(t=>{r.get().each((o=>{e.dispatch("TableSelectorChange",{start:o,finish:t})}))})),u=TI(c),m=TI(d),g=ri(u.asSpec()),p=ri(m.asSpec()),h=(t,o,n,s)=>{const r=n(o);CI.snapTo(t,r),((t,o,n,r)=>{const a=o.dom.getBoundingClientRect();Ht(t.element,"display");const i=nt(Ve(e.getBody())).dom.innerHeight,l=a[s]<0,c=((e,t)=>e[s]>t)(a,i);(l||c)&&Dt(t.element,"display","none")})(t,o)},f=e=>h(g,e,i,"top"),b=e=>h(p,e,l,"bottom");Do().deviceType.isTouch()&&(e.on("TableSelectionChange",(e=>{s.get()||(Ad(t,g),Ad(t,p),s.set(!0)),r.set(e.start),a.set(e.finish),e.otherCells.each((t=>{o.set(t.upOrLeftCells),n.set(t.downOrRightCells),f(e.start),b(e.finish)}))})),e.on("ResizeEditor ResizeWindow ScrollContent",(()=>{r.get().each(f),a.get().each(b)})),e.on("TableSelectionClear",(()=>{s.get()&&(Bd(g),Bd(p),s.set(!1)),r.clear(),a.clear()})))},AI=(e,t,o)=>{var n;const s=null!==(n=t.delimiter)&&void 0!==n?n:"\u203a";return{dom:{tag:"div",classes:["tox-statusbar__path"],attributes:{role:"navigation"}},behaviours:kl([Pp.config({mode:"flow",selector:"div[role=button]"}),Rm.config({disabled:o.isDisabled}),Sx(),ck.config({}),Kp.config({}),Jp("elementPathEvents",[Kr(((t,n)=>{e.shortcuts.add("alt+F11","focus statusbar elementpath",(()=>Pp.focusIn(t))),e.on("NodeChange",(n=>{const r=(t=>{const o=[];let n=t.length;for(;n-- >0;){const r=t[n];if(1===r.nodeType&&"BR"!==(s=r).nodeName&&!s.getAttribute("data-mce-bogus")&&"bookmark"!==s.getAttribute("data-mce-type")){const t=hw(e,r);if(t.isDefaultPrevented()||o.push({name:t.name,element:r}),t.isPropagationStopped())break}}var s;return o})(n.parents),a=r.length>0?j(r,((t,n,r)=>{const a=((t,n,s)=>Wh.sketch({dom:{tag:"div",classes:["tox-statusbar__path-item"],attributes:{"data-index":s,"aria-level":s+1}},components:[ti(t)],action:t=>{e.focus(),e.selection.select(n),e.nodeChanged()},buttonBehaviours:kl([kx(o.isDisabled),Sx()])}))(n.name,n.element,r);return 0===r?t.concat([a]):t.concat([{dom:{tag:"div",classes:["tox-statusbar__path-divider"],attributes:{"aria-hidden":!0}},components:[ti(` ${s} `)]},a])}),[]):[];Kp.set(t,a)}))}))])]),components:[]}};var MI;!function(e){e[e.None=0]="None",e[e.Both=1]="Both",e[e.Vertical=2]="Vertical"}(MI||(MI={}));const DI=(e,t,o)=>{const n=Ve(e.getContainer()),s=((e,t,o,n,s)=>{const r={height:VB(n+t.top,fb(e),vb(e))};return o===MI.Both&&(r.width=VB(s+t.left,hb(e),bb(e))),r})(e,t,o,Wt(n),Jt(n));le(s,((e,t)=>{h(e)&&Dt(n,t,NB(e))})),(e=>{e.dispatch("ResizeEditor")})(e)},BI=(e,t,o,n)=>{const s=$t(20*o,20*n);return DI(e,s,t),A.some(!0)},FI=(e,t)=>{const o=()=>{const o=[],n=Xb(e),s=Ub(e),r=Wb(e)||e.hasPlugin("wordcount");return s&&o.push(AI(e,{},t)),n&&o.push((()=>{const e=Ix("Alt+0");return{dom:{tag:"div",classes:["tox-statusbar__help-text"]},components:[ti($f.translate(["Press {0} for help",e]))]}})()),r&&o.push((()=>{const o=[];return e.hasPlugin("wordcount")&&o.push(((e,t)=>{const o=(e,o,n)=>Kp.set(e,[ti(t.translate(["{0} "+n,o[n]]))]);return Wh.sketch({dom:{tag:"button",classes:["tox-statusbar__wordcount"]},components:[],buttonBehaviours:kl([kx(t.isDisabled),Sx(),ck.config({}),Kp.config({}),pu.config({store:{mode:"memory",initialValue:{mode:"words",count:{words:0,characters:0}}}}),Jp("wordcount-events",[Qr((e=>{const t=pu.getValue(e),n="words"===t.mode?"characters":"words";pu.setValue(e,{mode:n,count:t.count}),o(e,t.count,n)})),Kr((t=>{e.on("wordCountUpdate",(e=>{const{mode:n}=pu.getValue(t);pu.setValue(t,{mode:n,count:e.wordCount}),o(t,e.wordCount,n)}))}))])]),eventOrder:{[ur()]:["disabling","alloy.base.behaviour","wordcount-events"]}})})(e,t)),Wb(e)&&o.push({dom:{tag:"span",classes:["tox-statusbar__branding"]},components:[{dom:{tag:"a",attributes:{href:"https://www.tiny.cloud/powered-by-tiny?utm_campaign=editor_referral&utm_medium=poweredby&utm_source=tinymce&utm_content=v6",rel:"noopener",target:"_blank","aria-label":$f.translate(["Powered by {0}","Tiny"])},innerHtml:'<svg width="50px" height="16px" viewBox="0 0 50 16" xmlns="http://www.w3.org/2000/svg">\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M10.143 0c2.608.015 5.186 2.178 5.186 5.331 0 0 .077 3.812-.084 4.87-.361 2.41-2.164 4.074-4.65 4.496-1.453.284-2.523.49-3.212.623-.373.071-.634.122-.785.152-.184.038-.997.145-1.35.145-2.732 0-5.21-2.04-5.248-5.33 0 0 0-3.514.03-4.442.093-2.4 1.758-4.342 4.926-4.963 0 0 3.875-.752 4.036-.782.368-.07.775-.1 1.15-.1Zm1.826 2.8L5.83 3.989v2.393l-2.455.475v5.968l6.137-1.189V9.243l2.456-.476V2.8ZM5.83 6.382l3.682-.713v3.574l-3.682.713V6.382Zm27.173-1.64-.084-1.066h-2.226v9.132h2.456V7.743c-.008-1.151.998-2.064 2.149-2.072 1.15-.008 1.987.92 1.995 2.072v5.065h2.455V7.359c-.015-2.18-1.657-3.929-3.837-3.913a3.993 3.993 0 0 0-2.908 1.296Zm-6.3-4.266L29.16 0v2.387l-2.456.475V.476Zm0 3.2v9.132h2.456V3.676h-2.456Zm18.179 11.787L49.11 3.676H46.58l-1.612 4.527-.46 1.382-.384-1.382-1.611-4.527H39.98l3.3 9.132L42.15 16l2.732-.537ZM22.867 9.738c0 .752.568 1.075.921 1.075.353 0 .668-.047.998-.154l.537 1.765c-.23.154-.92.537-2.225.537-1.305 0-2.655-.997-2.686-2.686a136.877 136.877 0 0 1 0-4.374H18.8V3.676h1.612v-1.98l2.455-.476v2.456h2.302V5.9h-2.302v3.837Z"/>\n</svg>\n'.trim()},behaviours:kl([oh.config({})])}]}),{dom:{tag:"div",classes:["tox-statusbar__right-container"]},components:o}})()),o.length>0?[{dom:{tag:"div",classes:["tox-statusbar__text-container",...(()=>{const e="tox-statusbar__text-container--flex-start",t="tox-statusbar__text-container--flex-end";if(n){const o="tox-statusbar__text-container-3-cols";return r||s?r&&!s?[o,t]:[o,e]:[o,"tox-statusbar__text-container--space-around"]}return[r&&!s?t:e]})()]},components:o}]:[]};return{dom:{tag:"div",classes:["tox-statusbar"]},components:(()=>{const n=o(),s=((e,t)=>{const o=(e=>{const t=jb(e);return!1===t?MI.None:"both"===t?MI.Both:MI.Vertical})(e);if(o===MI.None)return A.none();const n=o===MI.Both?"Press the arrow keys to resize the editor.":"Press the Up and Down arrow keys to resize the editor.";return A.some(tb("resize-handle",{tag:"div",classes:["tox-statusbar__resize-handle"],attributes:{title:t.translate("Resize"),"aria-label":t.translate(n)},behaviours:[CI.config({mode:"mouse",repositionTarget:!1,onDrag:(t,n,s)=>DI(e,s,o),blockerClass:"tox-blocker"}),Pp.config({mode:"special",onLeft:()=>BI(e,o,-1,0),onRight:()=>BI(e,o,1,0),onUp:()=>BI(e,o,0,-1),onDown:()=>BI(e,o,0,1)}),ck.config({}),oh.config({})]},t.icons))})(e,t);return n.concat(s.toArray())})()}},II=(e,t)=>t.get().getOrDie(`UI for ${e} has not been rendered`),RI=(e,t)=>{const o=e.inline,n=o?WB:IB,s=iv(e)?oM:yA,r=(()=>{const e=Ql(),t=Ql(),o=Ql();return{dialogUi:e,popupUi:t,mainUi:o,getUiMotherships:()=>{const o=e.get().map((e=>e.mothership)),n=t.get().map((e=>e.mothership));return o.fold((()=>n.toArray()),(e=>n.fold((()=>[e]),(t=>Ze(e.element,t.element)?[e]:[e,t]))))},lazyGetInOuterOrDie:(e,t)=>()=>o.get().bind((e=>t(e.outerContainer))).getOrDie(`Could not find ${e} element in OuterContainer`)}})(),a=Ql(),i=Ql(),l=Ql(),c=Do().deviceType.isTouch()?["tox-platform-touch"]:[],d=ov(e),u=Cb(e),m=Gh({dom:{tag:"div",classes:["tox-anchorbar"]}}),g=Gh({dom:{tag:"div",classes:["tox-bottom-anchorbar"]}}),p=()=>r.mainUi.get().map((e=>e.outerContainer)).bind(FD.getHeader),h=r.lazyGetInOuterOrDie("anchor bar",m.getOpt),f=r.lazyGetInOuterOrDie("bottom anchor bar",g.getOpt),b=r.lazyGetInOuterOrDie("toolbar",FD.getToolbar),v=r.lazyGetInOuterOrDie("throbber",FD.getThrobber),y=((e,t,o,n)=>{const s=Es(!1),r=(e=>{const t=Es(ov(e)?"bottom":"top");return{isPositionedAtTop:()=>"top"===t.get(),getDockingMode:t.get,setDockingMode:t.set}})(t),a={icons:()=>t.ui.registry.getAll().icons,menuItems:()=>t.ui.registry.getAll().menuItems,translate:$f.translate,isDisabled:()=>t.mode.isReadOnly()||!t.ui.isEnabled(),getOption:t.options.get},i=rA(t),l=(e=>{const t=t=>()=>e.formatter.match(t),o=t=>()=>{const o=e.formatter.get(t);return void 0!==o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},n=Es([]),s=Es([]),r=Es(!1);return e.on("PreInit",(s=>{const r=BE(e),a=IE(e,r,t,o);n.set(a)})),e.on("addStyleModifications",(n=>{const a=IE(e,n.items,t,o);s.set(a),r.set(n.replace)})),{getData:()=>{const e=r.get()?[]:n.get(),t=s.get();return e.concat(t)}}})(t),c=(e=>({colorPicker:kE(e),hasCustomColors:CE(e),getColors:OE(e),getColorCols:_E(e)}))(t),d=(e=>({isDraggableModal:TE(e)}))(t),u={shared:{providers:a,anchors:SE(t,o,n,r.isPositionedAtTop),header:r},urlinput:i,styles:l,colorinput:c,dialog:d,isContextMenuOpen:()=>s.get(),setContextMenuState:e=>s.set(e)},m={...u,shared:{...u.shared,interpreter:e=>KT(e,{},m),getSink:e.popup}},g={...u,shared:{...u.shared,interpreter:e=>KT(e,{},g),getSink:e.dialog}};return{popup:m,dialog:g}})({popup:()=>sn.fromOption(r.popupUi.get().map((e=>e.sink)),"(popup) UI has not been rendered"),dialog:()=>sn.fromOption(r.dialogUi.get().map((e=>e.sink)),"UI has not been rendered")},e,h,f),x=()=>{const t=(()=>{const t={attributes:{[yc]:d?vc.BottomToTop:vc.TopToBottom}},o=FD.parts.menubar({dom:{tag:"div",classes:["tox-menubar"]},backstage:y.popup,onEscape:()=>{e.focus()}}),n=FD.parts.toolbar({dom:{tag:"div",classes:["tox-toolbar"]},getSink:y.popup.shared.getSink,providers:y.popup.shared.providers,onEscape:()=>{e.focus()},onToolbarToggled:t=>{((e,t)=>{e.dispatch("ToggleToolbarDrawer",{state:t})})(e,t)},type:u,lazyToolbar:b,lazyHeader:()=>p().getOrDie("Could not find header element"),...t}),s=FD.parts["multiple-toolbar"]({dom:{tag:"div",classes:["tox-toolbar-overlord"]},providers:y.popup.shared.providers,onEscape:()=>{e.focus()},type:u}),r=tv(e),a=Qb(e),i=Kb(e),l=qb(e),c=FD.parts.promotion({dom:{tag:"div",classes:["tox-promotion"]}}),g=r||a||i,h=l?[c,o]:[o];return FD.parts.header({dom:{tag:"div",classes:["tox-editor-header"].concat(g?[]:["tox-editor-header--empty"]),...t},components:q([i?h:[],r?[s]:a?[n]:[],sv(e)?[]:[m.asSpec()]]),sticky:iv(e),editor:e,sharedBackstage:y.popup.shared})})(),n={dom:{tag:"div",classes:["tox-sidebar-wrap"]},components:[FD.parts.socket({dom:{tag:"div",classes:["tox-edit-area"]}}),FD.parts.sidebar({dom:{tag:"div",classes:["tox-sidebar"]}})]},s=FD.parts.throbber({dom:{tag:"div",classes:["tox-throbber"]},backstage:y.popup}),r=FD.parts.viewWrapper({backstage:y.popup}),i=Pb(e)&&!o?A.some(FI(e,y.popup.shared.providers)):A.none(),l=q([d?[]:[t],o?[]:[n],d?[t]:[]]),h=FD.parts.editorContainer({components:q([l,o?[]:[g.asSpec(),...i.toArray()]])}),f=av(e),v={role:"application",...$f.isRtl()?{dir:"rtl"}:{},...f?{"aria-hidden":"true"}:{}},x=ri(FD.sketch({dom:{tag:"div",classes:["tox","tox-tinymce"].concat(o?["tox-tinymce-inline"]:[]).concat(d?["tox-tinymce--toolbar-bottom"]:[]).concat(c),styles:{visibility:"hidden",...f?{opacity:"0",border:"0"}:{}},attributes:v},components:[h,...o?[]:[r],s],behaviours:kl([Sx(),Rm.config({disableClass:"tox-tinymce--disabled"}),Pp.config({mode:"cyclic",selector:".tox-menubar, .tox-toolbar, .tox-toolbar__primary, .tox-toolbar__overflow--open, .tox-sidebar__overflow--open, .tox-statusbar__path, .tox-statusbar__wordcount, .tox-statusbar__branding a, .tox-statusbar__resize-handle"})])})),w=nk(x);return a.set(w),{mothership:w,outerContainer:x}},w=t=>{const o=NB((e=>{const t=(e=>{const t=gb(e),o=fb(e),n=vb(e);return RB(t).map((e=>VB(e,o,n)))})(e);return t.getOr(gb(e))})(e)),n=NB((e=>zB(e).getOr(pb(e)))(e));return e.inline||(zt("div","width",n)&&Dt(t.element,"width",n),zt("div","height",o)?Dt(t.element,"height",o):Dt(t.element,"height","400px")),o};return{popups:{backstage:y.popup,getMothership:()=>II("popups",l)},dialogs:{backstage:y.dialog,getMothership:()=>II("dialogs",i)},renderUI:()=>{const o=x(),a=(()=>{const t=rv(e),o=Ze(xt(),t)&&"grid"===It(t,"display"),n={dom:{tag:"div",classes:["tox","tox-silver-sink","tox-tinymce-aux"].concat(c),attributes:{...$f.isRtl()?{dir:"rtl"}:{}}},behaviours:kl([Sd.config({useFixed:()=>s.isDocked(p)})])},r={dom:{styles:{width:document.body.clientWidth+"px"}},events:Hr([Ur(wr(),(e=>{Dt(e.element,"width",document.body.clientWidth+"px")}))])},a=ri(fn(n,o?r:{})),l=nk(a);return i.set(l),{sink:a,mothership:l}})(),d=lv(e)?(()=>{const e={dom:{tag:"div",classes:["tox","tox-silver-sink","tox-silver-popup-sink","tox-tinymce-aux"].concat(c),attributes:{...$f.isRtl()?{dir:"rtl"}:{}}},behaviours:kl([Sd.config({useFixed:()=>s.isDocked(p),getBounds:()=>t.getPopupSinkBounds()})])},o=ri(e),n=nk(o);return l.set(n),{sink:o,mothership:n}})():(e=>(l.set(e.mothership),e))(a);r.dialogUi.set(a),r.popupUi.set(d),r.mainUi.set(o);return(t=>{const{mainUi:o,popupUi:r,uiMotherships:a}=t;ce(Ob(e),((t,o)=>{e.ui.registry.addGroupToolbarButton(o,t)}));const{buttons:i,menuItems:l,contextToolbars:c,sidebars:d,views:m}=e.ui.registry.getAll(),g=ev(e),h={menuItems:l,menus:cv(e),menubar:Db(e),toolbar:g.getOrThunk((()=>Bb(e))),allowToolbarGroups:u===sb.floating,buttons:i,sidebar:d,views:m};var f;f=o.outerContainer,e.addShortcut("alt+F9","focus menubar",(()=>{FD.focusMenubar(f)})),e.addShortcut("alt+F10","focus toolbar",(()=>{FD.focusToolbar(f)})),e.addCommand("ToggleToolbarDrawer",((e,t)=>{(null==t?void 0:t.skipFocus)?FD.toggleToolbarDrawerWithoutFocusing(f):FD.toggleToolbarDrawer(f)})),e.addQueryStateHandler("ToggleToolbarDrawer",(()=>FD.isToolbarDrawerToggled(f))),((e,t,o)=>{const n=(e,n)=>{L([t,...o],(t=>{t.broadcastEvent(e,n)}))},s=(e,n)=>{L([t,...o],(t=>{t.broadcastOn([e],n)}))},r=e=>s(Yd(),{target:e.target}),a=$o(),i=tc(a,"touchstart",r),l=tc(a,"touchmove",(e=>n(vr(),e))),c=tc(a,"touchend",(e=>n(yr(),e))),d=tc(a,"mousedown",r),u=tc(a,"mouseup",(e=>{0===e.raw.button&&s(Jd(),{target:e.target})})),m=e=>s(Yd(),{target:Ve(e.target)}),g=e=>{0===e.button&&s(Jd(),{target:Ve(e.target)})},p=()=>{L(e.editorManager.get(),(t=>{e!==t&&t.dispatch("DismissPopups",{relatedTarget:e})}))},h=e=>n(xr(),nc(e)),f=e=>{s(Kd(),{}),n(wr(),nc(e))},b=ht(Ve(e.getElement())),v=oc(b,"scroll",(o=>{requestAnimationFrame((()=>{if(null!=e.getContainer()){const s=jS(e,t.element).map((e=>[e.element,...e.others])).getOr([]);N(s,(e=>Ze(e,o.target)))&&(e.dispatch("ElementScroll",{target:o.target.dom}),n(Er(),o))}}))})),y=()=>s(Kd(),{}),x=t=>{t.state&&s(Yd(),{target:Ve(e.getContainer())})},w=e=>{s(Yd(),{target:Ve(e.relatedTarget.getContainer())})};e.on("PostRender",(()=>{e.on("click",m),e.on("tap",m),e.on("mouseup",g),e.on("mousedown",p),e.on("ScrollWindow",h),e.on("ResizeWindow",f),e.on("ResizeEditor",y),e.on("AfterProgressState",x),e.on("DismissPopups",w)})),e.on("remove",(()=>{e.off("click",m),e.off("tap",m),e.off("mouseup",g),e.off("mousedown",p),e.off("ScrollWindow",h),e.off("ResizeWindow",f),e.off("ResizeEditor",y),e.off("AfterProgressState",x),e.off("DismissPopups",w),d.unbind(),i.unbind(),l.unbind(),c.unbind(),u.unbind(),v.unbind()})),e.on("detach",(()=>{L([t,...o],Vd),L([t,...o],(e=>e.destroy()))}))})(e,o.mothership,a),s.setup(e,y.popup.shared,p),bF(e,y.popup),zF(e,y.popup.shared.getSink,y.popup),(e=>{const{sidebars:t}=e.ui.registry.getAll();L(ae(t),(o=>{const n=t[o],s=()=>xe(A.from(e.queryCommandValue("ToggleSidebar")),o);e.ui.registry.addToggleButton(o,{icon:n.icon,tooltip:n.tooltip,onAction:t=>{e.execCommand("ToggleSidebar",!1,o),t.setActive(s())},onSetup:t=>{t.setActive(s());const o=()=>t.setActive(s());return e.on("ToggleSidebar",o),()=>{e.off("ToggleSidebar",o)}}})}))})(e),TM(e,v,y.popup.shared),lF(e,c,r.sink,{backstage:y.popup}),EI(e,r.sink);const b={targetNode:e.getElement(),height:w(o.outerContainer)};return n.render(e,t,h,y.popup,b)})({popupUi:d,dialogUi:a,mainUi:o,uiMotherships:r.getUiMotherships()})}}},NI=x([os("lazySink"),us("dragBlockClass"),Os("getBounds",en),ys("useTabstopAt",E),ys("firstTabstop",0),ys("eventOrder",{}),hu("modalBehaviours",[Pp]),Bi("onExecute"),Ii("onEscape")]),VI={sketch:w},zI=x([ju({name:"draghandle",overrides:(e,t)=>({behaviours:kl([CI.config({mode:"mouse",getTarget:e=>mi(e,'[role="dialog"]').getOr(e),blockerClass:e.dragBlockClass.getOrDie(new Error("The drag blocker class was not specified for a dialog with a drag handle: \n"+JSON.stringify(t,null,2)).message),getBounds:e.getDragBounds})])})}),Uu({schema:[os("dom")],name:"title"}),Uu({factory:VI,schema:[os("dom")],name:"close"}),Uu({factory:VI,schema:[os("dom")],name:"body"}),ju({factory:VI,schema:[os("dom")],name:"footer"}),Wu({factory:{sketch:(e,t)=>({...e,dom:t.dom,components:t.components})},schema:[ys("dom",{tag:"div",styles:{position:"fixed",left:"0px",top:"0px",right:"0px",bottom:"0px"}}),ys("components",[])],name:"blocker"})]),HI=bm({name:"ModalDialog",configFields:NI(),partFields:zI(),factory:(e,t,o,n)=>{const s=Ql(),r=la("modal-events"),a={...e.eventOrder,[Sr()]:[r].concat(e.eventOrder["alloy.system.attached"]||[])};return{uid:e.uid,dom:e.dom,components:t,apis:{show:t=>{s.set(t);const o=e.lazySink(t).getOrDie(),r=n.blocker(),a=o.getSystem().build({...r,components:r.components.concat([ai(t)]),behaviours:kl([oh.config({}),Jp("dialog-blocker-events",[Yr(Xs(),(()=>{OM.isBlocked(t)||Pp.focusIn(t)}))])])});Ad(o,a),Pp.focusIn(t)},hide:e=>{s.clear(),st(e.element).each((t=>{e.getSystem().getByDom(t).each((e=>{Bd(e)}))}))},getBody:t=>nm(t,e,"body"),getFooter:t=>om(t,e,"footer"),setIdle:e=>{OM.unblock(e)},setBusy:(e,t)=>{OM.block(e,t)}},eventOrder:a,domModification:{attributes:{role:"dialog","aria-modal":"true"}},behaviours:bu(e.modalBehaviours,[Kp.config({}),Pp.config({mode:"cyclic",onEnter:e.onExecute,onEscape:e.onEscape,useTabstopAt:e.useTabstopAt,firstTabstop:e.firstTabstop}),OM.config({getRoot:s.get}),Jp(r,[Kr((t=>{((e,t)=>{const o=_t(e,"id").fold((()=>{const e=la("dialog-label");return kt(t,"id",e),e}),w);kt(e,"aria-labelledby",o)})(t.element,nm(t,e,"title").element)}))])])}},apis:{show:(e,t)=>{e.show(t)},hide:(e,t)=>{e.hide(t)},getBody:(e,t)=>e.getBody(t),getFooter:(e,t)=>e.getFooter(t),setBusy:(e,t,o)=>{e.setBusy(t,o)},setIdle:(e,t)=>{e.setIdle(t)}}}),LI=Dn([ty,oy].concat(Jy)),PI=Ln,UI=[Ey("button"),hy,ks("align","end",["start","end"]),ky,Sy,hs("buttonType",["primary","secondary"])],WI=[...UI,sy],jI=[as("type",["submit","cancel","custom"]),...WI],GI=[as("type",["menu"]),py,fy,hy,ds("items",LI),...UI],$I=[...UI,as("type",["togglebutton"]),rs("tooltip"),hy,py,Cs("active",!1)],qI=Jn("type",{submit:jI,cancel:jI,custom:jI,menu:GI,togglebutton:$I}),XI=[ty,sy,as("level",["info","warn","error","success"]),ay,ys("url","")],YI=Dn(XI),KI=[ty,sy,Sy,Ey("button"),hy,wy,hs("buttonType",["primary","secondary","toolbar"]),ky],JI=Dn(KI),ZI=[ty,oy],QI=ZI.concat([by]),eR=ZI.concat([ny,Sy]),tR=Dn(eR),oR=Ln,nR=QI.concat([Cy("auto")]),sR=Dn(nR),rR=Rn([iy,sy,ay]),aR=QI.concat([Ss("storageKey","default")]),iR=Dn(aR),lR=Hn,cR=Dn(QI),dR=Hn,uR=ZI.concat([Ss("tag","textarea"),rs("scriptId"),rs("scriptUrl"),xs("settings",void 0,Wn)]),mR=ZI.concat([Ss("tag","textarea"),is("init")]),gR=Gn((e=>qn("customeditor.old",Mn(mR),e).orThunk((()=>qn("customeditor.new",Mn(uR),e))))),pR=Hn,hR=Dn(QI),fR=Bn(On),bR=e=>[ty,ss("columns"),e],vR=[ty,rs("html"),ks("presets","presentation",["presentation","document"])],yR=Dn(vR),xR=QI.concat([Cs("border",!1),Cs("sandboxed",!0),Cs("streamContent",!1),Cs("transparent",!0)]),wR=Dn(xR),SR=Hn,kR=Dn(ZI.concat([ps("height")])),CR=Dn([rs("url"),gs("zoom"),gs("cachedWidth"),gs("cachedHeight")]),OR=QI.concat([ps("inputMode"),ps("placeholder"),Cs("maximized",!1),Sy]),_R=Dn(OR),TR=Hn,ER=e=>[ty,ny,e,ks("align","start",["start","center","end"])],AR=[sy,iy],MR=[sy,ds("items",Zn(0,(()=>DR)))],DR=Fn([Dn(AR),Dn(MR)]),BR=QI.concat([ds("items",DR),Sy]),FR=Dn(BR),IR=Hn,RR=QI.concat([cs("items",[sy,iy]),ws("size",1),Sy]),NR=Dn(RR),VR=Hn,zR=QI.concat([Cs("constrain",!0),Sy]),HR=Dn(zR),LR=Dn([rs("width"),rs("height")]),PR=ZI.concat([ny,ws("min",0),ws("max",0)]),UR=Dn(PR),WR=zn,jR=[ty,ds("header",Hn),ds("cells",Bn(Hn))],GR=Dn(jR),$R=QI.concat([ps("placeholder"),Cs("maximized",!1),Sy]),qR=Dn($R),XR=Hn,YR=[as("type",["directory","leaf"]),ry,rs("id"),ms("menu",rM)],KR=Dn(YR),JR=YR.concat([ds("children",Zn(0,(()=>jn("type",{directory:ZR,leaf:KR}))))]),ZR=Dn(JR),QR=jn("type",{directory:ZR,leaf:KR}),eN=[ty,ds("items",QR),fs("onLeafAction"),fs("onToggleExpand"),_s("defaultExpandedIds",[],Hn),ps("defaultSelectedId")],tN=Dn(eN),oN=QI.concat([ks("filetype","file",["image","media","file"]),Sy]),nN=Dn(oN),sN=Dn([iy,Oy]),rN=e=>Qn("items","items",{tag:"required",process:{}},Bn(Gn((t=>qn(`Checking item of ${e}`,aN,t).fold((e=>sn.error(Kn(e))),(e=>sn.value(e))))))),aN=En((()=>{return jn("type",{alertbanner:YI,bar:Dn((e=rN("bar"),[ty,e])),button:JI,checkbox:tR,colorinput:iR,colorpicker:cR,dropzone:hR,grid:Dn(bR(rN("grid"))),iframe:wR,input:_R,listbox:FR,selectbox:NR,sizeinput:HR,slider:UR,textarea:qR,urlinput:nN,customeditor:gR,htmlpanel:yR,imagepreview:kR,collection:sR,label:Dn(ER(rN("label"))),table:GR,tree:tN,panel:lN});var e})),iN=[ty,ys("classes",[]),ds("items",aN)],lN=Dn(iN),cN=[Ey("tab"),ry,ds("items",aN)],dN=[ty,cs("tabs",cN)],uN=Dn(dN),mN=WI,gN=qI,pN=Dn([rs("title"),ns("body",jn("type",{panel:lN,tabpanel:uN})),Ss("size","normal"),_s("buttons",[],gN),ys("initialData",{}),Os("onAction",b),Os("onChange",b),Os("onSubmit",b),Os("onClose",b),Os("onCancel",b),Os("onTabChange",b)]),hN=Dn([as("type",["cancel","custom"]),...mN]),fN=Dn([rs("title"),rs("url"),gs("height"),gs("width"),bs("buttons",hN),Os("onAction",b),Os("onCancel",b),Os("onClose",b),Os("onMessage",b)]),bN=e=>a(e)?[e].concat(X(fe(e),bN)):l(e)?X(e,bN):[],vN=e=>r(e.type)&&r(e.name),yN={checkbox:oR,colorinput:lR,colorpicker:dR,dropzone:fR,input:TR,iframe:SR,imagepreview:CR,selectbox:VR,sizeinput:LR,slider:WR,listbox:IR,size:LR,textarea:XR,urlinput:sN,customeditor:pR,collection:rR,togglemenuitem:PI},xN=e=>{const t=(e=>U(bN(e),vN))(e),o=X(t,(e=>(e=>A.from(yN[e.type]))(e).fold((()=>[]),(t=>[ns(e.name,t)]))));return Dn(o)},wN=e=>{var t;return{internalDialog:Xn(qn("dialog",pN,e)),dataValidator:xN(e),initialData:null!==(t=e.initialData)&&void 0!==t?t:{}}},SN={open:(e,t)=>{const o=wN(t);return e(o.internalDialog,o.initialData,o.dataValidator)},openUrl:(e,t)=>e(Xn(qn("dialog",fN,t))),redial:e=>wN(e)};var kN=Object.freeze({__proto__:null,events:(e,t)=>{const o=(o,n)=>{e.updateState.each((e=>{const s=e(o,n);t.set(s)})),e.renderComponents.each((s=>{const r=s(n,t.get());(e.reuseDom?Wp:Up)(o,r)}))};return Hr([Ur(dr(),((t,n)=>{const s=n;if(!s.universal){const n=e.channel;R(s.channels,n)&&o(t,s.data)}})),Kr(((t,n)=>{e.initialData.each((e=>{o(t,e)}))}))])}}),CN=Object.freeze({__proto__:null,getState:(e,t,o)=>o}),ON=[os("channel"),us("renderComponents"),us("updateState"),us("initialData"),Cs("reuseDom",!0)];const _N=Ol({fields:ON,name:"reflecting",active:kN,apis:CN,state:Object.freeze({__proto__:null,init:()=>{const e=Es(A.none());return{readState:()=>e.get().getOr("none"),get:e.get,set:e.set,clear:()=>e.set(A.none())}}})}),TN=e=>{const t=[],o={};return le(e,((e,n)=>{e.fold((()=>{t.push(n)}),(e=>{o[n]=e}))})),t.length>0?sn.error(t):sn.value(o)},EN=(e,t,o)=>{const n=Gh(CO.sketch((n=>({dom:{tag:"div",classes:["tox-form"].concat(e.classes)},components:H(e.items,(e=>XT(n,e,t,o)))}))));return{dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[n.asSpec()]}],behaviours:kl([Pp.config({mode:"acyclic",useTabstopAt:C(XO)}),(s=n,wm.config({find:s.getOpt})),IO(n,{postprocess:e=>TN(e).fold((e=>(console.error(e),{})),w)}),Jp("dialog-body-panel",[Ur(Xs(),((e,t)=>{e.getSystem().broadcastOn([e_],{newFocus:A.some(t.event.target)})}))])])};var s},AN=fm({name:"TabButton",configFields:[ys("uid",void 0),os("value"),Qn("dom","dom",xn((()=>({attributes:{role:"tab",id:la("aria"),"aria-selected":"false"}}))),Nn()),us("action"),ys("domModification",{}),hu("tabButtonBehaviours",[oh,Pp,pu]),os("view")],factory:(e,t)=>({uid:e.uid,dom:e.dom,components:e.components,events:mh(e.action),behaviours:bu(e.tabButtonBehaviours,[oh.config({}),Pp.config({mode:"execution",useSpace:!0,useEnter:!0}),pu.config({store:{mode:"memory",initialValue:e.value}})]),domModification:e.domModification})}),MN=x([os("tabs"),os("dom"),ys("clickToDismiss",!1),hu("tabbarBehaviours",[Gm,Pp]),Ai(["tabClass","selectedClass"])]),DN=Gu({factory:AN,name:"tabs",unit:"tab",overrides:e=>{const t=(e,t)=>{Gm.dehighlight(e,t),Ir(e,Mr(),{tabbar:e,button:t})},o=(e,t)=>{Gm.highlight(e,t),Ir(e,Ar(),{tabbar:e,button:t})};return{action:n=>{const s=n.getSystem().getByUid(e.uid).getOrDie(),r=Gm.isHighlighted(s,n);(r&&e.clickToDismiss?t:r?b:o)(s,n)},domModification:{classes:[e.markers.tabClass]}}}}),BN=x([DN]),FN=bm({name:"Tabbar",configFields:MN(),partFields:BN(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,"debug.sketcher":"Tabbar",domModification:{attributes:{role:"tablist"}},behaviours:bu(e.tabbarBehaviours,[Gm.config({highlightClass:e.markers.selectedClass,itemClass:e.markers.tabClass,onHighlight:(e,t)=>{kt(t.element,"aria-selected","true")},onDehighlight:(e,t)=>{kt(t.element,"aria-selected","false")}}),Pp.config({mode:"flow",getInitial:e=>Gm.getHighlighted(e).map((e=>e.element)),selector:"."+e.markers.tabClass,executeOnMove:!0})])})}),IN=fm({name:"Tabview",configFields:[hu("tabviewBehaviours",[Kp])],factory:(e,t)=>({uid:e.uid,dom:e.dom,behaviours:bu(e.tabviewBehaviours,[Kp.config({})]),domModification:{attributes:{role:"tabpanel"}}})}),RN=x([ys("selectFirst",!0),Di("onChangeTab"),Di("onDismissTab"),ys("tabs",[]),hu("tabSectionBehaviours",[])]),NN=Uu({factory:FN,schema:[os("dom"),ls("markers",[os("tabClass"),os("selectedClass")])],name:"tabbar",defaults:e=>({tabs:e.tabs})}),VN=Uu({factory:IN,name:"tabview"}),zN=x([NN,VN]),HN=bm({name:"TabSection",configFields:RN(),partFields:zN(),factory:(e,t,o,n)=>{const s=(t,o)=>{om(t,e,"tabbar").each((e=>{o(e).each(Rr)}))};return{uid:e.uid,dom:e.dom,components:t,behaviours:fu(e.tabSectionBehaviours),events:Hr(q([e.selectFirst?[Kr(((e,t)=>{s(e,Gm.getFirst)}))]:[],[Ur(Ar(),((t,o)=>{(t=>{const o=pu.getValue(t);om(t,e,"tabview").each((n=>{G(e.tabs,(e=>e.value===o)).each((o=>{const s=o.view();_t(t.element,"id").each((e=>{kt(n.element,"aria-labelledby",e)})),Kp.set(n,s),e.onChangeTab(n,t,s)}))}))})(o.event.button)})),Ur(Mr(),((t,o)=>{const n=o.event.button;e.onDismissTab(t,n)}))]])),apis:{getViewItems:t=>om(t,e,"tabview").map((e=>Kp.contents(e))).getOr([]),showTab:(e,t)=>{s(e,(e=>{const o=Gm.getCandidates(e);return G(o,(e=>pu.getValue(e)===t)).filter((t=>!Gm.isHighlighted(e,t)))}))}}}},apis:{getViewItems:(e,t)=>e.getViewItems(t),showTab:(e,t,o)=>{e.showTab(t,o)}}}),LN=(e,t)=>{Dt(e,"height",t+"px"),Dt(e,"flex-basis",t+"px")},PN=(e,t,o)=>{mi(e,'[role="dialog"]').each((e=>{pi(e,'[role="tablist"]').each((n=>{o.get().map((o=>(Dt(t,"height","0"),Dt(t,"flex-basis","0"),Math.min(o,((e,t,o)=>{const n=ot(e).dom,s=mi(e,".tox-dialog-wrap").getOr(e);let r;r="fixed"===It(s,"position")?Math.max(n.clientHeight,window.innerHeight):Math.max(n.offsetHeight,n.scrollHeight);const a=Wt(t),i=t.dom.offsetLeft>=o.dom.offsetLeft+Jt(o)?Math.max(Wt(o),a):a,l=parseInt(It(e,"margin-top"),10)||0,c=parseInt(It(e,"margin-bottom"),10)||0;return r-(Wt(e)+l+c-i)})(e,t,n))))).each((e=>{LN(t,e)}))}))}))},UN=e=>pi(e,'[role="tabpanel"]'),WN="send-data-to-section",jN="send-data-to-view",GN=(e,t,o)=>{const n=Es({}),s=e=>{const t=pu.getValue(e),o=TN(t).getOr({}),s=n.get(),r=fn(s,o);n.set(r)},r=e=>{const t=n.get();pu.setValue(e,t)},a=Es(null),i=H(e.tabs,(e=>({value:e.name,dom:{tag:"div",classes:["tox-dialog__body-nav-item"]},components:[ti(o.shared.providers.translate(e.title))],view:()=>[CO.sketch((n=>({dom:{tag:"div",classes:["tox-form"]},components:H(e.items,(e=>XT(n,e,t,o))),formBehaviours:kl([Pp.config({mode:"acyclic",useTabstopAt:C(XO)}),Jp("TabView.form.events",[Kr(r),Jr(s)]),Al.config({channels:Ds([{key:WN,value:{onReceive:s}},{key:jN,value:{onReceive:r}}])})])})))]}))),l=(e=>{const t=Ql(),o=[Kr((o=>{const n=o.element;UN(n).each((s=>{Dt(s,"visibility","hidden"),o.getSystem().getByDom(s).toOptional().each((o=>{const n=((e,t,o)=>H(e,((n,s)=>{Kp.set(o,e[s].view());const r=t.dom.getBoundingClientRect();return Kp.set(o,[]),r.height})))(e,s,o),r=(e=>oe(ee(e,((e,t)=>e>t?-1:e<t?1:0))))(n);r.fold(t.clear,t.set)})),PN(n,s,t),Ht(s,"visibility"),((e,t)=>{oe(e).each((e=>HN.showTab(t,e.value)))})(e,o),requestAnimationFrame((()=>{PN(n,s,t)}))}))})),Ur(wr(),(e=>{const o=e.element;UN(o).each((e=>{PN(o,e,t)}))})),Ur(kk,((e,o)=>{const n=e.element;UN(n).each((e=>{const o=Il(ht(e));Dt(e,"visibility","hidden");const s=Nt(e,"height").map((e=>parseInt(e,10)));Ht(e,"height"),Ht(e,"flex-basis");const r=e.dom.getBoundingClientRect().height;s.forall((e=>r>e))?(t.set(r),PN(n,e,t)):s.each((t=>{LN(e,t)})),Ht(e,"visibility"),o.each(Dl)}))}))];return{extraEvents:o,selectFirst:!1}})(i);return HN.sketch({dom:{tag:"div",classes:["tox-dialog__body"]},onChangeTab:(e,t,o)=>{const n=pu.getValue(t);Ir(e,Sk,{name:n,oldName:a.get()}),a.set(n)},tabs:i,components:[HN.parts.tabbar({dom:{tag:"div",classes:["tox-dialog__body-nav"]},components:[FN.parts.tabs({})],markers:{tabClass:"tox-tab",selectedClass:"tox-dialog__body-nav-item--active"},tabbarBehaviours:kl([ck.config({})])}),HN.parts.tabview({dom:{tag:"div",classes:["tox-dialog__body-content"]}})],selectFirst:l.selectFirst,tabSectionBehaviours:kl([Jp("tabpanel",l.extraEvents),Pp.config({mode:"acyclic"}),wm.config({find:e=>oe(HN.getViewItems(e))}),RO(A.none(),(e=>(e.getSystem().broadcastOn([WN],{}),n.get())),((e,t)=>{n.set(t),e.getSystem().broadcastOn([jN],{})}))])})},$N=(e,t,o,n,s)=>({dom:{tag:"div",classes:["tox-dialog__content-js"],attributes:{...o.map((e=>({id:e}))).getOr({}),...s?{"aria-live":"polite"}:{}}},components:[],behaviours:kl([BO(0),_N.config({channel:`${JO}-${t}`,updateState:(e,t)=>A.some({isTabPanel:()=>"tabpanel"===t.body.type}),renderComponents:e=>{const t=e.body;return"tabpanel"===t.type?[GN(t,e.initialData,n)]:[EN(t,e.initialData,n)]},initialData:e})])}),qN=lb.deviceType.isTouch(),XN=(e,t)=>({dom:{tag:"div",styles:{display:"none"},classes:["tox-dialog__header"]},components:[e,t]}),YN=(e,t)=>HI.parts.close(Wh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":t.translate("Close")}},action:e,buttonBehaviours:kl([ck.config({})])})),KN=()=>HI.parts.title({dom:{tag:"div",classes:["tox-dialog__title"],innerHtml:"",styles:{display:"none"}}}),JN=(e,t)=>HI.parts.body({dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[{dom:jh(`<p>${Gf(t.translate(e))}</p>`)}]}]}),ZN=e=>HI.parts.footer({dom:{tag:"div",classes:["tox-dialog__footer"]},components:e}),QN=(e,t)=>[ok.sketch({dom:{tag:"div",classes:["tox-dialog__footer-start"]},components:e}),ok.sketch({dom:{tag:"div",classes:["tox-dialog__footer-end"]},components:t})],eV=e=>{const t="tox-dialog",o=t+"-wrap",n=o+"__backdrop",s=t+"__disable-scroll";return HI.sketch({lazySink:e.lazySink,onEscape:t=>(e.onEscape(t),A.some(!0)),useTabstopAt:e=>!XO(e),firstTabstop:e.firstTabstop,dom:{tag:"div",classes:[t].concat(e.extraClasses),styles:{position:"relative",...e.extraStyles}},components:[e.header,e.body,...e.footer.toArray()],parts:{blocker:{dom:jh(`<div class="${o}"></div>`),components:[{dom:{tag:"div",classes:qN?[n,n+"--opaque"]:[n]}}]}},dragBlockClass:o,modalBehaviours:kl([oh.config({}),Jp("dialog-events",e.dialogEvents.concat([Yr(Xs(),((e,t)=>{OM.isBlocked(e)||Pp.focusIn(e)})),Ur(_r(),((e,t)=>{e.getSystem().broadcastOn([e_],{newFocus:t.event.newFocus})}))])),Jp("scroll-lock",[Kr((()=>{La(xt(),s)})),Jr((()=>{Pa(xt(),s)}))]),...e.extraBehaviours]),eventOrder:{[ur()]:["dialog-events"],[Sr()]:["scroll-lock","dialog-events","alloy.base.behaviour"],[kr()]:["alloy.base.behaviour","dialog-events","scroll-lock"],...e.eventOrder}})},tV=e=>Wh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":e.translate("Close"),title:e.translate("Close")}},buttonBehaviours:kl([ck.config({})]),components:[tb("close",{tag:"span",classes:["tox-icon"]},e.icons)],action:e=>{Fr(e,bk)}}),oV=(e,t,o,n)=>({dom:{tag:"div",classes:["tox-dialog__title"],attributes:{...o.map((e=>({id:e}))).getOr({})}},components:[],behaviours:kl([_N.config({channel:`${KO}-${t}`,initialData:e,renderComponents:e=>[ti(n.translate(e.title))]})])}),nV=()=>({dom:jh('<div class="tox-dialog__draghandle"></div>')}),sV=(e,t,o)=>((e,t,o)=>{const n=HI.parts.title(oV(e,t,A.none(),o)),s=HI.parts.draghandle(nV()),r=HI.parts.close(tV(o)),a=[n].concat(e.draggable?[s]:[]).concat([r]);return ok.sketch({dom:jh('<div class="tox-dialog__header"></div>'),components:a})})({title:o.shared.providers.translate(e),draggable:o.dialog.isDraggableModal()},t,o.shared.providers),rV=(e,t,o,n)=>({dom:{tag:"div",classes:["tox-dialog__busy-spinner"],attributes:{"aria-label":o.translate(e)},styles:{left:"0px",right:"0px",bottom:"0px",top:`${n.getOr(0)}px`,position:"absolute"}},behaviours:t,components:[{dom:jh('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}),aV=(e,t,o)=>({onClose:()=>o.closeWindow(),onBlock:o=>{const n=pi(e().element,".tox-dialog__header").map((e=>Wt(e)));HI.setBusy(e(),((e,s)=>rV(o.message,s,t,n)))},onUnblock:()=>{HI.setIdle(e())}}),iV=(e,t,o,n)=>ri(eV({...e,firstTabstop:1,lazySink:n.shared.getSink,extraBehaviours:[_N.config({channel:`${YO}-${e.id}`,updateState:(e,t)=>A.some(t),initialData:t}),VO({}),...e.extraBehaviours],onEscape:e=>{Fr(e,bk)},dialogEvents:o,eventOrder:{[dr()]:[_N.name(),Al.name()],[Sr()]:["scroll-lock",_N.name(),"messages","dialog-events","alloy.base.behaviour"],[kr()]:["alloy.base.behaviour","dialog-events","messages",_N.name(),"scroll-lock"]}})),lV=(e,t={})=>H(e,(e=>"menu"===e.type?(e=>{const o=H(e.items,(e=>{const o=be(t,e.name).getOr(Es(!1));return{...e,storage:o}}));return{...e,items:o}})(e):e)),cV=e=>j(e,((e,t)=>"menu"===t.type?j(t.items,((e,t)=>(e[t.name]=t.storage,e)),e):e),{}),dV=(e,t)=>[$r(Xs(),qO),e(fk,((e,o,n,s)=>{Il(ht(s.element)).fold(b,Bl),t.onClose(),o.onClose()})),e(bk,((e,t,o,n)=>{t.onCancel(e),Fr(n,fk)})),Ur(wk,((e,o)=>t.onUnblock())),Ur(xk,((e,o)=>t.onBlock(o.event)))],uV=(e,t,o)=>{const n=(t,o)=>Ur(t,((t,n)=>{s(t,((s,r)=>{o(e(),s,n.event,t)}))})),s=(e,t)=>{_N.getState(e).get().each((o=>{t(o.internalDialog,e)}))};return[...dV(n,t),n(yk,((e,t)=>t.onSubmit(e))),n(hk,((e,t,o)=>{t.onChange(e,{name:o.name})})),n(vk,((e,t,n,s)=>{const r=()=>s.getSystem().isConnected()?Pp.focusIn(s):void 0,a=e=>Tt(e,"disabled")||_t(e,"aria-disabled").exists((e=>"true"===e)),i=ht(s.element),l=Il(i);t.onAction(e,{name:n.name,value:n.value}),Il(i).fold(r,(e=>{a(e)||l.exists((t=>Qe(e,t)&&a(t)))?r():o().toOptional().filter((t=>!Qe(t.element,e))).each(r)}))})),n(Sk,((e,t,o)=>{t.onTabChange(e,{newTabName:o.name,oldTabName:o.oldName})})),Jr((t=>{const o=e();pu.setValue(t,o.getData())}))]},mV=(e,t)=>{const o=t.map((e=>e.footerButtons)).getOr([]),n=P(o,(e=>"start"===e.align)),s=(e,t)=>ok.sketch({dom:{tag:"div",classes:[`tox-dialog__footer-${e}`]},components:H(t,(e=>e.memento.asSpec()))});return[s("start",n.pass),s("end",n.fail)]},gV=(e,t,o)=>({dom:jh('<div class="tox-dialog__footer"></div>'),components:[],behaviours:kl([_N.config({channel:`${ZO}-${t}`,initialData:e,updateState:(e,t)=>{const n=H(t.buttons,(e=>{const t=Gh(((e,t)=>BT(e,e.type,t))(e,o));return{name:e.name,align:e.align,memento:t}}));return A.some({lookupByName:t=>((e,t,o)=>G(t,(e=>e.name===o)).bind((t=>t.memento.getOpt(e))))(e,n,t),footerButtons:n})},renderComponents:mV})])}),pV=(e,t,o)=>HI.parts.footer(gV(e,t,o)),hV=(e,t)=>{if(e.getRoot().getSystem().isConnected()){const o=wm.getCurrent(e.getFormWrapper()).getOr(e.getFormWrapper());return CO.getField(o,t).orThunk((()=>{const o=e.getFooter().bind((e=>_N.getState(e).get()));return o.bind((e=>e.lookupByName(t)))}))}return A.none()},fV=(e,t,o)=>{const n=t=>{const o=e.getRoot();o.getSystem().isConnected()&&t(o)},s={getData:()=>{const t=e.getRoot(),n=t.getSystem().isConnected()?e.getFormWrapper():t;return{...pu.getValue(n),...ce(o,(e=>e.get()))}},setData:t=>{n((n=>{const r=s.getData(),a=fn(r,t),i=((e,t)=>{const o=e.getRoot();return _N.getState(o).get().map((e=>Xn(qn("data",e.dataValidator,t)))).getOr(t)})(e,a),l=e.getFormWrapper();pu.setValue(l,i),le(o,((e,t)=>{ve(a,t)&&e.set(a[t])}))}))},setEnabled:(t,o)=>{hV(e,t).each(o?Rm.enable:Rm.disable)},focus:t=>{hV(e,t).each(oh.focus)},block:e=>{if(!r(e))throw new Error("The dialogInstanceAPI.block function should be passed a blocking message of type string as an argument");n((t=>{Ir(t,xk,{message:e})}))},unblock:()=>{n((e=>{Fr(e,wk)}))},showTab:t=>{n((o=>{const n=e.getBody();_N.getState(n).get().exists((e=>e.isTabPanel()))&&wm.getCurrent(n).each((e=>{HN.showTab(e,t)}))}))},redial:r=>{n((n=>{const a=e.getId(),i=t(r),l=lV(i.internalDialog.buttons,o);n.getSystem().broadcastOn([`${YO}-${a}`],i),n.getSystem().broadcastOn([`${KO}-${a}`],i.internalDialog),n.getSystem().broadcastOn([`${JO}-${a}`],i.internalDialog),n.getSystem().broadcastOn([`${ZO}-${a}`],{...i.internalDialog,buttons:l}),s.setData(i.initialData)}))},close:()=>{n((e=>{Fr(e,fk)}))},toggleFullscreen:e.toggleFullscreen};return s},bV=(e,t,o,n=!1)=>{const s=la("dialog"),r=la("dialog-label"),a=la("dialog-content"),i=e.internalDialog,l="medium"===i.size?A.some("tox-dialog--width-md"):A.none(),c=Gh(((e,t,o,n)=>ok.sketch({dom:jh('<div class="tox-dialog__header"></div>'),components:[oV(e,t,A.some(o),n),nV(),tV(n)],containerBehaviours:kl([CI.config({mode:"mouse",blockerClass:"blocker",getTarget:e=>hi(e,'[role="dialog"]').getOrDie(),snaps:{getSnapPoints:()=>[],leftAttr:"data-drag-left",topAttr:"data-drag-top"}})])}))({title:i.title,draggable:!0},s,r,o.shared.providers)),d=Gh(((e,t,o,n,s)=>$N(e,t,A.some(o),n,s))({body:i.body,initialData:i.initialData},s,a,o,n)),u=lV(i.buttons),m=cV(u),g=Ce(0!==u.length,Gh(((e,t,o)=>gV(e,t,o))({buttons:u},s,o))),p=uV((()=>b),{onBlock:e=>{OM.block(f,((t,n)=>{const s=c.getOpt(f).map((e=>Wt(e.element)));return rV(e.message,n,o.shared.providers,s)}))},onUnblock:()=>{OM.unblock(f)},onClose:()=>t.closeWindow()},o.shared.getSink),h="tox-dialog-inline",f=ri({dom:{tag:"div",classes:["tox-dialog",h,...l.toArray()],attributes:{role:"dialog","aria-labelledby":r}},eventOrder:{[dr()]:[_N.name(),Al.name()],[ur()]:["execute-on-form"],[Sr()]:["reflecting","execute-on-form"]},behaviours:kl([Pp.config({mode:"cyclic",onEscape:e=>(Fr(e,fk),A.some(!0)),useTabstopAt:e=>!XO(e)&&("button"!==Ue(e)||"disabled"!==Ot(e,"disabled")),firstTabstop:1}),_N.config({channel:`${YO}-${s}`,updateState:(e,t)=>A.some(t),initialData:e}),oh.config({}),Jp("execute-on-form",p.concat([Yr(Xs(),((e,t)=>{Pp.focusIn(e)})),Ur(_r(),((e,t)=>{e.getSystem().broadcastOn([e_],{newFocus:t.event.newFocus})}))])),OM.config({getRoot:()=>A.some(f)}),Kp.config({}),VO({})]),components:[c.asSpec(),d.asSpec(),...g.map((e=>e.asSpec())).toArray()]}),b=fV({getId:x(s),getRoot:x(f),getFooter:()=>g.map((e=>e.get(f))),getBody:()=>d.get(f),getFormWrapper:()=>{const e=d.get(f);return wm.getCurrent(e).getOr(e)},toggleFullscreen:()=>{const e="tox-dialog--fullscreen",t=Ve(f.element.dom);Ga(t,[e])?(ja(t,[e]),Wa(t,[h])):(ja(t,[h]),Wa(t,[e]))}},t.redial,m);return{dialog:f,instanceApi:b}};var vV=tinymce.util.Tools.resolve("tinymce.util.URI");const yV=["insertContent","setContent","execCommand","close","block","unblock"],xV=e=>a(e)&&-1!==yV.indexOf(e.mceAction),wV=(e,t,o,n)=>{const s=la("dialog"),i=sV(e.title,s,n),l=(e=>{const t={dom:{tag:"div",classes:["tox-dialog__content-js"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-iframe"]},components:[GO(A.none(),{dom:{tag:"iframe",attributes:{src:e.url}},behaviours:kl([ck.config({}),oh.config({})])})]}],behaviours:kl([Pp.config({mode:"acyclic",useTabstopAt:C(XO)})])};return HI.parts.body(t)})(e),c=e.buttons.bind((e=>0===e.length?A.none():A.some(pV({buttons:e},s,n)))),u=((e,t)=>{const o=(e,t)=>Ur(e,((e,o)=>{n(e,((n,s)=>{t(x,n,o.event,e)}))})),n=(e,t)=>{_N.getState(e).get().each((o=>{t(o,e)}))};return[...dV(o,t),o(vk,((e,t,o)=>{t.onAction(e,{name:o.name})}))]})(0,aV((()=>y),n.shared.providers,t)),m={...e.height.fold((()=>({})),(e=>({height:e+"px","max-height":e+"px"}))),...e.width.fold((()=>({})),(e=>({width:e+"px","max-width":e+"px"})))},p=e.width.isNone()&&e.height.isNone()?["tox-dialog--width-lg"]:[],h=new vV(e.url,{base_uri:new vV(window.location.href)}),f=`${h.protocol}://${h.host}${h.port?":"+h.port:""}`,b=Zl(),v=[Jp("messages",[Kr((()=>{const t=tc(Ve(window),"message",(t=>{if(h.isSameOrigin(new vV(t.raw.origin))){const n=t.raw.data;xV(n)?((e,t,o)=>{switch(o.mceAction){case"insertContent":e.insertContent(o.content);break;case"setContent":e.setContent(o.content);break;case"execCommand":const n=!!d(o.ui)&&o.ui;e.execCommand(o.cmd,n,o.value);break;case"close":t.close();break;case"block":t.block(o.message);break;case"unblock":t.unblock()}})(o,x,n):(e=>!xV(e)&&a(e)&&ve(e,"mceAction"))(n)&&e.onMessage(x,n)}}));b.set(t)})),Jr(b.clear)]),Al.config({channels:{[QO]:{onReceive:(e,t)=>{pi(e.element,"iframe").each((e=>{const o=e.dom.contentWindow;g(o)&&o.postMessage(t,f)}))}}}})],y=iV({id:s,header:i,body:l,footer:c,extraClasses:p,extraBehaviours:v,extraStyles:m},e,u,n),x=(e=>{const t=t=>{e.getSystem().isConnected()&&t(e)};return{block:e=>{if(!r(e))throw new Error("The urlDialogInstanceAPI.block function should be passed a blocking message of type string as an argument");t((t=>{Ir(t,xk,{message:e})}))},unblock:()=>{t((e=>{Fr(e,wk)}))},close:()=>{t((e=>{Fr(e,fk)}))},sendMessage:e=>{t((t=>{t.getSystem().broadcastOn([QO],e)}))}}})(y);return{dialog:y,instanceApi:x}},SV=(e,t)=>Xn(qn("data",t,e)),kV=e=>$S(e,".tox-alert-dialog")||$S(e,".tox-confirm-dialog"),CV=(e,t,o)=>t&&o?[]:[$A.config({contextual:{lazyContext:()=>A.some(Jo(Ve(e.getContentAreaContainer()))),fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top"],lazyViewport:t=>jS(e,t.element).map((e=>({bounds:GS(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:Xt(e.element).top})}))).getOrThunk((()=>({bounds:en(),optScrollEnv:A.none()})))})],OV=e=>{const t=e.editor,o=iv(t),n=(e=>{const t=e.shared;return{open:(o,n)=>{const s=()=>{HI.hide(l),n()},r=Gh(BT({name:"close-alert",text:"OK",primary:!0,buttonType:A.some("primary"),align:"end",enabled:!0,icon:A.none()},"cancel",e)),a=KN(),i=YN(s,t.providers),l=ri(eV({lazySink:()=>t.getSink(),header:XN(a,i),body:JN(o,t.providers),footer:A.some(ZN(QN([],[r.asSpec()]))),onEscape:s,extraClasses:["tox-alert-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Ur(bk,s)],eventOrder:{}}));HI.show(l);const c=r.get(l);oh.focus(c)}}})(e.backstages.dialog),s=(e=>{const t=e.shared;return{open:(o,n)=>{const s=e=>{HI.hide(c),n(e)},r=Gh(BT({name:"yes",text:"Yes",primary:!0,buttonType:A.some("primary"),align:"end",enabled:!0,icon:A.none()},"submit",e)),a=BT({name:"no",text:"No",primary:!1,buttonType:A.some("secondary"),align:"end",enabled:!0,icon:A.none()},"cancel",e),i=KN(),l=YN((()=>s(!1)),t.providers),c=ri(eV({lazySink:()=>t.getSink(),header:XN(i,l),body:JN(o,t.providers),footer:A.some(ZN(QN([],[a,r.asSpec()]))),onEscape:()=>s(!1),extraClasses:["tox-confirm-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Ur(bk,(()=>s(!1))),Ur(yk,(()=>s(!0)))],eventOrder:{}}));HI.show(c);const d=r.get(c);oh.focus(d)}}})(e.backstages.dialog),r=(t,o)=>SN.open(((t,n,s)=>{const r=n,a=((e,t,o)=>{const n=la("dialog"),s=e.internalDialog,r=sV(s.title,n,o),a=((e,t,o)=>{const n=$N(e,t,A.none(),o,!1);return HI.parts.body(n)})({body:s.body,initialData:s.initialData},n,o),i=lV(s.buttons),l=cV(i),c=Ce(0!==i.length,pV({buttons:i},n,o)),d=uV((()=>p),aV((()=>m),o.shared.providers,t),o.shared.getSink),u=(e=>{switch(e){case"large":return["tox-dialog--width-lg"];case"medium":return["tox-dialog--width-md"];default:return[]}})(s.size),m=iV({id:n,header:r,body:a,footer:c,extraClasses:u,extraBehaviours:[],extraStyles:{}},e,d,o),g={getId:x(n),getRoot:x(m),getBody:()=>HI.getBody(m),getFooter:()=>HI.getFooter(m),getFormWrapper:()=>{const e=HI.getBody(m);return wm.getCurrent(e).getOr(e)},toggleFullscreen:()=>{const e="tox-dialog--fullscreen",t=Ve(m.element.dom);Ua(t,e)?(Pa(t,e),Wa(t,u)):(ja(t,u),La(t,e))}},p=fV(g,t.redial,l);return{dialog:m,instanceApi:p}})({dataValidator:s,initialData:r,internalDialog:t},{redial:SN.redial,closeWindow:()=>{HI.hide(a.dialog),o(a.instanceApi)}},e.backstages.dialog);return HI.show(a.dialog),a.instanceApi.setData(r),a.instanceApi}),t),a=(n,s,r,a)=>SN.open(((n,i,l)=>{const c=SV(i,l),d=Ql(),u=e.backstages.popup.shared.header.isPositionedAtTop(),m=()=>d.on((e=>{Ph.reposition(e),$A.refresh(e)})),g=bV({dataValidator:l,initialData:c,internalDialog:n},{redial:SN.redial,closeWindow:()=>{d.on(Ph.hide),t.off("ResizeEditor",m),d.clear(),r(g.instanceApi)}},e.backstages.popup,a.ariaAttrs),p=ri(Ph.sketch({lazySink:e.backstages.popup.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:a.persistent?{event:"doNotDismissYet"}:{},...u?{}:{fireRepositionEventInstead:{}},inlineBehaviours:kl([Jp("window-manager-inline-events",[Ur(Cr(),((e,t)=>{Fr(g.dialog,bk)}))]),...CV(t,o,u)]),isExtraPart:(e,t)=>kV(t)}));return d.set(p),Ph.showWithinBounds(p,ai(g.dialog),{anchor:s},(()=>{const e=t.inline?xt():Ve(t.getContainer()),o=Jo(e);return A.some(o)})),o&&u||($A.refresh(p),t.on("ResizeEditor",m)),g.instanceApi.setData(c),Pp.focusIn(g.dialog),g.instanceApi}),n),i=(o,n,s,r)=>SN.open(((o,a,i)=>{const l=SV(a,i),c=Ql(),d=e.backstages.popup.shared.header.isPositionedAtTop(),u=()=>c.on((e=>{Ph.reposition(e),$A.refresh(e)})),m=bV({dataValidator:i,initialData:l,internalDialog:o},{redial:SN.redial,closeWindow:()=>{c.on(Ph.hide),t.off("ResizeEditor ScrollWindow ElementScroll",u),c.clear(),s(m.instanceApi)}},e.backstages.popup,r.ariaAttrs),g=ri(Ph.sketch({lazySink:e.backstages.popup.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:r.persistent?{event:"doNotDismissYet"}:{},...d?{}:{fireRepositionEventInstead:{}},inlineBehaviours:kl([Jp("window-manager-inline-events",[Ur(Cr(),((e,t)=>{Fr(m.dialog,bk)}))]),$A.config({contextual:{lazyContext:()=>A.some(Jo(Ve(t.getContentAreaContainer()))),fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top","bottom"],lazyViewport:e=>jS(t,e.element).map((e=>({bounds:GS(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:Xt(e.element).top})}))).getOrThunk((()=>({bounds:en(),optScrollEnv:A.none()})))})]),isExtraPart:(e,t)=>kV(t)}));return c.set(g),Ph.showWithinBounds(g,ai(m.dialog),{anchor:n},(()=>e.backstages.popup.shared.getSink().toOptional().bind((e=>{const o=jS(t,e.element).map((e=>GS(e))).getOr(en()),n=Jo(Ve(t.getContentAreaContainer())),s=Qo(n,o);return A.some(Ko(s.x,s.y,s.width,s.height-15))})))),$A.refresh(g),t.on("ResizeEditor ScrollWindow ElementScroll",u),m.instanceApi.setData(l),Pp.focusIn(m.dialog),m.instanceApi}),o);return{open:(t,o,n)=>{if(!u(o)){if("toolbar"===o.inline)return a(t,e.backstages.popup.shared.anchors.inlineDialog(),n,o);if("bottom"===o.inline)return i(t,e.backstages.popup.shared.anchors.inlineBottomDialog(),n,o);if("cursor"===o.inline)return a(t,e.backstages.popup.shared.anchors.cursor(),n,o)}return r(t,n)},openUrl:(o,n)=>((o,n)=>SN.openUrl((o=>{const s=wV(o,{closeWindow:()=>{HI.hide(s.dialog),n(s.instanceApi)}},t,e.backstages.dialog);return HI.show(s.dialog),s.instanceApi}),o))(o,n),alert:(e,t)=>{n.open(e,t)},close:e=>{e.close()},confirm:(e,t)=>{s.open(e,t)}}};tn.add("silver",(e=>{(e=>{ub(e),(e=>{const t=e.options.register,o=e=>f(e,r)?{value:Bw(e),valid:!0}:{valid:!1,message:"Must be an array of strings."},n=e=>h(e)&&e>0?{value:e,valid:!0}:{valid:!1,message:"Must be a positive number."};t("color_map",{processor:o,default:["#BFEDD2","Light Green","#FBEEB8","Light Yellow","#F8CAC6","Light Red","#ECCAFA","Light Purple","#C2E0F4","Light Blue","#2DC26B","Green","#F1C40F","Yellow","#E03E2D","Red","#B96AD9","Purple","#3598DB","Blue","#169179","Dark Turquoise","#E67E23","Orange","#BA372A","Dark Red","#843FA1","Dark Purple","#236FA1","Dark Blue","#ECF0F1","Light Gray","#CED4D9","Medium Gray","#95A5A6","Gray","#7E8C8D","Dark Gray","#34495E","Navy Blue","#000000","Black","#ffffff","White"]}),t("color_map_background",{processor:o}),t("color_map_foreground",{processor:o}),t("color_cols",{processor:n,default:Nw(e)}),t("color_cols_foreground",{processor:n,default:Vw(e,Mw)}),t("color_cols_background",{processor:n,default:Vw(e,Dw)}),t("custom_colors",{processor:"boolean",default:!0}),t("color_default_foreground",{processor:"string",default:Iw}),t("color_default_background",{processor:"string",default:Iw})})(e),(e=>{const t=e.options.register;t("contextmenu_avoid_overlap",{processor:"string",default:""}),t("contextmenu_never_use_native",{processor:"boolean",default:!1}),t("contextmenu",{processor:e=>!1===e?{value:[],valid:!0}:r(e)||f(e,r)?{value:vF(e),valid:!0}:{valid:!1,message:"Must be false or a string."},default:"link linkchecker image editimage table spellchecker configurepermanentpen"})})(e)})(e);let t=()=>en();const{dialogs:o,popups:n,renderUI:s}=RI(e,{getPopupSinkBounds:()=>t()});LS(e,n.backstage.shared);const a=OV({editor:e,backstages:{popup:n.backstage,dialog:o.backstage}});return{renderUI:()=>{const o=s();return jS(e,n.getMothership().element).each((e=>{t=()=>GS(e)})),o},getWindowManagerImpl:x(a),getNotificationManagerImpl:()=>((e,t,o)=>{const n=t.backstage.shared,s=()=>{const t=Jo(Ve(e.getContentAreaContainer())),o=en(),n=Yi(o.x,t.x,t.right),s=Yi(o.y,t.y,t.bottom),r=Math.max(t.right,o.right),a=Math.max(t.bottom,o.bottom);return A.some(Ko(n,s,r-n,a-s))};return{open:(t,r)=>{const a=()=>{r(),Ph.hide(l)},i=ri(nb.sketch({text:t.text,level:R(["success","error","warning","warn","info"],t.type)?t.type:void 0,progress:!0===t.progressBar,icon:t.icon,closeButton:t.closeButton,onAction:a,iconProvider:n.providers.icons,translationProvider:n.providers.translate})),l=ri(Ph.sketch({dom:{tag:"div",classes:["tox-notifications-container"]},lazySink:n.getSink,fireDismissalEventInstead:{},...n.header.isPositionedAtTop()?{}:{fireRepositionEventInstead:{}}}));o.add(l),h(t.timeout)&&t.timeout>0&&Uh.setEditorTimeout(e,(()=>{a()}),t.timeout);const c={close:a,reposition:()=>{const t=ai(i),o={maxHeightFunction:cc()},r=e.notificationManager.getNotifications();if(r[0]===c){const e={...n.anchors.banner(),overrides:o};Ph.showWithinBounds(l,t,{anchor:e},s)}else I(r,c).each((e=>{const n=r[e-1].getEl(),a={type:"node",root:xt(),node:A.some(Ve(n)),overrides:o,layouts:{onRtl:()=>[cl],onLtr:()=>[cl]}};Ph.showWithinBounds(l,t,{anchor:a},s)}))},text:e=>{nb.updateText(i,e)},settings:t,getEl:()=>i.element.dom,progressBar:{value:e=>{nb.updateProgress(i,e)}}};return c},close:e=>{e.close()},getArgs:e=>e.settings}})(e,{backstage:n.backstage},n.getMothership())}}))}();