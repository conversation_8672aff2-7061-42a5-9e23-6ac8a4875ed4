.modal {
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    position: fixed;
    top: 0;
    left: 0;
    display: none;
    z-index: 999999;
    align-items: center;
    justify-content: center;
}

.modal .container {
    display: flex;
    flex-flow: column;
    background-color: #ffffff;
    box-shadow: 0px 0px 5px 2px rgba(0, 0, 0, 0.03);
}

.modal .container.zoom {
    transform: scale(0.5);
}

.modal .container.slide {
    transform: translateY(-1000px);
}

.modal .container .header {
    display: flex;
    padding: 20px;
    font-weight: 600;
    justify-content: space-between;
    border-bottom: 1px solid #e6e9ec;
    align-items: center;
}

.modal .container .header h1 {
    font-weight: 500;
    color: #4d4d4d;
    padding: 0;
    margin: 0;
}

.modal .container .header button {
    appearance: none;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 24px;
    line-height: 24px;
    padding-bottom: 4px;
    cursor: pointer;
    color: #9a9a9a;
}

.modal .container .header button:hover {
    color: #676767;
}

.modal .container .content {
    padding: 20px 15px;
    flex: 1;
}

.modal .container .content .prompt-input input {
    padding: 5px;
    /*border-radius: 4px;*/
    border: 1px solid #e6e9ec;
    background-repeat: no-repeat;
    margin-top: 15px;
    width: 95%;
}

.modal .container .footer {
    border-top: 1px solid #e6e9ec;
    background-color: #f9fafc;
    padding: 10px;
    display: flex;
}

.modal .container .footer button {
    display: inline-flex;
    appearance: none;
    margin-right: 7px;
    cursor: pointer;
    background: #666 url(../images/ui/button_light.png) repeat-x;
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0, #ccc), color-stop(1, #666));
    background: -moz-linear-gradient(top center, #ccc 0%, #666 100%);
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    border: 1px solid;
    border-color: #666 #999 #fff;
    color: #fff;
    display: inline-block;
    font-size: 12px;
    line-height: 27px;
    overflow: hidden;
    padding: 0 10px;
    text-decoration: none;
    text-shadow: rgba(0, 0, 0, 0.5) 0 -1px 0;
}

.modal .container .footer button:last-child {
    margin-right: 0;
}

.modal .container .footer button:hover {
    background-color: #326fd3;
}

.modal .container .footer button.alt {
    font-weight: 500;
    background-color: #dadbdd;
    /*color: #606266;*/
}

.modal .container .footer button.alt:hover {
    background-color: #d5d6d8;
}

.modal .container .footer button.right {
    margin-left: auto;
}

.modal.small .container {
    width: 400px;
}

.modal.medium .container {
    width: 600px;
}

.modal.large .container {
    width: 900px;
}

.modal.full .container {
    width: 100%;
    height: 100%;
}

.modal.open {
    display: flex;
}

.modal.open .container.zoom {
    transform: scale(1);
    transition: all 0.2s ease;
}

.modal.open .container.slide {
    transform: translateY(0);
    transition: all 0.5s ease;
}