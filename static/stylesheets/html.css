/* `HTML5 Reset
----------------------------------------------------------------------------------------------------*/

a,
abbr,
address,
article,
aside,
audio,
b,
blockquote,
body,
caption,
cite,
code,
dd,
del,
dfn,
dialog,
div,
dl,
dt,
em,
fieldset,
figure,
footer,
form,
h1,
h2,
h3,
h4,
h5,
h6,
header,
hgroup,
hr,
html,
i,
iframe,
img,
ins,
kbd,
label,
legend,
li,
mark,
menu,
nav,
object,
ol,
p,
pre,
q,
samp,
section,
small,
span,
strong,
sub,
sup,
table,
tbody,
td,
tfoot,
th,
thead,
time,
tr,
ul,
var,
video {
	border: 0;
	margin: 0;
	outline: 0;
	padding: 0;
	font-size: 100%;
}

html,
body {
	height: 100%;
}

b,
strong {
/*
	Makes browsers agree.
	IE + Opera = font-weight: bold.
	Gecko + WebKit = font-weight: bolder.
*/
	font-weight: bold;
}

img {
	font-size: 0;
}

table {
	border-collapse: collapse;
	border-spacing: 0;
}

th,
td,
caption {
	font-weight: normal;
	vertical-align: top;
	text-align: left;
}