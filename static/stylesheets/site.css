/* `Basic HTML
----------------------------------------------------------------------------------------------------*/

* {
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

html,
body {
	overflow: hidden;
}

body {
	/*
		Pre-load #nav_arrow image.
	*/
	background: #000 url(../images/ui/nav_arrow.png) no-repeat -99999px -99999px;
	color: #000;
	cursor: default;
}

a {
	color: #06c;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	font-weight: normal;
	line-height: 1;
}

ol ol,
ul ul,
ol ul,
ul ol {
	margin-bottom: 0;
}

/* `Misc
----------------------------------------------------------------------------------------------------*/

.abs {
	position: absolute;
	top: auto;
	left: auto;
	right: auto;
	bottom: auto;
}

.cancel {
	color: #c00;
}

.actions {
	text-decoration: none;
	color: rgb(0, 0, 0);
}

.float_right {
	float: right;
}

.float_left {
	float: left;
}

/*
	Added via JS to <textarea> and class="input_full"
*/
.input_full_wrap {
	display: block;
	padding-right: 8px;
}

.gutter_left {
	padding-left: 20px;
}

.gutter_right {
	padding-right: 20px;
}

/* `Data Table
----------------------------------------------------------------------------------------------------*/

table.data {
	border: 1px solid #ddd;
	width: 100%;
	font-size: 10pt;
	border-radius: 10px;
}

table.data th {
	padding: 5px 10px;
	font-weight: bolder;
	font-size: 10pt;
}

table.data td {
	padding: 5px 10px;
	font-size: 10pt;
}

table.data thead th {
	background: #eee url(../images/ui/data_th.png) repeat-x;
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0, #fff), color-stop(1, #eee));
	background: -moz-linear-gradient(top center, #fff 0%, #eee 100%);
	border-bottom: 1px solid #ddd;
	text-shadow: #fff 0 1px 0;
}

table.data tbody > tr:nth-child(even) {
	background: #edf3fe;
}

table.data th:first-child {
	-moz-border-radius: 5px 0 0 0;
}
table.data th:last-child {
	-moz-border-radius: 0 5px 0 0;
}
table.data tr:last-child td:first-child {
	-moz-border-radius: 0 0 0 5px;
}
table.data tr:last-child td:last-child {
	-moz-border-radius: 0 0 5px 0;
}

/* `Horizontal Table
----------------------------------------------------------------------------------------------------*/

table.horiz td {
	padding: 20px 0 0 20px;
}

table.horiz tr:first-child td {
	padding-top: 0;
}

table.horiz tr td:first-child {
	padding-left: 0;
}

/* `Buttons
----------------------------------------------------------------------------------------------------*/

.button {
	background: #666 url(../images/ui/button_light.png) repeat-x;
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0, #ccc), color-stop(1, #666));
	background: -moz-linear-gradient(top center, #ccc 0%, #666 100%);
	-moz-border-radius: 5px;
	-webkit-border-radius: 5px;
	border-radius: 5px;
	border: 1px solid;
	border-color: #666 #999 #fff;
	color: #fff;
	display: inline-block;
	font-size: 12px;
	line-height: 27px;
	margin: 8px 0 0;
	overflow: hidden;
	padding: 0 10px;
	text-decoration: none;
	text-shadow: rgba(0, 0, 0, 0.5) 0 -1px 0;
	height: 28px;
}

.button:active,
.button_active {
	background: #333 url(../images/ui/button_light_active.png) repeat-x;
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0, #999), color-stop(1, #333));
	background: -moz-linear-gradient(top center, #999 0%, #333 100%);
}

.button.float_right {
	margin-right: 7px;
}

.button.float_left {
	margin-left: 7px;
}

/* `Chrome
----------------------------------------------------------------------------------------------------*/

.chrome_light {
	background: #bbb url(../images/ui/chrome_light.png) repeat-x;
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0, #fff), color-stop(1, #bbb));
	background: -moz-linear-gradient(top center, #fff 0%, #bbb 100%);
	color: #777;
	text-shadow: #fff 0 1px 0;
}

.chrome_dark {
	background: #333 url(../images/ui/chrome_dark.png) repeat-x;
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0, #666), color-stop(1, #333));
	background: -moz-linear-gradient(top center, #666 0%, #333 100%);
}

.chrome_dark .button {
	background: #222 url(../images/ui/button_dark.png) repeat-x;
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0, #999), color-stop(1, #222));
	background: -moz-linear-gradient(top center, #999 0%, #222 100%);
	border-color: #000 #333 #666;
}

.chrome_dark .button:active,
.chrome_dark .button_active {
	background: #000 url(../images/ui/button_dark_active.png) repeat-x;
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0, #666), color-stop(1, #000));
	background: -moz-linear-gradient(top center, #666 0%, #000 100%);
}

.chrome_dark,
.chrome_dark a {
	color: #fff;
	text-shadow: #000 0 -1px 0;
}

/* `Search
----------------------------------------------------------------------------------------------------*/

#q {
	vertical-align: baseline;
	width: 220px;
}

/* `Headers + Footers
----------------------------------------------------------------------------------------------------*/

.header_upper,
.header_lower,
.footer_upper,
.footer_lower {
	line-height: 45px;
	overflow: hidden;
	text-align: center;
	left: 0;
	right: 0;
	height: 45px;
}

.header_upper,
.header_lower {
	border-bottom: 1px solid #000;
}

.footer_upper,
.footer_lower {
	border-top: 1px solid #000;
}

.header_upper {
	font-size: 20px;
	top: 0;
}

.header_lower {
	top: 46px;
}

.footer_upper {
	bottom: 46px;
}

.footer_lower {
	bottom: 0;
}

/* `Main
----------------------------------------------------------------------------------------------------*/

#main {
	top: 0;
	left: 251px;
	right: 0;
	bottom: 0;
	min-width: 312px;
}

#main_content {
	background: #fff;
	left: 0;
	right: 0;
}

#main_content_inner {
	/*
		1px bottom padding = fixes Opera bug.
	*/
	padding: 20px 20px 1px;
}

/* `Sidebar
----------------------------------------------------------------------------------------------------*/

#sidebar {
	top: 0;
	left: 0;
	bottom: 0;
	width: 250px;
}

#sidebar_content {
	background: #f5f5f5;
	left: 0;
	right: 0;
}

#sidebar_menu,
#sidebar_menu li {
	font-weight: bold;
	list-style: none;
	margin: 0;
}

#sidebar_menu a {
	border-bottom: 1px solid #ddd;
	color: #333;
	display: block;
	padding: 10px 20px 10px 30px;
	position: relative;
	text-decoration: none;
	/*
		Work around for iScroll.js
		Forces <a> without href="#"
		to retain the hover state.
	*/
	cursor: pointer;
}

#sidebar_menu .active > a {
	background: #035de7 url(../images/ui/menu_active_a.png) repeat-x;
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0, #058cf7), color-stop(1, #035de7));
	background: -moz-linear-gradient(top center, #058cf7 0%, #035de7 100%);
	color: #fff;
}

#sidebar_menu ul {
	display: none;
}

#sidebar_menu .expanded > a {
	background: #eee url(../images/ui/menu_expanded.png) repeat-x left bottom;
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0, #eee), color-stop(1, #fff));
	background: -moz-linear-gradient(top center, #eee 0%, #fff 100%);
	color: #ccc;
	text-shadow: #fff 0 1px 0;
}

#sidebar_menu_home .abs,
#sidebar_menu .toggle_arrow {
	font-size: 0;
	overflow: hidden;
	left: 10px;
	width: 13px;
	height: 14px;
}

#sidebar_menu_home .abs {
	background: url(../images/ui/menu_home.png) no-repeat;
	top: 12px;
}

#sidebar_menu .toggle_arrow {
	background: url(../images/ui/toggle_arrow.png) no-repeat;
	top: 13px;
}

#sidebar_menu_home.active .abs,
#sidebar_menu .expanded > a .toggle_arrow {
	background-position: 0 -14px;
}

/* `Hide Sidebar
----------------------------------------------------------------------------------------------------*/

.is_portrait #main {
	left: 0;
}

#button_navigation {
	display: none;
}

.is_portrait #button_navigation {
	display: block;
}

#nav_arrow,
.is_portrait #sidebar,
.is_portrait #sidebar .header_upper {
	display: none;
}

.is_portrait #sidebar {
	-moz-box-shadow: rgba(0, 0, 0, 0.5) 0 2px 5px;
	-webkit-box-shadow: rgba(0, 0, 0, 0.5) 0 2px 5px;
	box-shadow: rgba(0, 0, 0, 0.5) 0 2px 5px;
	background: #000;
	top: 45px;
	left: 8px;
	bottom: auto;
	padding: 1px;
}

.is_portrait #sidebar_search_form {
	margin: 0 0 1px;
}

.is_portrait #sidebar > * {
	position: static !important;
}

html .is_portrait #sidebar > #nav_arrow {
	position: absolute !important;
	background: url(../images/ui/nav_arrow.png) no-repeat;
	display: block;
	top: -6px;
	left: 33px;
	right: auto;
	bottom: auto;
	width: 11px;
	height: 6px;
}