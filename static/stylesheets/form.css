/*
	Form Elements
	-- Styled to look like native Safari on OS X.
	-- Drop-down <select> menus are unaffected.
	-- Buttons are unaffected. Native OS style.
*/

/* `UI Consistency
----------------------------------------------------------------------------------------------------*/

::-webkit-input-placeholder {
	color: #ccc;
}

/*
	Separate rule for Firefox.
	Cannot stack with WebKit's.
*/
.placeholder_text {
	color: #ccc !important;
}

input[type=search] {
	-webkit-appearance: none;
	-webkit-box-sizing: content-box;
}

input[type=search]::-webkit-search-decoration {
	display: none;
}

input,
button,
select,
textarea {
	margin: 0;
}

.ie_button,
button,
input[type=reset],
input[type=submit],
input[type=button] {
	display: inline-block;
	font-size: 11px;
	overflow: visible;
	width: auto;
}

.ie_text,
input[type=date],
input[type=datetime],
input[type=datetime-local],
input[type=email],
input[type=month],
input[type=number],
input[type=password],
input[type=range],
input[type=search],
input[type=tel],
input[type=text],
input[type=time],
input[type=url],
input[type=week] {
	height: 16px;
}

.ie_text,
textarea,
select[multiple],
input[type=date],
input[type=datetime],
input[type=datetime-local],
input[type=email],
input[type=month],
input[type=number],
input[type=password],
input[type=search],
input[type=tel],
input[type=text],
input[type=time],
input[type=url],
input[type=week] {
	background: #fff url(../images/ui/input_shadow.png) repeat-x;
	border: 1px solid;
	border-color: #848484 #c1c1c1 #e1e1e1;
	color: #000;
	margin: 0;
	padding-top: 2px;
	padding-left: 3px;
	padding-right: 3px;
	font: 13px Arial, sans-serif;
	vertical-align: top;
}

.ie_multiple,
select[multiple] {
	padding: 0;
}

textarea {
	max-width: 100%;
	min-width: 100%;
	width: 100%;
}

optgroup {
	color: #000;
	font-style: normal;
	font-weight: normal;
}

/* `Widths
----------------------------------------------------------------------------------------------------*/

.input_tiny {
	width: 50px;
}

.input_small {
	width: 100px;
}

.input_medium {
	width: 150px;
}

.input_large {
	width: 200px;
}

.input_xlarge {
	width: 250px;
}

.input_full {
	width: 100%;
}

/*
	Added via JS to <textarea> and class="input_full"
*/
.input_full_wrap {
	display: block;
	padding-right: 8px;
}

.switch {
	position: relative;
	display: inline-block;
	width: 30px;
	height: 17px;
}

.switch input { 
	opacity: 0;
	width: 0;
	height: 0;
}

.slider {
	position: absolute;
	cursor: pointer;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #ccc;
	-webkit-transition: .4s;
	transition: .4s;
}

.slider:before {
	position: absolute;
	content: "";
	height: 13px;
	width: 13px;
	left: 2px;
	bottom: 2px;
	background-color: white;
	-webkit-transition: .4s;
	transition: .4s;
}

input:checked + .slider {
	background-color: #2196F3;
}

input:focus + .slider {
	box-shadow: 0 0 1px #2196F3;
}

input:checked + .slider:before {
	-webkit-transform: translateX(13px);
	-ms-transform: translateX(13px);
	transform: translateX(13px);
}

/* Rounded sliders */
.slider.round {
	border-radius: 34px;
}

.slider.round:before {
	border-radius: 50%;
}
