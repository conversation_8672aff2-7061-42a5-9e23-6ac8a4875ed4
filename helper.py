import sqlite3
import pandas as pd

def db_connect(db_file):
    conn = None
    try:
        conn = sqlite3.connect(db_file)
    except sqlite3.Error as e:
        print(e)
    return conn

def build_menu(location, role_id):
    topmenu_items = get_menu(role_id)
    menu = ""
    menu_target = ""
    # cnxn = db_connect()
    messagetotal = "0"
    for index, topmenu_item in topmenu_items.iterrows():
        submenu_items = get_submenu(topmenu_item["menu_id"], role_id)
        if not submenu_items.empty:
            menu = menu + '\t\t\t\t\t<li><a href="#">' + topmenu_item["menu_value"] + "</a>\n\t\t\t\t\t\t<ul>\n"
            for index, submenu_item in submenu_items.iterrows():
                if submenu_item["menu_target"] != None:
                    menu_target = 'target="' + submenu_item["menu_target"] + '"'
                else:
                    menu_target = ""
                if str(submenu_item["menu_value"]).lower() == location:
                    menu = (
                        menu
                        + '\t\t\t\t\t\t\t<li class="active"><a '
                        + menu_target
                        + 'href="'
                        + submenu_item["menu_url"]
                        + '"><span class="abs"></span>'
                        + submenu_item["menu_value"]
                        + "</a></li>\n"
                    )
                else:
                    menu = (
                        menu
                        + "\t\t\t\t\t\t\t<li><a "
                        + menu_target
                        + 'href="'
                        + submenu_item["menu_url"]
                        + '"><span class="abs"></span>'
                        + submenu_item["menu_value"]
                        + "</a></li>\n"
                    )
            menu = menu + "\t\t\t\t\t\t</ul>\n\t\t\t\t\t</li>\n"
        else:
            if topmenu_item["menu_target"] != None:
                menu_target = 'target="' + topmenu_item["menu_target"] + '"'
            else:
                menu_target = ""
            if str(topmenu_item["menu_value"]).lower() == location:
                if topmenu_item["menu_value"] == "Home":
                    menu = (
                        menu
                        + '<li class="active" id="sidebar_menu_home"><a '
                        + menu_target
                        + 'href="'
                        + topmenu_item["menu_url"]
                        + '"><span class="abs"></span>'
                        + topmenu_item["menu_value"]
                        + "</a></li>\n"
                    )
                else:
                    if topmenu_item["menu_value"] == "Messages":
                        menu_title = "Messages (" + str(messagetotal) + ")"
                    else:
                        menu_title = topmenu_item["menu_value"]
                    menu = (
                        menu
                        + '<li class="active">\n<a '
                        + menu_target
                        + 'href="'
                        + topmenu_item["menu_url"]
                        + '"><span class="abs"></span>'
                        + menu_title
                        + "</a></li>\n"
                    )
            else:
                if topmenu_item["menu_value"] == "Home":
                    menu = (
                        menu
                        + '<li id="sidebar_menu_home"><a '
                        + menu_target
                        + 'href="'
                        + topmenu_item["menu_url"]
                        + '"><span class="abs"></span>'
                        + topmenu_item["menu_value"]
                        + "</a></li>\n"
                    )
                else:
                    if topmenu_item["menu_value"] == "Messages":
                        menu_title = "Messages (" + str(messagetotal) + ")"
                    else:
                        menu_title = topmenu_item["menu_value"]
                    menu = (
                        menu
                        + "\t\t\t\t\t<li><a "
                        + menu_target
                        + 'href="'
                        + topmenu_item["menu_url"]
                        + '"><span class="abs"></span>'
                        + menu_title
                        + "</a></li>\n"
                    )
    return menu

def get_menu(role_id):
    cnxn = db_connect('databases/system.db')
    menu = pd.read_sql_query(
        "select * from menu where menu_parent is null order by menu_order",
        cnxn,
    )
    return menu

def get_submenu(id, role_id):
    cnxn = db_connect('databases/system.db')
    menu = pd.read_sql_query(
        "select * from menu where menu_parent = '" + id + "' order by menu_order",
        cnxn,
    )
    return menu

def get_role_id(user_email):
    sql = "select role_id from users where user_name = '" + user_email + "'"
    cnxn = db_connect('databases/system.db')
    user_info = pd.read_sql_query(sql, cnxn)
    for key, value in user_info.iterrows():
        value = value["role_id"]
    return str(value)