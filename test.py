#!/usr/bin/env python3
import sqlite3
import pandas as pd

def db_connect(db_file):
    conn = None
    try:
        conn = sqlite3.connect(db_file)
    except sqlite3.Error as e:
        print(e)
    return conn

def get_menu(role_id):
    cnxn = db_connect('databases/system.db')
    
    menu = pd.read_sql_query(
        "select * from menu where menu_parent is null order by menu_order",
        cnxn,
    )
    #update below values with role defination.
    id_list = '1D72383E-C095-43AF-B1E6-2995DE227C9E,1276EF37-BCFA-45A5-9253-1C04E4C64A1C,D130F58F-1E3F-4274-9CDB-DA85B04A35B1'
    id_list = id_list.split(',')
    menu = menu[menu['menu_id'].isin(id_list)]
    #menu = menu.query('menu_id in @id_list')
    return menu

string = '1D72383E-C095-43AF-B1E6-2995DE227C9E,1276EF37-BCFA-45A5-9253-1C04E4C64A1C'
# print(check('xx1', string))

print(get_menu(string))

